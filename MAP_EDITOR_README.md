# <PERSON><PERSON><PERSON>'s Revenge - Map Editor

A graphical map editor for creating and editing levels in <PERSON><PERSON><PERSON>'s Revenge. The editor integrates seamlessly with the existing game architecture and provides an intuitive interface for map creation.

## Features

### Core Functionality
- **Visual Map Editing**: Drag-and-drop tile and entity placement
- **Zoom and Pan**: Mouse wheel zoom and middle-click pan navigation
- **Grid Display**: Toggle-able grid overlay for precise placement
- **Asset Browser**: Browse all available tiles and entities with visual previews
- **Tool System**: Multiple editing tools (Selection, Paint Brush)
- **File Management**: Create, open, save, and save-as operations

### User Interface
- **Menu Bar**: File operations and editor commands
- **Toolbar**: Quick tool selection with keyboard shortcuts
- **Asset Panel**: Categorized asset browser with search functionality
- **Map Canvas**: Main editing area with coordinate tracking
- **Status Bar**: Real-time information about tools, coordinates, and map status

### Integration
- **Asset System**: Uses existing AssetManager and procedural asset generation
- **Map Format**: Full support for .map file format with YAML headers and ASCII bodies
- **Base Legend**: Integrates with base_legend.yaml for consistent asset definitions
- **Configuration**: Respects game_config.yaml settings for rendering and behavior

## Getting Started

### Launch the Editor
```bash
./run.sh editor
```

### Basic Controls

#### Navigation
- **Mouse Wheel**: Zoom in/out
- **Middle Mouse + Drag**: Pan the map
- **Home Key**: Reset zoom and center view
- **G Key**: Toggle grid display

#### Tools
- **S Key**: Switch to Selection tool
- **P Key**: Switch to Paint Brush tool
- **Left Click**: Use current tool (paint/select)
- **Right Click**: Erase (place default floor tile)
- **Escape**: Exit editor or close dialogs

#### File Operations
- **File > New Map**: Create a new map with custom dimensions
- **File > Open Map**: Load an existing map file
- **File > Save Map**: Save current map
- **File > Save As**: Save map with new name/location

## Map File Format

The editor supports the complete .map file format used by the game:

```yaml
# YAML Header
legend:
  # Level-specific symbol overrides
  'H': { category: entity, type: building, data_id: "house" }

placements:
  # Unique entities with special properties
  - char: 'g'
    position: { x: 15, y: 8 }
    overrides: { name: "Boss Goblin", max_hp: 100 }

metadata:
  name: "Level Name"
  description: "Level description"
  background_music: "theme"
  
---
# ASCII Body (visual grid)
######################
#..................H.#
#..@...............#.#
######################
```

## Available Assets

### Tiles
- `#` - Stone Wall
- `.` - Dirt Floor  
- `~` - Deep Water
- `^` - Rock Mountain
- `T` - Oak Tree
- `=` - Wooden Bridge
- `+` - Wooden Door

### Entities
- `@` - Player Start Position
- `g` - Goblin Grunt
- `G` - Goblin Shaman
- `d` - Green Drake
- `s` - Sand Scorpion
- `!` - Health Potion
- `$` - Gold Coin
- `%` - Treasure Chest
- `k` - Ancient Key
- `C` - Chest (Container)
- `i` - Generic Item
- `m` - Generic Monster

## Workflow

### Creating a New Map
1. **File > New Map** to open the creation dialog
2. Enter map name and dimensions (5-100 tiles)
3. Click **OK** to create the map
4. Use the Paint Brush tool to place tiles and entities
5. **File > Save As** to save your map

### Editing Existing Maps
1. **File > Open Map** to load an existing map
2. Select assets from the Asset Panel
3. Use tools to modify the map
4. **File > Save** to save changes

### Asset Placement
1. Select an asset from the Asset Panel (left side)
2. Choose the Paint Brush tool (P key)
3. Left-click to place the selected asset
4. Right-click to erase (place default floor)
5. Hold and drag for continuous painting

## Technical Details

### Architecture
- **Editor Engine**: Main coordinator managing all systems
- **Tool System**: Pluggable tool architecture for different editing modes
- **Map Operations**: High-level operations for map manipulation
- **UI Components**: Modular interface components
- **Asset Integration**: Seamless integration with game asset system

### File Structure
```
src/editor/
├── main.py              # Editor entry point
├── editor_engine.py     # Main editor class
├── map_operations.py    # Map file operations
├── tool_manager.py      # Tool coordination
├── tools/               # Editing tools
│   ├── base_tool.py     # Abstract tool class
│   ├── paint_tool.py    # Paint brush tool
│   └── selection_tool.py # Selection tool
└── ui/                  # User interface
    ├── asset_panel.py   # Asset browser
    ├── canvas.py        # Map editing canvas
    ├── menu_bar.py      # File menu
    ├── toolbar.py       # Tool selection
    ├── status_bar.py    # Status information
    └── dialogs.py       # Dialog boxes
```

### Configuration
The editor respects settings from `game_config.yaml`:
- **tile_size**: Base tile size for rendering
- **screen_width/height**: Editor window dimensions
- **target_fps**: Frame rate limit
- **rendering**: Various rendering settings

## Troubleshooting

### Common Issues
- **Assets not loading**: Check that base_legend.yaml is present and valid
- **Save failures**: Ensure write permissions in the target directory
- **Performance issues**: Reduce zoom level or map size for better performance

### Debug Mode
Enable debug logging by setting `verbose_logging: true` in game_config.yaml.

## Future Enhancements

Potential improvements for future versions:
- **Undo/Redo**: Edit history management
- **Copy/Paste**: Selection copying and pasting
- **Layer Support**: Separate tile and entity layers
- **Advanced Tools**: Line tool, fill tool, rectangle tool
- **Map Properties**: Enhanced metadata editing
- **Validation**: Real-time map validation feedback
- **Performance**: Optimizations for large maps

## Contributing

The map editor follows the same architecture patterns as the main game:
- Clean separation of concerns
- Dependency injection
- Event-driven architecture
- Modular UI components

When adding new features, maintain consistency with existing patterns and ensure proper integration with the game systems.
