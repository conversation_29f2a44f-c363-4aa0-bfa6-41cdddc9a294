[project]
name = "treebeards-revenge"
version = "0.1.0"
description = "A clean architecture RPG game"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "pygame>=2.5.0",
    "pydantic>=2.0.0",
    "pyyaml>=6.0",
    "pytest>=7.0.0",
    "mypy>=1.0.0",
    "pytest-cov>=4.0.0",
    "numpy>=2.3.1",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src"]

[tool.mypy]
strict = true
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "--cov=src --cov-report=html --cov-report=term-missing"

[tool.coverage.run]
source = ["src"]
omit = ["*/tests/*"]
