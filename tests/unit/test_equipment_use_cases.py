"""
Unit tests for equipment and inventory use cases.
"""

import pytest
from unittest.mock import Mock

from src.application.use_cases import (
    CreateNewPlayerUseCase, EquipItemUseCase, UnequipItemUseCase,
    AddItemToInventoryUseCase, RemoveItemFromInventoryUseCase,
    GetInventoryUseCase, CanAddItemToInventoryUseCase, GetPlayerVisualDataUseCase
)
from src.application.interfaces import GameStateData
from src.game_core.entities import Player, Position, Stats
from src.game_core.config import get_config


class TestEquipmentUseCases:
    """Test equipment management use cases."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.event_bus = Mock()
        self.player = Player(
            id="test_player",
            name="Test Hero",
            position=Position(100, 100),
            asset_id="player.hero",
            stats=Stats(hp=100, max_hp=100, mp=50, max_mp=50, strength=10, defense=5, speed=8),
            size=(32, 32),
            level=1,
            experience=0,
            inventory={"iron_sword": 1, "leather_armor": 1}
        )
        self.game_state = GameStateData(
            player=self.player,
            monsters={},
            items={},
            current_level_id="test",
            collision_map=[[False]],
            level_tiles=[[]]
        )
    
    def test_equip_item_use_case(self):
        """Test equipping an item."""
        use_case = EquipItemUseCase(self.event_bus)

        # Equip iron sword
        result = use_case.execute(self.game_state, "iron_sword", "main_hand_weapon")

        assert result.player.main_hand_weapon == "iron_sword"
        assert "iron_sword" not in result.player.inventory
    
    def test_equip_item_not_in_inventory(self):
        """Test equipping an item not in inventory."""
        use_case = EquipItemUseCase(self.event_bus)

        # Try to equip item not in inventory - should raise exception
        with pytest.raises(ValueError, match="Unknown item"):
            use_case.execute(self.game_state, "nonexistent_item", "main_hand_weapon")
    
    def test_equip_item_with_replacement(self):
        """Test equipping an item when slot is already occupied."""
        # First equip rusty sword
        player_with_rusty = Player(
            id=self.player.id,
            name=self.player.name,
            position=self.player.position,
            asset_id=self.player.asset_id,
            stats=self.player.stats,
            size=(32, 32),
            level=self.player.level,
            experience=self.player.experience,
            main_hand_weapon="rusty_sword",
            inventory={"iron_sword": 1}
        )
        game_state_with_rusty = GameStateData(
            player=player_with_rusty,
            monsters={},
            items={},
            current_level_id="test",
            collision_map=[[False]],
            level_tiles=[[]]
        )
        
        use_case = EquipItemUseCase(self.event_bus)
        result = use_case.execute(game_state_with_rusty, "iron_sword", "main_hand_weapon")
        
        # Iron sword should be equipped, rusty sword should be in inventory
        assert result.player.main_hand_weapon == "iron_sword"
        assert result.player.has_item("rusty_sword", 1)
        assert not result.player.has_item("iron_sword", 1)
    
    def test_unequip_item_use_case(self):
        """Test unequipping an item."""
        # Start with equipped item
        player_with_equipment = Player(
            id=self.player.id,
            name=self.player.name,
            position=self.player.position,
            asset_id=self.player.asset_id,
            stats=self.player.stats,
            size=(32, 32),
            level=self.player.level,
            experience=self.player.experience,
            main_hand_weapon="iron_sword",
            inventory={}
        )
        game_state_with_equipment = GameStateData(
            player=player_with_equipment,
            monsters={},
            items={},
            current_level_id="test",
            collision_map=[[False]],
            level_tiles=[[]]
        )
        
        use_case = UnequipItemUseCase(self.event_bus)
        result = use_case.execute(game_state_with_equipment, "main_hand_weapon")

        assert result.player.main_hand_weapon is None
        assert result.player.has_item("iron_sword", 1)
    
    def test_unequip_empty_slot(self):
        """Test unequipping from an empty slot."""
        use_case = UnequipItemUseCase(self.event_bus)

        # Should raise exception for empty slot
        with pytest.raises(ValueError, match="No equipment in slot"):
            use_case.execute(self.game_state, "main_hand_weapon")


class TestInventoryUseCases:
    """Test inventory management use cases."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.event_bus = Mock()
        self.player = Player(
            id="test_player",
            name="Test Hero",
            position=Position(100, 100),
            asset_id="player.hero",
            stats=Stats(hp=100, max_hp=100, mp=50, max_mp=50, strength=10, defense=5, speed=8),
            size=(32, 32),
            level=1,
            experience=0,
            inventory={"bread": 3, "health_potion": 2}
        )
        self.game_state = GameStateData(
            player=self.player,
            monsters={},
            items={},
            current_level_id="test",
            collision_map=[[False]],
            level_tiles=[[]]
        )
    
    def test_add_item_to_inventory_use_case(self):
        """Test adding items to inventory."""
        use_case = AddItemToInventoryUseCase(self.event_bus)
        result = use_case.execute(self.game_state, "iron_sword", 1)
        
        assert result.player.has_item("iron_sword", 1)
        assert result.player.has_item("bread", 3)  # Existing items preserved
    
    def test_remove_item_from_inventory_use_case(self):
        """Test removing items from inventory."""
        use_case = RemoveItemFromInventoryUseCase(self.event_bus)
        result = use_case.execute(self.game_state, "bread", 2)
        
        assert result.player.inventory["bread"] == 1
        assert result.player.has_item("health_potion", 2)  # Other items preserved
    
    def test_get_inventory_use_case(self):
        """Test getting inventory information."""
        use_case = GetInventoryUseCase(self.event_bus)
        result = use_case.execute(self.game_state)
        
        assert result["inventory"]["bread"] == 3
        assert result["inventory"]["health_potion"] == 2
        assert result["space_used"] == 2
        config = get_config()
        max_slots = config.inventory_ui.inventory_max_slots
        assert result["space_remaining"] == max_slots - 2
    
    def test_can_add_item_to_inventory_use_case(self):
        """Test checking if items can be added to inventory."""
        use_case = CanAddItemToInventoryUseCase(self.event_bus)
        
        # Can add to existing item
        assert use_case.execute(self.game_state, "bread", 5) == True
        
        # Can add new item (space available)
        assert use_case.execute(self.game_state, "new_item", 1) == True
    
    def test_can_add_item_inventory_full(self):
        """Test checking inventory space when full."""
        # Create player with full inventory
        full_inventory = {f"item_{i}": 1 for i in range(20)}
        player_full = Player(
            id=self.player.id,
            name=self.player.name,
            position=self.player.position,
            asset_id=self.player.asset_id,
            stats=self.player.stats,
            size=(32, 32),
            level=self.player.level,
            experience=self.player.experience,
            inventory=full_inventory
        )
        game_state_full = GameStateData(
            player=player_full,
            monsters={},
            items={},
            current_level_id="test",
            collision_map=[[False]],
            level_tiles=[[]]
        )
        
        use_case = CanAddItemToInventoryUseCase(self.event_bus)
        
        # Can add to existing item
        assert use_case.execute(game_state_full, "item_0", 5) == True
        
        # Cannot add new item (no space)
        assert use_case.execute(game_state_full, "new_item", 1) == False


class TestPlayerCreationUseCase:
    """Test player creation use case."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.event_bus = Mock()
    
    def test_create_new_player_use_case(self):
        """Test creating a new player with starting equipment."""
        use_case = CreateNewPlayerUseCase(self.event_bus)
        player = use_case.execute("Test Hero", Position(192, 192))
        
        # Check basic properties
        assert player.name == "Test Hero"
        assert player.position == Position(192, 192)
        assert player.level == 1
        assert player.experience == 0
        
        # Check starting equipment
        assert player.head_equipment == "cloth_cap"
        assert player.chest_equipment == "cloth_shirt"
        assert player.legs_equipment == "cloth_pants"
        assert player.boots_equipment == "simple_shoes"
        assert player.main_hand_weapon == "rusty_sword"
        assert player.off_hand_equipment is None
        
        # Check starting inventory
        assert player.has_item("bread", 3)
        assert player.has_item("health_potion", 2)
        assert player.has_item("gold_coin", 25)


class TestPlayerVisualDataUseCase:
    """Test player visual data use case."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.event_bus = Mock()
        self.player = Player(
            id="test_player",
            name="Test Hero",
            position=Position(100, 100),
            asset_id="player.hero",
            stats=Stats(hp=100, max_hp=100, mp=50, max_mp=50, strength=10, defense=5, speed=8),
            size=(32, 32),
            level=1,
            experience=0,
            head_equipment="cloth_cap",
            chest_equipment="cloth_shirt",
            main_hand_weapon="rusty_sword"
        )
        self.game_state = GameStateData(
            player=self.player,
            monsters={},
            items={},
            current_level_id="test",
            collision_map=[[False]],
            level_tiles=[[]]
        )
    
    def test_get_player_visual_data_use_case(self):
        """Test getting player visual data for rendering."""
        use_case = GetPlayerVisualDataUseCase(self.event_bus)
        result = use_case.execute(self.game_state)
        
        assert result["head_equipment"] == "cloth_cap"
        assert result["chest_equipment"] == "cloth_shirt"
        assert result["legs_equipment"] is None
        assert result["boots_equipment"] is None
        assert result["main_hand_weapon"] == "rusty_sword"
        assert result["off_hand_equipment"] is None
    
    def test_get_player_visual_data_no_player(self):
        """Test getting visual data when no player exists."""
        game_state_no_player = GameStateData(
            player=None,
            monsters={},
            items={},
            current_level_id="test",
            collision_map=[[False]],
            level_tiles=[[]]
        )
        
        use_case = GetPlayerVisualDataUseCase(self.event_bus)
        result = use_case.execute(game_state_no_player)
        
        assert result == {}
