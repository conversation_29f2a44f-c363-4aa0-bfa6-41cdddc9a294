"""
Unit tests for core game logic functions.

These tests verify the pure game mechanics without any external dependencies.
"""

import pytest
from src.game_core import (
    is_move_valid, calculate_damage, apply_damage, check_level_up,
    calculate_hit_chance, check_hit, Position, Direction, Stats
)


class TestMovementLogic:
    """Test movement validation logic."""
    
    def test_is_move_valid_empty_map(self):
        # 3x3 map with no obstacles
        collision_map = [
            [False, False, False],
            [False, False, False], 
            [False, False, False]
        ]
        
        pos = Position(1, 1)  # Center
        
        # All moves should be valid
        assert is_move_valid(pos, Direction.NORTH, collision_map) is True
        assert is_move_valid(pos, Direction.SOUTH, collision_map) is True
        assert is_move_valid(pos, Direction.EAST, collision_map) is True
        assert is_move_valid(pos, Direction.WEST, collision_map) is True
    
    def test_is_move_valid_with_walls(self):
        # 3x3 map with walls
        collision_map = [
            [True,  False, True],
            [<PERSON>alse, <PERSON>als<PERSON>, <PERSON>als<PERSON>],
            [<PERSON>,  <PERSON>alse, True]
        ]
        
        pos = Position(1, 1)  # Center
        
        # Should be blocked by walls
        assert is_move_valid(pos, Direction.NORTH, collision_map) is True   # (1,0) is free
        assert is_move_valid(pos, Direction.NORTHWEST, collision_map) is False  # (0,0) is wall
        assert is_move_valid(pos, Direction.NORTHEAST, collision_map) is False  # (2,0) is wall
    
    def test_is_move_valid_boundary_check(self):
        collision_map = [
            [False, False],
            [False, False]
        ]
        
        # Test moving outside boundaries
        pos = Position(0, 0)  # Top-left corner
        assert is_move_valid(pos, Direction.NORTH, collision_map) is False  # Out of bounds
        assert is_move_valid(pos, Direction.WEST, collision_map) is False   # Out of bounds
        assert is_move_valid(pos, Direction.SOUTH, collision_map) is True   # Valid
        assert is_move_valid(pos, Direction.EAST, collision_map) is True    # Valid


class TestCombatLogic:
    """Test combat calculation logic."""
    
    def test_calculate_damage_basic(self):
        attacker = Stats(hp=100, max_hp=100, mp=50, max_mp=50,
                        strength=10, defense=5, speed=8)
        defender = Stats(hp=80, max_hp=80, mp=30, max_mp=30,
                        strength=6, defense=3, speed=6)
        
        # Mock randomness by running multiple times
        damages = [calculate_damage(attacker, defender) for _ in range(10)]
        
        # All damages should be at least 1
        assert all(d >= 1 for d in damages)
        
        # Damage should be roughly around (10 - 3) = 7, with variance
        # Most should be between 5 and 10
        assert all(d <= 15 for d in damages)  # Reasonable upper bound
    
    def test_calculate_damage_with_base_damage(self):
        attacker = Stats(hp=100, max_hp=100, mp=50, max_mp=50,
                        strength=5, defense=2, speed=8)
        defender = Stats(hp=80, max_hp=80, mp=30, max_mp=30,
                        strength=6, defense=2, speed=6)
        
        # With weapon damage
        damage_no_weapon = calculate_damage(attacker, defender, base_damage=0)
        damage_with_weapon = calculate_damage(attacker, defender, base_damage=5)
        
        # Weapon damage should generally increase damage
        # (Note: due to randomness, we can't guarantee exact values)
        assert damage_with_weapon >= damage_no_weapon or damage_with_weapon >= 1
    
    def test_apply_damage(self):
        stats = Stats(hp=100, max_hp=100, mp=50, max_mp=50,
                     strength=10, defense=5, speed=8)
        
        damaged_stats = apply_damage(stats, 30)
        
        assert damaged_stats.hp == 70
        assert damaged_stats.max_hp == 100  # Unchanged
        assert damaged_stats.strength == 10  # Unchanged
    
    def test_calculate_hit_chance(self):
        fast_attacker = Stats(hp=100, max_hp=100, mp=50, max_mp=50,
                             strength=10, defense=5, speed=15)
        slow_defender = Stats(hp=80, max_hp=80, mp=30, max_mp=30,
                             strength=6, defense=3, speed=5)
        
        hit_chance = calculate_hit_chance(fast_attacker, slow_defender)
        
        # Base chance is 75%, +10 speed difference = +20% = 95%
        assert hit_chance == 0.95
    
    def test_calculate_hit_chance_bounds(self):
        # Test that hit chance is bounded between 10% and 95%
        very_slow_attacker = Stats(hp=100, max_hp=100, mp=50, max_mp=50,
                                  strength=10, defense=5, speed=1)
        very_fast_defender = Stats(hp=80, max_hp=80, mp=30, max_mp=30,
                                  strength=6, defense=3, speed=50)
        
        hit_chance = calculate_hit_chance(very_slow_attacker, very_fast_defender)
        
        # Should be clamped to minimum 10%
        assert hit_chance == 0.1


class TestExperienceLogic:
    """Test experience and leveling logic."""
    
    def test_check_level_up_not_ready(self):
        # Level 1 character with 50 XP (needs 100 to reach level 2)
        assert check_level_up(50, 1) is False
    
    def test_check_level_up_ready(self):
        # Level 1 character with 100 XP (exactly enough for level 2)
        assert check_level_up(100, 1) is True
        
        # Level 1 character with 150 XP (more than enough)
        assert check_level_up(150, 1) is True
    
    def test_check_level_up_higher_levels(self):
        # Level 5 character needs 500 XP for level 6
        assert check_level_up(499, 5) is False
        assert check_level_up(500, 5) is True
        assert check_level_up(600, 5) is True


class TestUtilityFunctions:
    """Test utility functions for game logic."""
    
    def test_screen_to_direction_vector(self):
        from src.game_core import screen_to_direction_vector
        
        # Player at center of screen
        player_pos = (400, 300)
        
        # Mouse directly to the right
        mouse_pos = (500, 300)
        direction = screen_to_direction_vector(player_pos, mouse_pos)
        
        # Should point east (1, 0)
        assert abs(direction.x - 1.0) < 0.0001
        assert abs(direction.y - 0.0) < 0.0001
        
        # Mouse directly above
        mouse_pos = (400, 200)
        direction = screen_to_direction_vector(player_pos, mouse_pos)
        
        # Should point north (0, -1)
        assert abs(direction.x - 0.0) < 0.0001
        assert abs(direction.y - (-1.0)) < 0.0001
    
    def test_normalize_vector(self):
        from src.game_core import normalize_vector
        
        # 3-4-5 right triangle
        normalized = normalize_vector(3.0, 4.0)
        assert abs(normalized.x - 0.6) < 0.0001
        assert abs(normalized.y - 0.8) < 0.0001
        assert abs(normalized.magnitude() - 1.0) < 0.0001
