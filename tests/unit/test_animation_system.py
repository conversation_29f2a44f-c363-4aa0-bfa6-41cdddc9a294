"""
Unit tests for the animation system.

Tests the core animation functionality including state management,
transform calculations, and weapon-specific animations.
"""

import pytest
import time
import math
from unittest.mock import Mock, patch

from src.game_core import (
    Player, Position, Stats, Vector2,
    PlayerAnimationState, AnimationTransform, WeaponAnimationTransform, CombinedAnimationTransform,
    calculate_animation_progress, calculate_swing_rotation,
    calculate_vibration_offset, calculate_animation_transform,
    calculate_weapon_swing_rotation, calculate_weapon_animation_transform,
    calculate_combined_animation_transform, is_animation_active
)
from src.application.animation_manager import AnimationManager
from src.game_data import ItemDefinition, ItemType


class TestAnimationCore:
    """Test core animation functions."""
    
    def test_calculate_animation_progress(self):
        """Test animation progress calculation."""
        current_time = 10.0
        start_time = 8.0
        duration = 2.0
        
        # Test normal progress
        progress = calculate_animation_progress(current_time, start_time, duration)
        assert progress == 1.0  # Animation complete
        
        # Test mid-animation
        current_time = 9.0
        progress = calculate_animation_progress(current_time, start_time, duration)
        assert progress == 0.5  # Half complete
        
        # Test before start
        current_time = 7.0
        progress = calculate_animation_progress(current_time, start_time, duration)
        assert progress == 0.0  # Clamped to 0
        
        # Test zero duration
        progress = calculate_animation_progress(10.0, 8.0, 0.0)
        assert progress == 1.0  # Instant completion
    
    def test_calculate_swing_rotation(self):
        """Test melee weapon swing rotation calculation."""
        direction = Vector2(1.0, 0.0)  # Right direction
        max_angle = 15.0
        
        # Test at start of swing (progress = 0)
        rotation = calculate_swing_rotation(0.0, direction, max_angle)
        assert rotation == 0.0
        
        # Test at peak of swing (progress = 0.5)
        rotation = calculate_swing_rotation(0.5, direction, max_angle)
        assert abs(rotation - 15.0) < 0.1  # Should be near max angle
        
        # Test at end of swing (progress = 1.0)
        rotation = calculate_swing_rotation(1.0, direction, max_angle)
        assert abs(rotation) < 0.1  # Should be back to 0
        
        # Test left direction
        left_direction = Vector2(-1.0, 0.0)
        rotation = calculate_swing_rotation(0.5, left_direction, max_angle)
        assert rotation < 0  # Should swing in opposite direction
    
    def test_calculate_vibration_offset(self):
        """Test ranged weapon vibration calculation."""
        amplitude = 2.0
        frequency = 20.0
        
        # Test at start (progress = 0)
        offset_x, offset_y = calculate_vibration_offset(0.0, amplitude, frequency)
        assert abs(offset_x) <= amplitude
        assert abs(offset_y) <= amplitude
        
        # Test at mid-point (progress = 0.5)
        offset_x, offset_y = calculate_vibration_offset(0.5, amplitude, frequency)
        assert abs(offset_x) <= amplitude
        assert abs(offset_y) <= amplitude
        
        # Test that vibration decreases over time
        early_offset_x, _ = calculate_vibration_offset(0.1, amplitude, frequency)
        late_offset_x, _ = calculate_vibration_offset(0.9, amplitude, frequency)
        # Later in animation should have smaller amplitude due to decay
        assert abs(late_offset_x) <= abs(early_offset_x) + 0.1  # Allow small tolerance
    
    def test_calculate_animation_transform(self):
        """Test animation transform calculation."""
        # Test idle state (no transform)
        idle_state = PlayerAnimationState(animation_type="idle")
        transform = calculate_animation_transform(idle_state, time.time())
        assert transform.rotation == 0.0
        assert transform.offset_x == 0.0
        assert transform.offset_y == 0.0
        
        # Test melee attack
        current_time = time.time()
        melee_state = PlayerAnimationState(
            animation_type="attacking",
            start_time=current_time - 0.1,  # 0.1 seconds into animation
            duration=0.3,
            weapon_type="melee",
            attack_direction=Vector2(1.0, 0.0)
        )
        transform = calculate_animation_transform(melee_state, current_time)
        assert transform.rotation != 0.0  # Should have rotation
        assert transform.offset_x == 0.0  # No offset for melee
        assert transform.offset_y == 0.0
        
        # Test ranged attack
        ranged_state = PlayerAnimationState(
            animation_type="attacking",
            start_time=current_time - 0.1,
            duration=0.3,
            weapon_type="ranged",
            attack_direction=Vector2(1.0, 0.0)
        )
        transform = calculate_animation_transform(ranged_state, current_time)
        assert transform.rotation == 0.0  # No rotation for ranged
        assert transform.offset_x != 0.0 or transform.offset_y != 0.0  # Should have offset
    
    def test_is_animation_active(self):
        """Test animation active state checking."""
        current_time = time.time()
        
        # Test idle state
        idle_state = PlayerAnimationState(animation_type="idle")
        assert not is_animation_active(idle_state, current_time)
        
        # Test active animation
        active_state = PlayerAnimationState(
            animation_type="attacking",
            start_time=current_time - 0.1,
            duration=0.3
        )
        assert is_animation_active(active_state, current_time)
        
        # Test finished animation
        finished_state = PlayerAnimationState(
            animation_type="attacking",
            start_time=current_time - 0.5,
            duration=0.3
        )
        assert not is_animation_active(finished_state, current_time)

    def test_calculate_weapon_swing_rotation(self):
        """Test weapon swing rotation calculation."""
        direction = Vector2(1.0, 0.0)  # Right direction
        max_angle = 45.0

        # Test at start of swing (progress = 0)
        rotation = calculate_weapon_swing_rotation(0.0, direction, max_angle)
        assert rotation == 0.0  # Should start at attack direction (0 degrees for right)

        # Test at peak of swing (progress = 0.5)
        rotation = calculate_weapon_swing_rotation(0.5, direction, max_angle)
        assert abs(rotation - 45.0) < 0.1  # Should be near max swing angle

        # Test at end of swing (progress = 1.0)
        rotation = calculate_weapon_swing_rotation(1.0, direction, max_angle)
        assert abs(rotation) < 0.1  # Should return to attack direction

        # Test different direction (up attack uses stabbing motion)
        up_direction = Vector2(0.0, -1.0)  # Up direction
        rotation = calculate_weapon_swing_rotation(0.5, up_direction, max_angle)
        assert rotation == 0.0  # Up attacks use stabbing motion, no rotation

    def test_calculate_weapon_animation_transform(self):
        """Test weapon animation transform calculation."""
        current_time = time.time()

        # Test melee weapon
        melee_state = PlayerAnimationState(
            animation_type="attacking",
            start_time=current_time - 0.1,
            duration=0.3,
            weapon_type="melee",
            attack_direction=Vector2(1.0, 0.0)
        )

        weapon_transform = calculate_weapon_animation_transform(melee_state, current_time)
        assert isinstance(weapon_transform, WeaponAnimationTransform)
        assert weapon_transform.rotation != 0.0  # Should have rotation
        assert weapon_transform.pivot_x == 0.72  # Right hand position for melee weapons
        assert weapon_transform.pivot_y == 0.71  # Hand height for melee weapons

        # Test ranged weapon
        ranged_state = PlayerAnimationState(
            animation_type="attacking",
            start_time=current_time - 0.1,
            duration=0.3,
            weapon_type="ranged",
            attack_direction=Vector2(1.0, 0.0)
        )

        weapon_transform = calculate_weapon_animation_transform(ranged_state, current_time)
        assert weapon_transform.rotation == 0.0  # No rotation for ranged
        assert weapon_transform.offset_x != 0.0 or weapon_transform.offset_y != 0.0  # Should have offset
        assert weapon_transform.pivot_y == 0.59  # Bow grip area

    def test_calculate_combined_animation_transform(self):
        """Test combined animation transform calculation."""
        current_time = time.time()

        # Test with melee weapon
        melee_state = PlayerAnimationState(
            animation_type="attacking",
            start_time=current_time - 0.1,
            duration=0.3,
            weapon_type="melee",
            attack_direction=Vector2(1.0, 0.0)
        )

        combined = calculate_combined_animation_transform(melee_state, current_time)
        assert isinstance(combined, CombinedAnimationTransform)
        assert isinstance(combined.player_transform, AnimationTransform)
        assert isinstance(combined.weapon_transform, WeaponAnimationTransform)

        # Player should have some rotation for melee
        assert combined.player_transform.rotation != 0.0
        # Weapon should have more rotation than player
        assert abs(combined.weapon_transform.rotation) > abs(combined.player_transform.rotation)

        # Test with idle state
        idle_state = PlayerAnimationState(animation_type="idle")
        combined_idle = calculate_combined_animation_transform(idle_state, current_time)
        assert combined_idle.player_transform.rotation == 0.0
        assert combined_idle.weapon_transform.rotation == 0.0


class TestAnimationManager:
    """Test the AnimationManager class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.event_bus = Mock()
        self.animation_manager = AnimationManager(self.event_bus)
        
        # Create test player
        self.player = Player(
            id="test_player",
            name="Test Hero",
            position=Position(100, 100),
            asset_id="player.hero",
            stats=Stats(hp=100, max_hp=100, mp=50, max_mp=50, strength=10, defense=5, speed=8),
            size=(32, 32),
            level=1,
            experience=0,
            main_hand_weapon="rusty_sword"
        )
    
    def test_update_config(self):
        """Test configuration updates."""
        config = {
            'animations': {
                'attack_base_duration': 500,
                'melee_swing_angle': 20.0,
                'ranged_vibration_amplitude': 3.0,
                'ranged_vibration_frequency': 25.0,
                'weapon_swing_angle': 60.0,
                'weapon_vibration_amplitude': 2.0
            }
        }
        
        self.animation_manager.update_config(config)
        
        assert self.animation_manager.config_attack_base_duration == 500
        assert self.animation_manager.config_melee_swing_angle == 20.0
        assert self.animation_manager.config_ranged_vibration_amplitude == 3.0
        assert self.animation_manager.config_ranged_vibration_frequency == 25.0
        assert self.animation_manager.config_weapon_swing_angle == 60.0
        assert self.animation_manager.config_weapon_vibration_amplitude == 2.0
    
    @patch('src.application.animation_manager.get_item_definition')
    def test_get_weapon_animation_type(self, mock_get_item_def):
        """Test weapon type detection."""
        # Test sword (melee)
        mock_get_item_def.return_value = ItemDefinition(
            id="test_sword", name="Test Sword", asset_id="test",
            item_type=ItemType.WEAPON, properties={"weapon_type": "sword"}
        )

        weapon_type = self.animation_manager.get_weapon_animation_type(self.player)
        assert weapon_type == "melee"

        # Test bow (ranged)
        mock_get_item_def.return_value = ItemDefinition(
            id="test_bow", name="Test Bow", asset_id="test",
            item_type=ItemType.WEAPON, properties={"weapon_type": "bow"}
        )

        weapon_type = self.animation_manager.get_weapon_animation_type(self.player)
        assert weapon_type == "ranged"

        # Test no weapon
        player_no_weapon = Player(
            id="test_player", name="Test Hero", position=Position(100, 100),
            asset_id="player.hero", stats=Stats(hp=100, max_hp=100, mp=50, max_mp=50, strength=10, defense=5, speed=8),
            size=(32, 32), level=1, experience=0, main_hand_weapon=None
        )

        weapon_type = self.animation_manager.get_weapon_animation_type(player_no_weapon)
        assert weapon_type == "unarmed"
    
    @patch('src.application.animation_manager.get_item_definition')
    def test_get_weapon_attack_speed(self, mock_get_item_def):
        """Test weapon attack speed detection."""
        mock_get_item_def.return_value = ItemDefinition(
            id="test_weapon", name="Test Weapon", asset_id="test",
            item_type=ItemType.WEAPON, properties={"attack_speed": 1.5}
        )

        attack_speed = self.animation_manager.get_weapon_attack_speed(self.player)
        assert attack_speed == 1.5
    
    @patch('src.application.animation_manager.get_item_definition')
    @patch('src.application.animation_manager.time.time')
    def test_start_attack_animation(self, mock_time, mock_get_item_def):
        """Test starting attack animations."""
        mock_time.return_value = 100.0
        mock_get_item_def.return_value = ItemDefinition(
            id="test_sword", name="Test Sword", asset_id="test",
            item_type=ItemType.WEAPON,
            properties={"weapon_type": "sword", "attack_speed": 1.2}
        )

        direction = Vector2(1.0, 0.0)
        self.animation_manager.start_attack_animation(self.player, direction)

        # Check animation state was set correctly
        state = self.animation_manager.animation_state
        assert state.animation_type == "attacking"
        assert state.start_time == 100.0
        assert state.weapon_type == "melee"
        assert state.attack_direction == direction
        assert state.duration == pytest.approx(0.25, rel=0.1)  # 300ms / 1.2 speed
    
    def test_update_animation(self):
        """Test animation updates."""
        # Start with an active animation
        self.animation_manager.animation_state = PlayerAnimationState(
            animation_type="attacking",
            start_time=time.time() - 1.0,  # Started 1 second ago
            duration=0.3  # 300ms duration - should be finished
        )
        
        # Update should reset to idle
        self.animation_manager.update_animation(0.016)  # 16ms frame
        
        assert self.animation_manager.animation_state.animation_type == "idle"
    
    def test_get_current_animation_transform(self):
        """Test getting current animation transform."""
        # Test idle state
        transform = self.animation_manager.get_current_animation_transform()
        assert transform.rotation == 0.0
        assert transform.offset_x == 0.0
        assert transform.offset_y == 0.0
        
        # Test active animation
        current_time = time.time()
        self.animation_manager.animation_state = PlayerAnimationState(
            animation_type="attacking",
            start_time=current_time - 0.1,
            duration=0.3,
            weapon_type="melee",
            attack_direction=Vector2(1.0, 0.0)
        )
        
        transform = self.animation_manager.get_current_animation_transform()
        # Should have some rotation for melee weapon
        assert transform.rotation != 0.0

    def test_get_current_combined_animation_transform(self):
        """Test getting current combined animation transform."""
        # Test idle state
        combined_transform = self.animation_manager.get_current_combined_animation_transform()
        assert isinstance(combined_transform, CombinedAnimationTransform)
        assert combined_transform.player_transform.rotation == 0.0
        assert combined_transform.weapon_transform.rotation == 0.0

        # Test active animation
        current_time = time.time()
        self.animation_manager.animation_state = PlayerAnimationState(
            animation_type="attacking",
            start_time=current_time - 0.1,
            duration=0.3,
            weapon_type="melee",
            attack_direction=Vector2(1.0, 0.0)
        )

        combined_transform = self.animation_manager.get_current_combined_animation_transform()
        # Should have transforms for both player and weapon
        assert combined_transform.player_transform.rotation != 0.0
        assert combined_transform.weapon_transform.rotation != 0.0
        # Weapon should have more rotation than player
        assert abs(combined_transform.weapon_transform.rotation) > abs(combined_transform.player_transform.rotation)
    
    def test_is_animation_active(self):
        """Test animation active checking."""
        # Test idle
        assert not self.animation_manager.is_animation_active()
        
        # Test active animation
        self.animation_manager.animation_state = PlayerAnimationState(
            animation_type="attacking",
            start_time=time.time() - 0.1,
            duration=0.3
        )
        
        assert self.animation_manager.is_animation_active()


class TestAnimationTransform:
    """Test AnimationTransform data class."""

    def test_default_values(self):
        """Test default transform values."""
        transform = AnimationTransform()
        assert transform.rotation == 0.0
        assert transform.offset_x == 0.0
        assert transform.offset_y == 0.0

    def test_custom_values(self):
        """Test custom transform values."""
        transform = AnimationTransform(rotation=15.0, offset_x=2.5, offset_y=-1.0)
        assert transform.rotation == 15.0
        assert transform.offset_x == 2.5
        assert transform.offset_y == -1.0


class TestWeaponAnimationTransform:
    """Test WeaponAnimationTransform data class."""

    def test_default_values(self):
        """Test default weapon transform values."""
        transform = WeaponAnimationTransform()
        assert transform.rotation == 0.0
        assert transform.offset_x == 0.0
        assert transform.offset_y == 0.0
        assert transform.pivot_x == 0.5  # Center
        assert transform.pivot_y == 0.8  # Handle area

    def test_custom_values(self):
        """Test custom weapon transform values."""
        transform = WeaponAnimationTransform(
            rotation=45.0, offset_x=3.0, offset_y=-2.0,
            pivot_x=0.3, pivot_y=0.9
        )
        assert transform.rotation == 45.0
        assert transform.offset_x == 3.0
        assert transform.offset_y == -2.0
        assert transform.pivot_x == 0.3
        assert transform.pivot_y == 0.9


class TestCombinedAnimationTransform:
    """Test CombinedAnimationTransform data class."""

    def test_default_values(self):
        """Test default combined transform values."""
        combined = CombinedAnimationTransform()
        assert isinstance(combined.player_transform, AnimationTransform)
        assert isinstance(combined.weapon_transform, WeaponAnimationTransform)
        assert combined.player_transform.rotation == 0.0
        assert combined.weapon_transform.rotation == 0.0

    def test_custom_values(self):
        """Test custom combined transform values."""
        player_transform = AnimationTransform(rotation=10.0)
        weapon_transform = WeaponAnimationTransform(rotation=30.0)

        combined = CombinedAnimationTransform(
            player_transform=player_transform,
            weapon_transform=weapon_transform
        )

        assert combined.player_transform.rotation == 10.0
        assert combined.weapon_transform.rotation == 30.0
