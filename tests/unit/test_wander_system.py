"""
Unit tests for the wander system.

These tests verify the wandering behavior logic without external dependencies.
"""

import pytest
import time
from unittest.mock import Mock, patch
from src.game_core import Position, Direction, NPC, Monster, Stats
from src.game_core.wander_system import (
    WanderBehavior, WanderState, 
    _get_random_direction, _calculate_distance, _is_within_wander_radius,
    _find_valid_wander_target, _update_wander_behavior, update_wandering_entities
)


class TestWanderBehavior:
    """Test the WanderBehavior configuration class."""
    
    def test_wander_behavior_creation(self):
        """Test creating a WanderBehavior with default values."""
        wander = WanderBehavior()
        
        assert wander.enabled is True
        assert wander.wander_radius == 3.0
        assert wander.move_speed == 1.0
        assert wander.current_state == WanderState.IDLE
        assert wander.spawn_position is None
        assert wander.target_position is None
    
    def test_wander_behavior_custom_values(self):
        """Test creating a WanderBehavior with custom values."""
        wander = WanderBehavior(
            enabled=False,
            wander_radius=5.0,
            move_speed=0.5,
            idle_time_min=1.0,
            idle_time_max=3.0
        )
        
        assert wander.enabled is False
        assert wander.wander_radius == 5.0
        assert wander.move_speed == 0.5
        assert wander.idle_time_min == 1.0
        assert wander.idle_time_max == 3.0
    
    def test_state_transitions(self):
        """Test state transition methods."""
        wander = WanderBehavior()
        
        # Test transition to moving
        target_pos = Position(100, 100)
        direction = Direction.NORTH
        wander.transition_to_moving(target_pos, direction)
        
        assert wander.current_state == WanderState.MOVING
        assert wander.target_position == target_pos
        assert wander.current_direction == direction
        
        # Test transition to paused
        wander.transition_to_paused(2.0)
        
        assert wander.current_state == WanderState.PAUSED
        assert wander.state_duration == 2.0
        assert wander.current_direction is None
        
        # Test transition to idle
        wander.transition_to_idle()
        
        assert wander.current_state == WanderState.IDLE
        assert wander.target_position is None
        assert wander.current_direction is None
    
    def test_state_expiration(self):
        """Test state expiration checking."""
        wander = WanderBehavior()

        # Manually set state timing for testing
        wander.state_start_time = 95.0
        wander.state_duration = 3.0

        # Mock time.time to return specific values
        with patch('src.game_core.wander_system.time.time') as mock_time:
            # Should not be expired yet (2.9 seconds elapsed, 3 second duration)
            mock_time.return_value = 97.9
            assert not wander.is_state_expired()

            # Should be expired now (3.1 seconds elapsed, 3 second duration)
            mock_time.return_value = 98.1
            assert wander.is_state_expired()


class TestWanderUtilityFunctions:
    """Test utility functions used by the wander system."""
    
    def test_get_random_direction(self):
        """Test that random direction returns valid directions."""
        for _ in range(20):  # Test multiple times due to randomness
            direction = _get_random_direction()
            assert isinstance(direction, Direction)
            assert direction in [
                Direction.NORTH, Direction.NORTHEAST, Direction.EAST, Direction.SOUTHEAST,
                Direction.SOUTH, Direction.SOUTHWEST, Direction.WEST, Direction.NORTHWEST
            ]
    
    def test_calculate_distance(self):
        """Test distance calculation between positions."""
        pos1 = Position(0, 0)
        pos2 = Position(128, 0)  # 1 tile away (128 pixels = 1 tile)
        pos3 = Position(256, 256)  # 2 tiles away diagonally
        
        # Distance should be in tiles
        assert _calculate_distance(pos1, pos2, 128) == 1.0
        assert abs(_calculate_distance(pos1, pos3, 128) - 2.828) < 0.01  # sqrt(8) ≈ 2.828
    
    def test_is_within_wander_radius(self):
        """Test wander radius checking."""
        spawn_pos = Position(256, 256)  # Center at tile (2, 2)
        
        # Position 1 tile away should be within radius 2
        pos1 = Position(384, 256)  # 1 tile east
        assert _is_within_wander_radius(pos1, spawn_pos, 2.0, 128)
        
        # Position 3 tiles away should not be within radius 2
        pos2 = Position(640, 256)  # 3 tiles east
        assert not _is_within_wander_radius(pos2, spawn_pos, 2.0, 128)
    
    def test_find_valid_wander_target(self):
        """Test finding valid wander targets."""
        current_pos = Position(256, 256)  # Center at tile (2, 2)
        spawn_pos = Position(256, 256)
        wander_radius = 2.0
        
        # Create a simple collision map (5x5, all passable)
        collision_map = [[False for _ in range(5)] for _ in range(5)]
        
        target_info = _find_valid_wander_target(
            current_pos, spawn_pos, wander_radius, collision_map, 128
        )
        
        # Should find a valid target
        assert target_info is not None
        target_pos, direction = target_info
        assert isinstance(target_pos, Position)
        assert isinstance(direction, Direction)
        
        # Target should be within wander radius
        assert _is_within_wander_radius(target_pos, spawn_pos, wander_radius, 128)
    
    def test_find_valid_wander_target_blocked(self):
        """Test finding wander targets when area is blocked."""
        current_pos = Position(256, 256)  # Center at tile (2, 2)
        spawn_pos = Position(256, 256)
        wander_radius = 1.0
        
        # Create a collision map where everything is blocked
        collision_map = [[True for _ in range(5)] for _ in range(5)]
        
        target_info = _find_valid_wander_target(
            current_pos, spawn_pos, wander_radius, collision_map, 128
        )
        
        # Should not find a valid target
        assert target_info is None


class TestWanderBehaviorUpdate:
    """Test the core wander behavior update logic."""
    
    def create_test_npc(self, position: Position) -> NPC:
        """Create a test NPC for wander testing."""
        npc = NPC(
            id="test_npc",
            name="Test NPC",
            position=position,
            asset_id="test_asset",
            npc_type="commoner",
            behavior="dialog"
        )
        npc.wander_behavior = WanderBehavior(enabled=True, wander_radius=2.0)
        return npc
    
    @patch('src.game_core.wander_system.time.time')
    def test_idle_state_behavior(self, mock_time):
        """Test behavior during idle state."""
        mock_time.return_value = 100.0
        
        npc = self.create_test_npc(Position(256, 256))
        wander = npc.wander_behavior
        wander.current_state = WanderState.IDLE
        wander.state_start_time = 95.0
        wander.state_duration = 3.0
        
        # Create passable collision map
        collision_map = [[False for _ in range(5)] for _ in range(5)]
        
        # Should stay in same position while not expired
        mock_time.return_value = 97.0
        new_pos = _update_wander_behavior(npc, wander, 0.1, collision_map)
        assert new_pos == npc.position
        assert wander.current_state == WanderState.IDLE
        
        # Should transition to moving when expired
        mock_time.return_value = 100.0
        new_pos = _update_wander_behavior(npc, wander, 0.1, collision_map)
        # State should have changed (might be MOVING or back to IDLE if no valid target)
        assert wander.current_state in [WanderState.MOVING, WanderState.IDLE]
    
    def test_moving_state_behavior(self):
        """Test behavior during moving state."""
        npc = self.create_test_npc(Position(256, 256))
        wander = npc.wander_behavior
        
        # Set up moving state
        target_pos = Position(384, 256)  # 1 tile east
        wander.transition_to_moving(target_pos, Direction.EAST)
        
        # Create passable collision map
        collision_map = [[False for _ in range(5)] for _ in range(5)]
        
        # Should move towards target
        new_pos = _update_wander_behavior(npc, wander, 0.1, collision_map, 100.0)
        
        # Should have moved east (x should increase)
        assert new_pos.x > npc.position.x
        assert new_pos.y == npc.position.y  # y should stay same for eastward movement
    
    def test_collision_during_movement(self):
        """Test behavior when hitting collision during movement."""
        npc = self.create_test_npc(Position(256, 256))  # At tile (2, 2)
        wander = npc.wander_behavior

        # Set up moving state towards a blocked area
        target_pos = Position(384, 256)  # 1 tile east
        wander.transition_to_moving(target_pos, Direction.EAST)

        # Create collision map with blocked tile to the east
        collision_map = [[False for _ in range(5)] for _ in range(5)]
        collision_map[2][3] = True  # Block tile (3, 2) - where NPC would move to

        # Calculate where NPC would move to with given speed
        move_distance = 100.0 * 0.7 * 0.1  # base_speed * move_speed * dt = 7 pixels
        expected_new_x = 256 + 7  # Should move 7 pixels east
        expected_new_pos = Position(expected_new_x, 256)

        # Check if the expected position would be in a blocked tile
        expected_tile_x, expected_tile_y = expected_new_pos.to_tile_coords(128)

        # Should not move due to collision, or should transition to paused
        new_pos = _update_wander_behavior(npc, wander, 0.1, collision_map, 100.0)

        # Either should not move or should transition to paused state
        if collision_map[expected_tile_y][expected_tile_x]:
            # If collision detected, should not move and transition to paused
            assert new_pos == npc.position
            assert wander.current_state == WanderState.PAUSED
        else:
            # If no collision (movement is small), should move normally
            assert new_pos.x > npc.position.x  # Should move east


class TestUpdateWanderingEntities:
    """Test the main update function for wandering entities."""
    
    def create_test_entities(self):
        """Create test NPCs and monsters for testing."""
        # Create wandering NPC
        wandering_npc = NPC(
            id="wandering_npc",
            name="Wandering NPC",
            position=Position(256, 256),
            asset_id="test_asset",
            npc_type="commoner",
            behavior="dialog"
        )
        wandering_npc.wander_behavior = WanderBehavior(enabled=True)
        
        # Create stationary NPC
        stationary_npc = NPC(
            id="stationary_npc",
            name="Stationary NPC",
            position=Position(384, 256),
            asset_id="test_asset",
            npc_type="merchant",
            behavior="store"
        )
        stationary_npc.wander_behavior = WanderBehavior(enabled=False)
        
        # Create wandering monster
        wandering_monster = Monster(
            id="wandering_monster",
            name="Wandering Monster",
            position=Position(512, 256),
            asset_id="test_asset",
            stats=Stats(hp=50, max_hp=50, mp=0, max_mp=0, strength=10, defense=5, speed=8),
            monster_type="test_monster"
        )
        wandering_monster.wander_behavior = WanderBehavior(enabled=True)
        
        return {
            "wandering_npc": wandering_npc,
            "stationary_npc": stationary_npc
        }, {
            "wandering_monster": wandering_monster
        }
    
    def test_update_wandering_entities(self):
        """Test updating all wandering entities."""
        npcs, monsters = self.create_test_entities()
        
        # Create passable collision map
        collision_map = [[False for _ in range(10)] for _ in range(10)]
        
        # Mock config
        config = Mock()
        config.rendering = Mock()
        config.rendering.tile_size = 128
        config.movement = Mock()
        config.movement.player_move_speed = 6.67
        
        # Update entities
        updated_npcs, updated_monsters = update_wandering_entities(
            npcs, monsters, 0.1, collision_map, config
        )
        
        # Should return same number of entities
        assert len(updated_npcs) == len(npcs)
        assert len(updated_monsters) == len(monsters)
        
        # Wandering entities should have wander_behavior preserved
        assert hasattr(updated_npcs["wandering_npc"], 'wander_behavior')
        assert hasattr(updated_monsters["wandering_monster"], 'wander_behavior')
        
        # Stationary NPC should not have moved
        assert updated_npcs["stationary_npc"].position == npcs["stationary_npc"].position
    
    def test_update_with_no_config(self):
        """Test updating entities with no config provided."""
        npcs, monsters = self.create_test_entities()
        
        # Create passable collision map
        collision_map = [[False for _ in range(10)] for _ in range(10)]
        
        # Update entities without config
        updated_npcs, updated_monsters = update_wandering_entities(
            npcs, monsters, 0.1, collision_map, None
        )
        
        # Should still work with default values
        assert len(updated_npcs) == len(npcs)
        assert len(updated_monsters) == len(monsters)
