"""
Unit tests for the core game entities.

These tests verify the pure game logic without any external dependencies.
"""

import pytest
from src.game_core import Position, Vector2, <PERSON><PERSON>, <PERSON>, Monster, Direction


class TestPosition:
    """Test the Position value object."""
    
    def test_position_creation(self):
        pos = Position(5, 10)
        assert pos.x == 5
        assert pos.y == 10
    
    def test_position_immutable(self):
        pos = Position(5, 10)
        # Should not be able to modify
        with pytest.raises(AttributeError):
            pos.x = 15
    
    def test_position_move(self):
        pos = Position(5, 5)
        
        # Test all directions
        assert pos.move(Direction.NORTH) == Position(5, 4)
        assert pos.move(Direction.SOUTH) == Position(5, 6)
        assert pos.move(Direction.EAST) == Position(6, 5)
        assert pos.move(Direction.WEST) == Position(4, 5)
        
        # Test diagonals
        assert pos.move(Direction.NORTHEAST) == Position(6, 4)
        assert pos.move(Direction.SOUTHWEST) == Position(4, 6)
    
    def test_position_equality(self):
        pos1 = Position(5, 10)
        pos2 = Position(5, 10)
        pos3 = Position(6, 10)
        
        assert pos1 == pos2
        assert pos1 != pos3


class TestVector2:
    """Test the Vector2 value object."""
    
    def test_vector_creation(self):
        vec = Vector2(3.0, 4.0)
        assert vec.x == 3.0
        assert vec.y == 4.0
    
    def test_vector_magnitude(self):
        vec = Vector2(3.0, 4.0)
        assert vec.magnitude() == 5.0
    
    def test_vector_normalize(self):
        vec = Vector2(3.0, 4.0)
        normalized = vec.normalize()
        
        assert abs(normalized.magnitude() - 1.0) < 0.0001
        assert abs(normalized.x - 0.6) < 0.0001
        assert abs(normalized.y - 0.8) < 0.0001
    
    def test_zero_vector_normalize(self):
        vec = Vector2(0.0, 0.0)
        normalized = vec.normalize()
        assert normalized.x == 0.0
        assert normalized.y == 0.0


class TestStats:
    """Test the Stats value object."""
    
    def test_stats_creation(self):
        stats = Stats(hp=100, max_hp=100, mp=50, max_mp=50, 
                     strength=10, defense=5, speed=8)
        assert stats.hp == 100
        assert stats.strength == 10
    
    def test_take_damage(self):
        stats = Stats(hp=100, max_hp=100, mp=50, max_mp=50,
                     strength=10, defense=5, speed=8)
        
        damaged = stats.take_damage(30)
        assert damaged.hp == 70
        assert damaged.max_hp == 100  # Max HP unchanged
    
    def test_take_damage_beyond_zero(self):
        stats = Stats(hp=20, max_hp=100, mp=50, max_mp=50,
                     strength=10, defense=5, speed=8)
        
        damaged = stats.take_damage(50)
        assert damaged.hp == 0  # Can't go below 0
    
    def test_heal(self):
        stats = Stats(hp=50, max_hp=100, mp=25, max_mp=50,
                     strength=10, defense=5, speed=8)
        
        healed = stats.heal(30)
        assert healed.hp == 80
    
    def test_heal_beyond_max(self):
        stats = Stats(hp=90, max_hp=100, mp=25, max_mp=50,
                     strength=10, defense=5, speed=8)
        
        healed = stats.heal(30)
        assert healed.hp == 100  # Can't exceed max HP


class TestPlayer:
    """Test the Player entity."""
    
    def test_player_creation(self):
        stats = Stats(hp=100, max_hp=100, mp=50, max_mp=50,
                     strength=10, defense=5, speed=8)
        player = Player(
            id="player1",
            name="Hero",
            position=Position(5, 5),
            asset_id="player.hero",
            stats=stats,
            size=(32, 32),
            level=1,
            experience=0
        )
        
        assert player.name == "Hero"
        assert player.level == 1
        assert player.stats.hp == 100
    
    def test_player_level_up(self):
        stats = Stats(hp=100, max_hp=100, mp=50, max_mp=50,
                     strength=10, defense=5, speed=8)
        player = Player(
            id="player1",
            name="Hero",
            position=Position(5, 5),
            asset_id="player.hero",
            stats=stats,
            size=(32, 32),
            level=1,
            experience=100
        )
        
        leveled_player = player.level_up()
        
        assert leveled_player.level == 2
        assert leveled_player.stats.max_hp == 110  # +10 HP
        assert leveled_player.stats.strength == 12  # +2 STR
        assert leveled_player.id == player.id  # Same player


class TestMonster:
    """Test the Monster entity."""
    
    def test_monster_creation(self):
        stats = Stats(hp=30, max_hp=30, mp=0, max_mp=0,
                     strength=8, defense=2, speed=5)
        monster = Monster(
            id="goblin1",
            name="Goblin Grunt",
            position=Position(10, 10),
            asset_id="monster.goblin.grunt.side",
            stats=stats,
            monster_type="goblin",
            experience_reward=15
        )
        
        assert monster.name == "Goblin Grunt"
        assert monster.monster_type == "goblin"
        assert monster.experience_reward == 15
    
    def test_monster_is_alive(self):
        stats_alive = Stats(hp=30, max_hp=30, mp=0, max_mp=0,
                           strength=8, defense=2, speed=5)
        stats_dead = Stats(hp=0, max_hp=30, mp=0, max_mp=0,
                          strength=8, defense=2, speed=5)
        
        monster_alive = Monster(
            id="goblin1", name="Goblin", position=Position(0, 0),
            asset_id="monster.goblin", stats=stats_alive,
            monster_type="goblin"
        )
        
        monster_dead = Monster(
            id="goblin2", name="Dead Goblin", position=Position(0, 0),
            asset_id="monster.goblin", stats=stats_dead,
            monster_type="goblin"
        )
        
        assert monster_alive.is_alive() is True
        assert monster_dead.is_alive() is False
