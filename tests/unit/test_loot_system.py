"""
Test the monster loot system.
"""

import pytest
from unittest.mock import Mock

from src.game_core.entities import <PERSON><PERSON><PERSON>, Stats, Monster, Player, Item
from src.game_data.monsters import generate_loot, get_monster_definition
from src.application.use_cases import SpawnLootUseCase, PickupItemUseCase
from src.application.interfaces import GameStateData


class TestLootGeneration:
    """Test loot generation from monster definitions."""

    def test_generate_loot_humanoid_monster(self):
        """Test that humanoid monsters generate gold."""
        # Test goblin grunt (humanoid)
        loot = generate_loot("goblin_grunt")
        
        # Should generate gold coins
        assert "gold_coin" in loot
        assert 2 <= loot["gold_coin"] <= 8  # Based on loot table

    def test_generate_loot_non_humanoid_monster(self):
        """Test that non-humanoid monsters generate meat and leather."""
        # Test cow (non-humanoid)
        loot = generate_loot("cow")
        
        # Should have a chance to generate meat and leather
        # Note: Due to randomness, we can't guarantee specific items
        # but we can check that the function returns a valid dict
        assert isinstance(loot, dict)
        
        # If items are generated, they should be the right types
        if "meat" in loot:
            assert 2 <= loot["meat"] <= 5  # Based on loot table
        if "leather" in loot:
            assert 1 <= loot["leather"] <= 2  # Based on loot table

    def test_generate_loot_unknown_monster(self):
        """Test that unknown monsters return empty loot."""
        loot = generate_loot("unknown_monster")
        assert loot == {}

    def test_generate_loot_multiple_runs(self):
        """Test that loot generation produces varied results."""
        # Run multiple times to test randomness
        results = []
        for _ in range(10):
            loot = generate_loot("goblin_grunt")
            if "gold_coin" in loot:
                results.append(loot["gold_coin"])
        
        # Should have some variation in gold amounts
        assert len(set(results)) > 1  # Not all the same value


class TestPlayerAttackLootIntegration:
    """Test loot integration with player attacks."""

    def test_monster_defeat_adds_loot_to_inventory(self):
        """Test that defeating a monster adds loot directly to player inventory."""
        # This test would require setting up the full combat system
        # For now, we'll test the loot generation separately
        pass


class TestPickupItemUseCase:
    """Test the pickup item use case."""

    def test_pickup_item_success(self):
        """Test successful item pickup."""
        # Setup
        event_bus = Mock()
        use_case = PickupItemUseCase(event_bus)
        
        player = Player(
            id="player1",
            name="Test Player",
            position=Position(100, 100),
            asset_id="player.human.male",
            stats=Stats(hp=100, max_hp=100, mp=50, max_mp=50, strength=10, defense=5, speed=8),
            size=(64, 64),
            inventory={}
        )
        
        # Create an item entity
        item = Item(
            id="item1",
            name="Gold Coin",
            position=Position(200, 200),
            asset_id="item.treasure.coin.gold",
            item_type="treasure",
            stackable=True,
            stack_size=5
        )
        
        game_state = GameStateData(
            player=player,
            monsters={},
            items={"item1": item},
            npcs={},
            current_level_id="test_level",
            collision_map=[],
            level_tiles=[]
        )
        
        # Execute
        updated_state = use_case.execute(game_state, "item1")
        
        # Verify
        # Item should be removed from world
        assert "item1" not in updated_state.items
        
        # Item should be added to player inventory
        assert "gold_coin" in updated_state.player.inventory
        assert updated_state.player.inventory["gold_coin"] == 5

    def test_pickup_item_not_found(self):
        """Test pickup of non-existent item."""
        # Setup
        event_bus = Mock()
        use_case = PickupItemUseCase(event_bus)
        
        player = Player(
            id="player1",
            name="Test Player",
            position=Position(100, 100),
            asset_id="player.human.male",
            stats=Stats(hp=100, max_hp=100, mp=50, max_mp=50, strength=10, defense=5, speed=8),
            size=(64, 64),
            inventory={}
        )
        
        game_state = GameStateData(
            player=player,
            monsters={},
            items={},
            npcs={},
            current_level_id="test_level",
            collision_map=[],
            level_tiles=[]
        )
        
        # Execute
        updated_state = use_case.execute(game_state, "nonexistent_item")
        
        # Verify - state should be unchanged
        assert updated_state == game_state
