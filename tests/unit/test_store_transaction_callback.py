"""
Unit tests for store UI transaction callback functionality.
"""

import pytest
import pygame
from unittest.mock import Mo<PERSON>, MagicMock

from src.presentation.ui.store_ui import StoreUIController, StoreData, StoreMode
from src.application.use_cases import BuyFromNPCUseCase, SellToNPCUseCase
from src.application.interfaces import GameStateData
from src.game_core.entities import Player, Position, Stats


class TestStoreTransactionCallback:
    """Test that store transactions properly notify the game engine."""
    
    def setup_method(self):
        """Set up test fixtures."""
        # Initialize pygame for UI testing
        pygame.init()
        pygame.display.set_mode((800, 600))
        
        # Mock use cases
        self.buy_use_case = Mock(spec=BuyFromNPCUseCase)
        self.sell_use_case = Mock(spec=SellToNPCUseCase)
        
        # Create store UI controller
        self.store_ui = StoreUIController(self.buy_use_case, self.sell_use_case)
        
        # Create test player
        self.player = Player(
            id="test_player",
            name="<PERSON> Hero",
            position=Position(100, 100),
            asset_id="player.hero",
            stats=Stats(hp=100, max_hp=100, mp=50, max_mp=50, strength=10, defense=5, speed=8),
            size=(32, 32),
            level=1,
            experience=0,
            inventory={"gold_coin": 50, "bread": 2}
        )
        
        # Create test game state
        self.game_state = GameStateData(
            player=self.player,
            monsters={},
            items={},
            npcs={},
            current_level_id="test_level",
            collision_map=[],
            level_tiles=[]
        )
    
    def teardown_method(self):
        """Clean up after tests."""
        pygame.quit()
    
    def test_buy_transaction_calls_callback(self):
        """Test that buying an item calls the transaction callback."""
        # Set up store
        npc_inventory = {"bread": 10, "ale": 5}
        
        self.store_ui.show_store(
            npc_id="innkeeper_1",
            npc_name="Friendly Innkeeper",
            npc_type="innkeeper",
            npc_inventory=npc_inventory,
            player_inventory={"gold_coin": 50},
            player_gold=50
        )
        
        # Set up transaction callback
        callback_mock = Mock()
        self.store_ui.set_transaction_callback(callback_mock)
        
        # Mock successful purchase
        updated_player = Player(
            id="test_player",
            name="Test Hero",
            position=Position(100, 100),
            asset_id="player.hero",
            stats=Stats(hp=100, max_hp=100, mp=50, max_mp=50, strength=10, defense=5, speed=8),
            size=(32, 32),
            level=1,
            experience=0,
            inventory={"gold_coin": 48, "bread": 3}  # 2 gold spent, 1 bread added
        )
        
        updated_game_state = GameStateData(
            player=updated_player,
            monsters={},
            items={},
            npcs={},
            current_level_id="test_level",
            collision_map=[],
            level_tiles=[]
        )
        
        self.buy_use_case.execute.return_value = updated_game_state
        
        # Select an item and execute transaction
        self.store_ui.selected_item = "bread"
        self.store_ui._handle_item_transaction("bread", self.game_state)
        
        # Verify callback was called with updated game state
        callback_mock.assert_called_once_with(updated_game_state)
        
        # Verify the updated game state has the correct inventory
        called_game_state = callback_mock.call_args[0][0]
        assert called_game_state.player.inventory["gold_coin"] == 48
        assert called_game_state.player.inventory["bread"] == 3
    
    def test_sell_transaction_calls_callback(self):
        """Test that selling an item calls the transaction callback."""
        # Set up store in sell mode
        player_inventory = {"gold_coin": 50, "bread": 5}
        
        self.store_ui.show_store(
            npc_id="innkeeper_1",
            npc_name="Friendly Innkeeper",
            npc_type="innkeeper",
            npc_inventory={"bread": 10},
            player_inventory=player_inventory,
            player_gold=50
        )
        
        self.store_ui.current_mode = StoreMode.SELL
        
        # Set up transaction callback
        callback_mock = Mock()
        self.store_ui.set_transaction_callback(callback_mock)
        
        # Mock successful sale
        updated_player = Player(
            id="test_player",
            name="Test Hero",
            position=Position(100, 100),
            asset_id="player.hero",
            stats=Stats(hp=100, max_hp=100, mp=50, max_mp=50, strength=10, defense=5, speed=8),
            size=(32, 32),
            level=1,
            experience=0,
            inventory={"gold_coin": 51, "bread": 4}  # 1 gold gained, 1 bread sold
        )
        
        updated_game_state = GameStateData(
            player=updated_player,
            monsters={},
            items={},
            npcs={},
            current_level_id="test_level",
            collision_map=[],
            level_tiles=[]
        )
        
        self.sell_use_case.execute.return_value = updated_game_state
        
        # Select an item and execute transaction
        self.store_ui.selected_item = "bread"
        self.store_ui._handle_item_transaction("bread", self.game_state)
        
        # Verify callback was called with updated game state
        callback_mock.assert_called_once_with(updated_game_state)
        
        # Verify the updated game state has the correct inventory
        called_game_state = callback_mock.call_args[0][0]
        assert called_game_state.player.inventory["gold_coin"] == 51
        assert called_game_state.player.inventory["bread"] == 4
    
    def test_failed_transaction_does_not_call_callback(self):
        """Test that failed transactions don't call the callback."""
        # Set up store
        self.store_ui.show_store(
            npc_id="innkeeper_1",
            npc_name="Friendly Innkeeper",
            npc_type="innkeeper",
            npc_inventory={"bread": 10},
            player_inventory={"gold_coin": 50},
            player_gold=50
        )
        
        # Set up transaction callback
        callback_mock = Mock()
        self.store_ui.set_transaction_callback(callback_mock)
        
        # Mock failed purchase (exception)
        self.buy_use_case.execute.side_effect = ValueError("Not enough gold")
        
        # Select an item and execute transaction
        self.store_ui.selected_item = "bread"
        self.store_ui._handle_item_transaction("bread", self.game_state)
        
        # Verify callback was NOT called
        callback_mock.assert_not_called()
    
    def test_no_callback_set_does_not_crash(self):
        """Test that transactions work even when no callback is set."""
        # Set up store without setting callback
        self.store_ui.show_store(
            npc_id="innkeeper_1",
            npc_name="Friendly Innkeeper",
            npc_type="innkeeper",
            npc_inventory={"bread": 10},
            player_inventory={"gold_coin": 50},
            player_gold=50
        )
        
        # Mock successful purchase
        updated_game_state = GameStateData(
            player=self.player,
            monsters={},
            items={},
            npcs={},
            current_level_id="test_level",
            collision_map=[],
            level_tiles=[]
        )
        
        self.buy_use_case.execute.return_value = updated_game_state
        
        # This should not crash even without a callback
        self.store_ui.selected_item = "bread"
        self.store_ui._handle_item_transaction("bread", self.game_state)
        
        # Test passes if no exception is raised
