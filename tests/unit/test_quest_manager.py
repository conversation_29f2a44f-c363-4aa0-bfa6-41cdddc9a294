"""
Unit tests for the Quest Manager system.

Tests the core quest management functionality including quest registration,
state management, objective completion, and event emission.
"""

import pytest
import time
from unittest.mock import <PERSON><PERSON>, MagicMock

from src.game_core.quest_manager import GlobalQuestManager, QuestState, Quest, QuestObjective
from src.game_core.events import GameEvent
from src.infrastructure.events.pygame_event_bus import PygameEventBus


class TestGlobalQuestManager:
    """Test cases for the GlobalQuestManager class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.event_bus = PygameEventBus()
        self.quest_manager = GlobalQuestManager(self.event_bus)
        
        # Sample quest definition
        self.sample_quest = {
            "id": "test_quest",
            "name": "Test Quest",
            "description": "A test quest for unit testing",
            "objectives": ["objective_1", "objective_2"],
            "metadata": {"level": "test_level", "reward_gold": 10}
        }

    def test_quest_manager_initialization(self):
        """Test that quest manager initializes correctly."""
        assert self.quest_manager is not None
        assert self.quest_manager.event_bus == self.event_bus
        assert len(self.quest_manager._quests) == 0
        assert len(self.quest_manager._registered_quest_definitions) == 0

    def test_register_quest_success(self):
        """Test successful quest registration."""
        result = self.quest_manager.register_quest(self.sample_quest)
        
        assert result is True
        assert "test_quest" in self.quest_manager._quests
        assert "test_quest" in self.quest_manager._registered_quest_definitions
        
        quest = self.quest_manager._quests["test_quest"]
        assert quest.id == "test_quest"
        assert quest.name == "Test Quest"
        assert quest.description == "A test quest for unit testing"
        assert len(quest.objectives) == 2
        assert quest.state == QuestState.NOT_STARTED

    def test_register_quest_missing_id(self):
        """Test quest registration fails with missing ID."""
        invalid_quest = {"name": "Test", "description": "Test", "objectives": []}
        result = self.quest_manager.register_quest(invalid_quest)
        
        assert result is False
        assert len(self.quest_manager._quests) == 0

    def test_register_quest_missing_required_fields(self):
        """Test quest registration fails with missing required fields."""
        invalid_quest = {"id": "test", "name": "Test"}  # Missing description and objectives
        result = self.quest_manager.register_quest(invalid_quest)
        
        assert result is False
        assert len(self.quest_manager._quests) == 0

    def test_start_quest_success(self):
        """Test successful quest starting."""
        # Register quest first
        self.quest_manager.register_quest(self.sample_quest)
        
        # Track events
        events = []
        def track_event(event):
            events.append(event)
        self.event_bus.subscribe("quest_started", track_event)
        
        # Start quest
        result = self.quest_manager.start_quest("test_quest")
        
        assert result is True
        quest = self.quest_manager._quests["test_quest"]
        assert quest.state == QuestState.ACTIVE
        assert quest.start_time is not None
        
        # Check event was emitted
        assert len(events) == 1
        assert events[0].event_type == "quest_started"
        assert events[0].data["quest_id"] == "test_quest"

    def test_start_quest_not_found(self):
        """Test starting a quest that doesn't exist."""
        result = self.quest_manager.start_quest("nonexistent_quest")
        assert result is False

    def test_start_quest_already_started(self):
        """Test starting a quest that's already active."""
        self.quest_manager.register_quest(self.sample_quest)
        self.quest_manager.start_quest("test_quest")
        
        # Try to start again
        result = self.quest_manager.start_quest("test_quest")
        assert result is False

    def test_complete_objective_success(self):
        """Test successful objective completion."""
        # Register and start quest
        self.quest_manager.register_quest(self.sample_quest)
        self.quest_manager.start_quest("test_quest")
        
        # Track events
        events = []
        def track_event(event):
            events.append(event)
        self.event_bus.subscribe("objective_completed", track_event)
        self.event_bus.subscribe("quest_completed", track_event)
        
        # Complete first objective
        result = self.quest_manager.complete_objective("test_quest", "objective_1")
        
        assert result is True
        quest = self.quest_manager._quests["test_quest"]
        assert quest.get_objective("objective_1").completed is True
        assert quest.state == QuestState.ACTIVE  # Still active, not all objectives complete
        
        # Check objective completed event
        assert len(events) == 1
        assert events[0].event_type == "objective_completed"
        
        # Complete second objective
        result = self.quest_manager.complete_objective("test_quest", "objective_2")
        
        assert result is True
        assert quest.get_objective("objective_2").completed is True
        assert quest.state == QuestState.COMPLETED
        assert quest.completion_time is not None
        
        # Check quest completed event
        assert len(events) == 3  # objective_completed + quest_completed
        quest_completed_events = [e for e in events if e.event_type == "quest_completed"]
        assert len(quest_completed_events) == 1

    def test_complete_objective_quest_not_found(self):
        """Test completing objective for nonexistent quest."""
        result = self.quest_manager.complete_objective("nonexistent", "objective")
        assert result is False

    def test_complete_objective_quest_not_active(self):
        """Test completing objective for inactive quest."""
        self.quest_manager.register_quest(self.sample_quest)
        # Don't start the quest
        
        result = self.quest_manager.complete_objective("test_quest", "objective_1")
        assert result is False

    def test_complete_objective_not_found(self):
        """Test completing nonexistent objective."""
        self.quest_manager.register_quest(self.sample_quest)
        self.quest_manager.start_quest("test_quest")
        
        result = self.quest_manager.complete_objective("test_quest", "nonexistent_objective")
        assert result is False

    def test_get_quest_state(self):
        """Test getting quest state."""
        # Quest not registered
        assert self.quest_manager.get_quest_state("test_quest") is None
        
        # Register quest
        self.quest_manager.register_quest(self.sample_quest)
        assert self.quest_manager.get_quest_state("test_quest") == QuestState.NOT_STARTED
        
        # Start quest
        self.quest_manager.start_quest("test_quest")
        assert self.quest_manager.get_quest_state("test_quest") == QuestState.ACTIVE

    def test_get_active_quests(self):
        """Test getting active quests."""
        # No quests
        assert len(self.quest_manager.get_active_quests()) == 0
        
        # Register and start quest
        self.quest_manager.register_quest(self.sample_quest)
        assert len(self.quest_manager.get_active_quests()) == 0
        
        self.quest_manager.start_quest("test_quest")
        active_quests = self.quest_manager.get_active_quests()
        assert len(active_quests) == 1
        assert active_quests[0].id == "test_quest"

    def test_is_objective_completed(self):
        """Test checking if objective is completed."""
        self.quest_manager.register_quest(self.sample_quest)
        self.quest_manager.start_quest("test_quest")
        
        # Initially not completed
        assert self.quest_manager.is_objective_completed("test_quest", "objective_1") is False
        
        # Complete objective
        self.quest_manager.complete_objective("test_quest", "objective_1")
        assert self.quest_manager.is_objective_completed("test_quest", "objective_1") is True

    def test_quest_progress(self):
        """Test getting quest progress information."""
        self.quest_manager.register_quest(self.sample_quest)
        self.quest_manager.start_quest("test_quest")
        
        progress = self.quest_manager.get_quest_progress("test_quest")
        
        assert progress["quest_id"] == "test_quest"
        assert progress["name"] == "Test Quest"
        assert progress["state"] == "active"
        assert progress["completion_percentage"] == 0.0
        assert len(progress["objectives"]) == 2
        
        # Complete one objective
        self.quest_manager.complete_objective("test_quest", "objective_1")
        progress = self.quest_manager.get_quest_progress("test_quest")
        assert progress["completion_percentage"] == 50.0

    def test_save_load_quest_data(self):
        """Test saving and loading quest data."""
        # Register and start quest
        self.quest_manager.register_quest(self.sample_quest)
        self.quest_manager.start_quest("test_quest")
        self.quest_manager.complete_objective("test_quest", "objective_1")
        
        # Get save data
        save_data = self.quest_manager.get_all_quests_data()
        assert "test_quest" in save_data
        
        # Create new quest manager and load data
        new_quest_manager = GlobalQuestManager(self.event_bus)
        new_quest_manager.register_quest(self.sample_quest)  # Need to register first
        new_quest_manager.load_quests_data(save_data)
        
        # Verify loaded state
        assert new_quest_manager.get_quest_state("test_quest") == QuestState.ACTIVE
        assert new_quest_manager.is_objective_completed("test_quest", "objective_1") is True
        assert new_quest_manager.is_objective_completed("test_quest", "objective_2") is False

    def test_fail_quest(self):
        """Test failing a quest."""
        self.quest_manager.register_quest(self.sample_quest)
        self.quest_manager.start_quest("test_quest")
        
        # Track events
        events = []
        def track_event(event):
            events.append(event)
        self.event_bus.subscribe("quest_failed", track_event)
        
        result = self.quest_manager.fail_quest("test_quest")
        
        assert result is True
        quest = self.quest_manager._quests["test_quest"]
        assert quest.state == QuestState.FAILED
        
        # Check event was emitted
        assert len(events) == 1
        assert events[0].event_type == "quest_failed"


if __name__ == "__main__":
    pytest.main([__file__])
