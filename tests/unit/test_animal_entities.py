"""
Tests for the new animal entities and their wandering behavior.
"""

import pytest
from unittest.mock import Mock

from src.application.use_cases import CreateMonsterUseCase
from src.game_core.entities import Position


class TestAnimalEntities:
    """Test the new animal entities."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.event_bus = Mock()
        self.create_monster_use_case = CreateMonsterUseCase(self.event_bus)
    
    def test_create_horse(self):
        """Test creating a horse entity."""
        position = Position(100, 100)
        horse = self.create_monster_use_case.execute("horse", position)
        
        assert horse.name == "Wild Horse"
        assert horse.monster_type == "horse"
        assert horse.ai_behavior == "peaceful"
        assert horse.asset_id == "animal.horse.brown.side"
        assert horse.stats.hp == 12
        assert horse.stats.speed == 15
        assert horse.wander_behavior is not None
        assert horse.wander_behavior.enabled is True
        assert horse.wander_behavior.wander_radius == 5.0
        assert horse.wander_behavior.move_speed == 0.8
    
    def test_create_cow(self):
        """Test creating a cow entity."""
        position = Position(200, 200)
        cow = self.create_monster_use_case.execute("cow", position)
        
        assert cow.name == "Cow"
        assert cow.monster_type == "cow"
        assert cow.ai_behavior == "peaceful"
        assert cow.asset_id == "animal.cow.spotted.side"
        assert cow.stats.hp == 15
        assert cow.stats.speed == 6
        assert cow.wander_behavior is not None
        assert cow.wander_behavior.enabled is True
    
    def test_create_chicken(self):
        """Test creating a chicken entity."""
        position = Position(300, 300)
        chicken = self.create_monster_use_case.execute("chicken", position)
        
        assert chicken.name == "Chicken"
        assert chicken.monster_type == "chicken"
        assert chicken.ai_behavior == "peaceful"
        assert chicken.asset_id == "animal.chicken.white.side"
        assert chicken.stats.hp == 5
        assert chicken.stats.speed == 12
        assert chicken.wander_behavior is not None
        assert chicken.wander_behavior.enabled is True
    
    def test_create_deer(self):
        """Test creating a deer entity."""
        position = Position(400, 400)
        deer = self.create_monster_use_case.execute("deer", position)

        assert deer.name == "Deer"
        assert deer.monster_type == "deer"
        assert deer.ai_behavior == "skittish"
        assert deer.asset_id == "animal.deer.brown.side"
        assert deer.stats.hp == 8
        assert deer.stats.speed == 18
        assert deer.wander_behavior is not None
        assert deer.wander_behavior.enabled is True
    
    def test_create_pig(self):
        """Test creating a pig entity."""
        position = Position(500, 500)
        pig = self.create_monster_use_case.execute("pig", position)
        
        assert pig.name == "Pig"
        assert pig.monster_type == "pig"
        assert pig.ai_behavior == "peaceful"
        assert pig.asset_id == "animal.pig.pink.side"
        assert pig.stats.hp == 10
        assert pig.stats.speed == 8
        assert pig.wander_behavior is not None
        assert pig.wander_behavior.enabled is True
    
    def test_create_dog(self):
        """Test creating a dog entity."""
        position = Position(600, 600)
        dog = self.create_monster_use_case.execute("dog", position)
        
        assert dog.name == "Stray Dog"
        assert dog.monster_type == "dog"
        assert dog.ai_behavior == "peaceful"
        assert dog.asset_id == "animal.dog.brown.side"
        assert dog.stats.hp == 7
        assert dog.stats.speed == 14
        assert dog.wander_behavior is not None
        assert dog.wander_behavior.enabled is True
    
    def test_all_animals_have_peaceful_behavior(self):
        """Test that most animals have peaceful AI behavior, except deer which is skittish."""
        peaceful_animals = ["horse", "cow", "chicken", "pig", "dog"]
        skittish_animals = ["deer"]
        position = Position(100, 100)

        for animal_id in peaceful_animals:
            animal = self.create_monster_use_case.execute(animal_id, position)
            assert animal.ai_behavior == "peaceful", f"{animal_id} should have peaceful behavior"

        for animal_id in skittish_animals:
            animal = self.create_monster_use_case.execute(animal_id, position)
            assert animal.ai_behavior == "skittish", f"{animal_id} should have skittish behavior"
            assert animal.wander_behavior is not None, f"{animal_id} should have wander behavior"
            assert animal.wander_behavior.enabled is True, f"{animal_id} should have wander enabled"
    
    def test_custom_stats_override(self):
        """Test that custom stats can override default values."""
        position = Position(100, 100)
        custom_stats = {"hp": 100, "max_hp": 100, "speed": 20}
        
        horse = self.create_monster_use_case.execute("horse", position, custom_stats=custom_stats)
        
        assert horse.stats.hp == 100
        assert horse.stats.max_hp == 100
        assert horse.stats.speed == 20
        # Other stats should remain default
        assert horse.stats.strength == 8  # Default from monster definition
        assert horse.stats.defense == 0   # Default from monster definition
    
    def test_custom_name_override(self):
        """Test that custom names can override default values."""
        position = Position(100, 100)
        custom_name = "My Pet Horse"
        
        horse = self.create_monster_use_case.execute("horse", position, name=custom_name)
        
        assert horse.name == "My Pet Horse"
        assert horse.monster_type == "horse"  # Type should remain the same
