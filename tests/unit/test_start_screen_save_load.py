"""
Unit tests for the start screen save/load system.

This module tests the complete start screen functionality including:
- SettingsUIController (main menu)
- SaveSlotUIController (save slot selection)
- NewGameUIController (new game creation)
- HelpUIController (help panel)
- Save/load integration
- Event handling and UI state management
"""

import pytest
import pygame
from unittest.mock import Mock, MagicMock, patch
from pathlib import Path
import json
import tempfile
import os

from src.presentation.ui import (
    SettingsUIController, SaveSlotUIController, NewGameUIController, 
    HelpUIController, SettingsData, SaveSlotData, NewGameData, 
    HelpData, GameState
)
from src.infrastructure.repositories.json_save_game_repository import JsonSaveGameRepository
from src.application.interfaces import GameStateData
from src.game_core.entities import Player, NPC, Position, Stats


class TestSettingsUIController:
    """Test the main settings/menu UI controller."""
    
    def setup_method(self):
        """Set up test fixtures."""
        pygame.init()
        pygame.display.set_mode((800, 600))
        
        self.save_repository = Mock(spec=JsonSaveGameRepository)
        self.settings_ui = SettingsUIController(self.save_repository)
        
        # Set up callbacks
        self.on_new_game = Mock()
        self.on_load_game = Mock()
        self.on_save_game = Mock()
        self.on_exit_game = Mock()
        
        self.settings_ui.on_new_game = self.on_new_game
        self.settings_ui.on_load_game = self.on_load_game
        self.settings_ui.on_save_game = self.on_save_game
        self.settings_ui.on_exit_game = self.on_exit_game
    
    def teardown_method(self):
        """Clean up after tests."""
        pygame.quit()
    
    def test_settings_ui_initialization(self):
        """Test that settings UI initializes correctly."""
        assert self.settings_ui is not None
        assert not self.settings_ui.is_settings_visible()
        assert self.settings_ui.settings_data.game_state == GameState.MENU
        assert self.settings_ui.selected_button == 0
        assert len(self.settings_ui.buttons) == 5  # New, Load, Save, Help, Exit
    
    def test_show_hide_settings(self):
        """Test showing and hiding settings interface."""
        # Initially hidden
        assert not self.settings_ui.is_settings_visible()
        
        # Show settings
        self.settings_ui.show_settings(GameState.MENU, "slot_1")
        assert self.settings_ui.is_settings_visible()
        assert self.settings_ui.settings_data.game_state == GameState.MENU
        assert self.settings_ui.settings_data.current_save_slot == "slot_1"
        
        # Hide settings
        self.settings_ui.hide_settings()
        assert not self.settings_ui.is_settings_visible()
    
    def test_keyboard_navigation(self):
        """Test keyboard navigation through menu buttons."""
        self.settings_ui.show_settings()
        
        # Test down arrow navigation
        event = pygame.event.Event(pygame.KEYDOWN, key=pygame.K_DOWN)
        self.settings_ui.handle_event(event)
        assert self.settings_ui.selected_button == 1
        
        # Test up arrow navigation (wrap around)
        self.settings_ui.selected_button = 0
        event = pygame.event.Event(pygame.KEYDOWN, key=pygame.K_UP)
        self.settings_ui.handle_event(event)
        assert self.settings_ui.selected_button == 4  # Last button
        
        # Test down arrow wrap around
        self.settings_ui.selected_button = 4
        event = pygame.event.Event(pygame.KEYDOWN, key=pygame.K_DOWN)
        self.settings_ui.handle_event(event)
        assert self.settings_ui.selected_button == 0  # First button
    
    def test_button_actions(self):
        """Test that button actions trigger correct callbacks."""
        # Test New Game button
        self.settings_ui.show_settings()
        self.settings_ui.selected_button = 0
        event = pygame.event.Event(pygame.KEYDOWN, key=pygame.K_RETURN)
        self.settings_ui.handle_event(event)
        self.on_new_game.assert_called_once()

        # Test Load Game button (need to show settings again since new game hides it)
        self.settings_ui.show_settings()
        self.settings_ui.selected_button = 1
        event = pygame.event.Event(pygame.KEYDOWN, key=pygame.K_RETURN)
        self.settings_ui.handle_event(event)
        self.on_load_game.assert_called_once_with("load_request")

        # Test Save Game button
        self.settings_ui.show_settings()
        self.settings_ui.settings_data.current_save_slot = "slot_3"
        self.settings_ui.selected_button = 2
        event = pygame.event.Event(pygame.KEYDOWN, key=pygame.K_RETURN)
        self.settings_ui.handle_event(event)
        self.on_save_game.assert_called_once_with("slot_3")

        # Test Exit Game button
        self.settings_ui.show_settings()
        self.settings_ui.selected_button = 4
        event = pygame.event.Event(pygame.KEYDOWN, key=pygame.K_RETURN)
        self.settings_ui.handle_event(event)
        self.on_exit_game.assert_called_once()
    
    def test_help_panel_toggle(self):
        """Test help panel showing and hiding."""
        self.settings_ui.show_settings()
        
        # Show help
        self.settings_ui.selected_button = 3  # Help button
        event = pygame.event.Event(pygame.KEYDOWN, key=pygame.K_RETURN)
        self.settings_ui.handle_event(event)
        assert self.settings_ui.show_help is True
        
        # Hide help with escape
        event = pygame.event.Event(pygame.KEYDOWN, key=pygame.K_ESCAPE)
        self.settings_ui.handle_event(event)
        assert self.settings_ui.show_help is False
    
    def test_mouse_interaction(self):
        """Test mouse interaction with buttons."""
        self.settings_ui.show_settings()

        # Test that mouse events are handled when settings are visible
        # We'll test the _handle_click method directly since mouse position calculation is complex
        result = self.settings_ui._handle_click((400, 300))  # Some position

        # Even if click doesn't hit a button, it should return False (not consume event)
        # But if settings are visible, the main handle_event should return False for mouse events outside buttons
        event = pygame.event.Event(pygame.MOUSEBUTTONDOWN, button=1, pos=(10, 10))  # Outside any button
        result = self.settings_ui.handle_event(event)

        # Should not consume the event if click is outside buttons
        assert result is False


class TestSaveSlotUIController:
    """Test the save slot selection UI controller."""
    
    def setup_method(self):
        """Set up test fixtures."""
        pygame.init()
        pygame.display.set_mode((800, 600))
        
        self.save_repository = Mock(spec=JsonSaveGameRepository)
        self.save_slot_ui = SaveSlotUIController(self.save_repository)
        
        # Set up callbacks
        self.on_slot_selected = Mock()
        self.on_cancel = Mock()
        
        self.save_slot_ui.on_slot_selected = self.on_slot_selected
        self.save_slot_ui.on_cancel = self.on_cancel
    
    def teardown_method(self):
        """Clean up after tests."""
        pygame.quit()
    
    def test_save_slot_ui_initialization(self):
        """Test that save slot UI initializes correctly."""
        assert self.save_slot_ui is not None
        assert not self.save_slot_ui.is_visible()
        assert self.save_slot_ui.slot_data.mode == "load"
        assert self.save_slot_ui.slot_data.selected_slot is None
    
    def test_show_hide_save_slots(self):
        """Test showing and hiding save slot interface."""
        # Initially hidden
        assert not self.save_slot_ui.is_visible()
        
        # Show in load mode
        self.save_slot_ui.show_save_slots("load")
        assert self.save_slot_ui.is_visible()
        assert self.save_slot_ui.slot_data.mode == "load"
        
        # Show in save mode
        self.save_slot_ui.show_save_slots("save")
        assert self.save_slot_ui.is_visible()
        assert self.save_slot_ui.slot_data.mode == "save"
        
        # Hide
        self.save_slot_ui.hide_save_slots()
        assert not self.save_slot_ui.is_visible()
    
    def test_slot_selection_with_enter_key(self):
        """Test slot selection and loading with Enter key."""
        # Mock save data
        save_info = {
            "player_name": "Test Hero",
            "level_name": "Town Square",
            "timestamp": "2024-01-01T12:00:00"
        }
        self.save_repository.get_save_info.return_value = save_info
        
        self.save_slot_ui.show_save_slots("load")
        self.save_slot_ui.slot_data.selected_slot = 3
        self.save_slot_ui.slot_data.selected_slot_data = save_info
        
        # Press Enter to load
        event = pygame.event.Event(pygame.KEYDOWN, key=pygame.K_RETURN)
        self.save_slot_ui.handle_event(event)
        
        self.on_slot_selected.assert_called_once_with("slot_3")
        assert not self.save_slot_ui.is_visible()  # Should hide after selection
    
    def test_escape_key_cancellation(self):
        """Test cancellation with Escape key."""
        self.save_slot_ui.show_save_slots("load")
        
        event = pygame.event.Event(pygame.KEYDOWN, key=pygame.K_ESCAPE)
        self.save_slot_ui.handle_event(event)
        
        self.on_cancel.assert_called_once()
        assert not self.save_slot_ui.is_visible()
    
    def test_save_mode_functionality(self):
        """Test save mode functionality."""
        self.save_slot_ui.show_save_slots("save")
        self.save_slot_ui.slot_data.selected_slot = 5
        
        # Press Enter to save
        event = pygame.event.Event(pygame.KEYDOWN, key=pygame.K_RETURN)
        self.save_slot_ui.handle_event(event)
        
        self.on_slot_selected.assert_called_once_with("slot_5")
        assert not self.save_slot_ui.is_visible()


class TestNewGameUIController:
    """Test the new game creation UI controller."""
    
    def setup_method(self):
        """Set up test fixtures."""
        pygame.init()
        pygame.display.set_mode((800, 600))
        
        self.save_repository = Mock(spec=JsonSaveGameRepository)
        self.new_game_ui = NewGameUIController(self.save_repository)
        
        # Set up callbacks
        self.on_new_game_created = Mock()
        self.on_cancel = Mock()
        
        self.new_game_ui.on_new_game_created = self.on_new_game_created
        self.new_game_ui.on_cancel = self.on_cancel
    
    def teardown_method(self):
        """Clean up after tests."""
        pygame.quit()
    
    def test_new_game_ui_initialization(self):
        """Test that new game UI initializes correctly."""
        assert self.new_game_ui is not None
        assert not self.new_game_ui.is_visible()
        assert self.new_game_ui.new_game_data.selected_slot is None
        assert self.new_game_ui.new_game_data.game_name == ""
    
    def test_show_hide_new_game(self):
        """Test showing and hiding new game interface."""
        # Initially hidden
        assert not self.new_game_ui.is_visible()
        
        # Show new game UI
        self.new_game_ui.show_new_game()
        assert self.new_game_ui.is_visible()
        assert self.new_game_ui.new_game_data.selected_slot is None
        assert self.new_game_ui.new_game_data.game_name == ""
        
        # Hide new game UI
        self.new_game_ui.hide_new_game()
        assert not self.new_game_ui.is_visible()
    
    def test_slot_selection_with_number_keys(self):
        """Test slot selection using number keys."""
        self.new_game_ui.show_new_game()
        
        # Test number key 1-9
        for i in range(1, 10):
            key = getattr(pygame, f'K_{i}')
            event = pygame.event.Event(pygame.KEYDOWN, key=key)
            self.new_game_ui.handle_event(event)
            assert self.new_game_ui.new_game_data.selected_slot == i
        
        # Test number key 0 (should map to slot 10)
        event = pygame.event.Event(pygame.KEYDOWN, key=pygame.K_0)
        self.new_game_ui.handle_event(event)
        assert self.new_game_ui.new_game_data.selected_slot == 10
    
    def test_text_input_for_game_name(self):
        """Test text input for game name."""
        self.new_game_ui.show_new_game()
        self.new_game_ui.new_game_data.input_active = True
        
        # Test character input
        event = pygame.event.Event(pygame.KEYDOWN, key=pygame.K_h, unicode='H')
        self.new_game_ui.handle_event(event)
        assert self.new_game_ui.new_game_data.game_name == "H"
        
        event = pygame.event.Event(pygame.KEYDOWN, key=pygame.K_e, unicode='e')
        self.new_game_ui.handle_event(event)
        assert self.new_game_ui.new_game_data.game_name == "He"
        
        # Test backspace
        event = pygame.event.Event(pygame.KEYDOWN, key=pygame.K_BACKSPACE)
        self.new_game_ui.handle_event(event)
        assert self.new_game_ui.new_game_data.game_name == "H"
    
    def test_new_game_creation(self):
        """Test new game creation with Enter key."""
        self.new_game_ui.show_new_game()
        self.new_game_ui.new_game_data.selected_slot = 3
        self.new_game_ui.new_game_data.game_name = "Test Hero"
        
        # Press Enter to create game
        event = pygame.event.Event(pygame.KEYDOWN, key=pygame.K_RETURN)
        self.new_game_ui.handle_event(event)
        
        self.on_new_game_created.assert_called_once_with("slot_3", "Test Hero")
        assert not self.new_game_ui.is_visible()
    
    def test_input_focus_toggle(self):
        """Test input focus toggle with Tab key."""
        self.new_game_ui.show_new_game()
        
        # Initially input not active
        assert not self.new_game_ui.new_game_data.input_active
        
        # Press Tab to activate input
        event = pygame.event.Event(pygame.KEYDOWN, key=pygame.K_TAB)
        self.new_game_ui.handle_event(event)
        assert self.new_game_ui.new_game_data.input_active
        
        # Press Tab again to deactivate
        event = pygame.event.Event(pygame.KEYDOWN, key=pygame.K_TAB)
        self.new_game_ui.handle_event(event)
        assert not self.new_game_ui.new_game_data.input_active


class TestHelpUIController:
    """Test the help panel UI controller."""

    def setup_method(self):
        """Set up test fixtures."""
        pygame.init()
        pygame.display.set_mode((800, 600))

        self.help_ui = HelpUIController()

        # Set up callback
        self.on_close = Mock()
        self.help_ui.on_close = self.on_close

    def teardown_method(self):
        """Clean up after tests."""
        pygame.quit()

    def test_help_ui_initialization(self):
        """Test that help UI initializes correctly."""
        assert self.help_ui is not None
        assert not self.help_ui.is_visible()

    def test_show_hide_help(self):
        """Test showing and hiding help interface."""
        # Initially hidden
        assert not self.help_ui.is_visible()

        # Show help
        self.help_ui.show_help()
        assert self.help_ui.is_visible()

        # Hide help
        self.help_ui.hide_help()
        assert not self.help_ui.is_visible()

    def test_escape_key_closes_help(self):
        """Test that Escape key closes help panel."""
        self.help_ui.show_help()

        event = pygame.event.Event(pygame.KEYDOWN, key=pygame.K_ESCAPE)
        self.help_ui.handle_event(event)

        self.on_close.assert_called_once()
        assert not self.help_ui.is_visible()


class TestSaveGameRepository:
    """Test the save game repository functionality."""

    def setup_method(self):
        """Set up test fixtures."""
        # Create temporary directory for save files
        self.temp_dir = tempfile.mkdtemp()
        self.save_repository = JsonSaveGameRepository()
        self.save_repository.save_directory = Path(self.temp_dir)

        # Create test player (use "gold" not "gold_coin" to match save repository expectations)
        self.test_player = Player(
            id="test_player",
            name="Test Hero",
            position=Position(100, 100),
            asset_id="player.hero",
            stats=Stats(hp=100, max_hp=100, mp=50, max_mp=50, strength=10, defense=5, speed=8),
            size=(32, 32),
            level=5,
            experience=1250,
            inventory={"gold": 150, "bread": 3, "sword": 1}
        )

        # Create test NPC
        self.test_npc = NPC(
            id="merchant_1",
            name="Test Merchant",
            position=Position(200, 200),
            asset_id="npc.merchant.general",
            npc_type="merchant",
            behavior="store",
            dialog=["Welcome to my shop!", "What can I get for you?"],
            inventory={"health_potion": 10, "bread": 5},
            properties={"shop_level": 2}
        )

        # Create test game state
        self.test_game_state = GameStateData(
            player=self.test_player,
            monsters={},
            items={},
            npcs={"merchant_1": self.test_npc},
            current_level_id="town_square",
            collision_map=[],
            level_tiles=[]
        )

    def teardown_method(self):
        """Clean up after tests."""
        # Remove temporary directory
        import shutil
        shutil.rmtree(self.temp_dir)

    def test_save_game_creates_file(self):
        """Test that saving a game creates a save file."""
        self.save_repository.save_game(self.test_game_state, "test_slot")

        save_file = Path(self.temp_dir) / "test_slot.json"
        assert save_file.exists()

        # Verify file contents
        with open(save_file, 'r') as f:
            save_data = json.load(f)

        assert "metadata" in save_data
        assert save_data["metadata"]["save_slot"] == "test_slot"
        assert save_data["metadata"]["player_name"] == "Test Hero"
        assert save_data["metadata"]["player_level"] == 5
        assert save_data["metadata"]["player_gold"] == 150

    def test_load_game_returns_correct_data(self):
        """Test that loading a game returns correct game state."""
        # First save a game
        self.save_repository.save_game(self.test_game_state, "test_slot")

        # Then load it
        loaded_game_state = self.save_repository.load_game("test_slot")

        assert loaded_game_state is not None
        assert loaded_game_state.player.name == "Test Hero"
        assert loaded_game_state.player.level == 5
        assert loaded_game_state.player.inventory["gold"] == 150
        assert loaded_game_state.current_level_id == "town_square"

        # Verify NPCs are not saved (they should be loaded fresh from level files)
        assert len(loaded_game_state.npcs) == 0

    def test_load_nonexistent_game_returns_none(self):
        """Test that loading a nonexistent game returns None."""
        result = self.save_repository.load_game("nonexistent_slot")
        assert result is None

    def test_get_save_info_returns_metadata(self):
        """Test that get_save_info returns correct metadata."""
        # Save a game first
        self.save_repository.save_game(self.test_game_state, "test_slot")

        # Get save info
        save_info = self.save_repository.get_save_info("test_slot")

        assert save_info is not None
        assert save_info["save_slot"] == "test_slot"
        assert save_info["player_name"] == "Test Hero"
        assert save_info["player_level"] == 5
        assert save_info["player_gold"] == 150
        assert "timestamp" in save_info
        assert "file_size" in save_info

    def test_list_save_slots_returns_available_slots(self):
        """Test that list_save_slots returns available save slots."""
        # Create multiple save files
        self.save_repository.save_game(self.test_game_state, "slot_1")
        self.save_repository.save_game(self.test_game_state, "slot_3")
        self.save_repository.save_game(self.test_game_state, "slot_7")

        # List save slots (returns all 10 slots, some with data, some None)
        save_slots = self.save_repository.list_save_slots()

        assert len(save_slots) == 10  # Always returns 10 slots
        assert save_slots["slot_1"] is not None  # Has data
        assert save_slots["slot_3"] is not None  # Has data
        assert save_slots["slot_7"] is not None  # Has data
        assert save_slots["slot_2"] is None      # No data
        assert save_slots["slot_4"] is None      # No data

    def test_delete_save_slot_removes_file(self):
        """Test that deleting a save slot removes the file."""
        # Save a game first
        self.save_repository.save_game(self.test_game_state, "test_slot")
        save_file = Path(self.temp_dir) / "test_slot.json"
        assert save_file.exists()

        # Delete the save slot
        self.save_repository.delete_save_slot("test_slot")
        assert not save_file.exists()

    def test_npc_serialization_preserves_state(self):
        """Test that NPC state is properly preserved during save/load."""
        # Modify NPC inventory to test state preservation
        modified_npc = self.test_npc.add_item_to_stock("mana_potion", 3)
        modified_npc = modified_npc.remove_item_from_stock("bread", 2)

        # Update game state with modified NPC
        modified_game_state = GameStateData(
            player=self.test_player,
            monsters={},
            items={},
            npcs={"merchant_1": modified_npc},
            current_level_id="town_square",
            collision_map=[],
            level_tiles=[]
        )

        # Save the game
        self.save_repository.save_game(modified_game_state, "npc_test_slot")

        # Load the game
        loaded_game_state = self.save_repository.load_game("npc_test_slot")

        # Verify NPCs are not saved (they should be loaded fresh from level files)
        assert loaded_game_state is not None
        assert len(loaded_game_state.npcs) == 0

        # NPCs will be loaded fresh from level files, so inventory changes are not preserved
        # This matches the behavior of level transitions and keeps the game consistent


class TestStartScreenIntegration:
    """Integration tests for the complete start screen system."""

    def setup_method(self):
        """Set up test fixtures."""
        pygame.init()
        pygame.display.set_mode((800, 600))

        # Create temporary directory for save files
        self.temp_dir = tempfile.mkdtemp()
        self.save_repository = JsonSaveGameRepository()
        self.save_repository.save_directory = Path(self.temp_dir)

        # Create UI controllers
        self.settings_ui = SettingsUIController(self.save_repository)
        self.save_slot_ui = SaveSlotUIController(self.save_repository)
        self.new_game_ui = NewGameUIController(self.save_repository)
        self.help_ui = HelpUIController()

        # Track callback calls
        self.callback_calls = []

        # Set up callbacks to track interactions
        self.settings_ui.on_new_game = lambda: self.callback_calls.append("new_game")
        self.settings_ui.on_load_game = lambda x: self.callback_calls.append(f"load_game:{x}")
        self.settings_ui.on_save_game = lambda x: self.callback_calls.append(f"save_game:{x}")
        self.settings_ui.on_exit_game = lambda: self.callback_calls.append("exit_game")

        self.save_slot_ui.on_slot_selected = lambda x: self.callback_calls.append(f"slot_selected:{x}")
        self.save_slot_ui.on_cancel = lambda: self.callback_calls.append("slot_cancel")

        self.new_game_ui.on_new_game_created = lambda x, y: self.callback_calls.append(f"new_game_created:{x}:{y}")
        self.new_game_ui.on_cancel = lambda: self.callback_calls.append("new_game_cancel")

        self.help_ui.on_close = lambda: self.callback_calls.append("help_close")

    def teardown_method(self):
        """Clean up after tests."""
        pygame.quit()
        import shutil
        shutil.rmtree(self.temp_dir)

    def test_complete_new_game_flow(self):
        """Test the complete new game creation flow."""
        # 1. Show settings menu
        self.settings_ui.show_settings()
        assert self.settings_ui.is_settings_visible()

        # 2. Select "New Game" button and press Enter
        self.settings_ui.selected_button = 0  # New Game
        event = pygame.event.Event(pygame.KEYDOWN, key=pygame.K_RETURN)
        self.settings_ui.handle_event(event)

        # 3. Verify new game callback was triggered
        assert "new_game" in self.callback_calls

        # 4. Show new game UI (simulating game engine response)
        self.new_game_ui.show_new_game()
        assert self.new_game_ui.is_visible()

        # 5. Select slot and enter name
        self.new_game_ui.new_game_data.selected_slot = 2
        self.new_game_ui.new_game_data.game_name = "Hero Adventure"

        # 6. Create the game
        event = pygame.event.Event(pygame.KEYDOWN, key=pygame.K_RETURN)
        self.new_game_ui.handle_event(event)

        # 7. Verify new game creation callback
        assert "new_game_created:slot_2:Hero Adventure" in self.callback_calls
        assert not self.new_game_ui.is_visible()

    def test_complete_load_game_flow(self):
        """Test the complete load game flow."""
        # First create a save file (use "gold" not "gold_coin")
        test_player = Player(
            id="test_player", name="Saved Hero", position=Position(50, 50),
            asset_id="player.hero", stats=Stats(hp=80, max_hp=100, mp=30, max_mp=50, strength=12, defense=6, speed=9),
            size=(32, 32), level=3, experience=750, inventory={"gold": 75}
        )
        test_game_state = GameStateData(
            player=test_player, monsters={}, items={}, npcs={},
            current_level_id="forest", collision_map=[], level_tiles=[]
        )
        self.save_repository.save_game(test_game_state, "slot_5")

        # 1. Show settings menu
        self.settings_ui.show_settings()

        # 2. Select "Load Game" and press Enter
        self.settings_ui.selected_button = 1  # Load Game
        event = pygame.event.Event(pygame.KEYDOWN, key=pygame.K_RETURN)
        self.settings_ui.handle_event(event)

        # 3. Verify load game callback
        assert "load_game:load_request" in self.callback_calls

        # 4. Show save slot UI (simulating game engine response)
        self.save_slot_ui.show_save_slots("load")
        assert self.save_slot_ui.is_visible()

        # 5. Select a slot with save data
        self.save_slot_ui.slot_data.selected_slot = 5
        self.save_slot_ui.slot_data.selected_slot_data = self.save_repository.get_save_info("slot_5")

        # 6. Load the game
        event = pygame.event.Event(pygame.KEYDOWN, key=pygame.K_RETURN)
        self.save_slot_ui.handle_event(event)

        # 7. Verify slot selection callback
        assert "slot_selected:slot_5" in self.callback_calls
        assert not self.save_slot_ui.is_visible()

    def test_help_system_integration(self):
        """Test the help system integration."""
        # 1. Show settings menu
        self.settings_ui.show_settings()

        # 2. Select Help button
        self.settings_ui.selected_button = 3  # Help
        event = pygame.event.Event(pygame.KEYDOWN, key=pygame.K_RETURN)
        self.settings_ui.handle_event(event)

        # 3. Verify help is shown
        assert self.settings_ui.show_help is True

        # 4. Close help with Escape
        event = pygame.event.Event(pygame.KEYDOWN, key=pygame.K_ESCAPE)
        self.settings_ui.handle_event(event)

        # 5. Verify help is hidden
        assert self.settings_ui.show_help is False

    def test_context_aware_escape_key(self):
        """Test context-aware escape key behavior."""
        # Test escape in new game UI
        self.new_game_ui.show_new_game()
        event = pygame.event.Event(pygame.KEYDOWN, key=pygame.K_ESCAPE)
        self.new_game_ui.handle_event(event)

        assert "new_game_cancel" in self.callback_calls
        assert not self.new_game_ui.is_visible()

        # Test escape in save slot UI
        self.save_slot_ui.show_save_slots("load")
        event = pygame.event.Event(pygame.KEYDOWN, key=pygame.K_ESCAPE)
        self.save_slot_ui.handle_event(event)

        assert "slot_cancel" in self.callback_calls
        assert not self.save_slot_ui.is_visible()
