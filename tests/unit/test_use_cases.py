"""
Unit tests for application layer use cases.

These tests verify the use case logic without external dependencies.
"""

import pytest
import time
from unittest.mock import Mock, patch

from src.application import (
    MovePlayerUseCase, PlayerAttackUseCase, BuildLevelUseCase, GameStateData
)
from src.game_core import Player, Monster, Position, Direction, Vector2, Stats
from src.game_core.config import get_config, initialize_config


class TestMovePlayerUseCase:
    """Test the MovePlayerUseCase."""

    def setup_method(self):
        """Initialize config before each test."""
        from pathlib import Path
        config_path = Path(__file__).parent.parent.parent / "game_config.yaml"
        initialize_config(str(config_path))

    def test_move_player_valid_move(self):
        # Setup
        event_bus = Mock()
        config = get_config()
        use_case = MovePlayerUseCase(
            event_bus,
            tile_size=config.rendering.tile_size,
            move_speed=config.movement.player_move_speed * config.rendering.tile_size
        )
        
        # Create game state with player
        player = Player(
            id="player1",
            name="Hero",
            position=Position(192, 192),  # Center of tile (1,1) with tile_size=128
            asset_id="player.hero",
            stats=Stats(hp=100, max_hp=100, mp=50, max_mp=50,
                       strength=10, defense=5, speed=8),
            size=(32, 32),
            level=1,
            experience=0
        )
        
        # 3x3 empty map
        collision_map = [
            [False, False, False],
            [False, False, False],
            [False, False, False]
        ]
        
        game_state = GameStateData(
            player=player,
            monsters={},
            items={},
            current_level_id="test_level",
            collision_map=collision_map
        )
        
        # Execute
        result = use_case.execute(game_state, Direction.NORTH, 0.016)  # 60 FPS delta time
        
        # Verify
        assert result.player is not None
        # With pixel-based movement, player should have moved slightly north
        # move_speed = config.movement.player_move_speed * config.rendering.tile_size
        # move_speed = 6.67 * 128 = 854.56, dt=0.016, distance = 854.56 * 0.016 = 13.67 pixels north
        expected_distance = config.movement.player_move_speed * config.rendering.tile_size * 0.016
        assert result.player.position.x == 192  # X should remain the same
        assert abs(result.player.position.y - (192 - expected_distance)) < 0.1  # Y should decrease by expected distance

        # Verify event was published
        event_bus.publish.assert_called_once()
        published_event = event_bus.publish.call_args[0][0]
        assert published_event.event_type == "player_moved"
        assert published_event.old_position == Position(192, 192)
        assert abs(published_event.new_position.y - (192 - expected_distance)) < 0.1  # Moved north by expected distance
    
    def test_move_player_blocked_move(self):
        # Setup
        event_bus = Mock()
        config = get_config()
        use_case = MovePlayerUseCase(
            event_bus,
            tile_size=config.rendering.tile_size,
            move_speed=config.movement.player_move_speed * config.rendering.tile_size
        )
        
        player = Player(
            id="player1",
            name="Hero",
            position=Position(192, 192),  # Center of tile (1,1) with tile_size=128
            asset_id="player.hero",
            stats=Stats(hp=100, max_hp=100, mp=50, max_mp=50,
                       strength=10, defense=5, speed=8),
            size=(32, 32),
            level=1,
            experience=0
        )
        
        # Map with wall to the north
        collision_map = [
            [False, True, False],   # Wall at (1,0)
            [False, False, False],
            [False, False, False]
        ]
        
        game_state = GameStateData(
            player=player,
            monsters={},
            items={},
            current_level_id="test_level",
            collision_map=collision_map
        )
        
        # Execute with larger movement to cross tile boundary
        result = use_case.execute(game_state, Direction.NORTH, 0.5)  # Large dt to move ~100 pixels
        
        # Verify - player should NOT move because there's a wall at (1,0)
        assert result.player is not None
        assert result.player.position == Position(192, 192)  # Position unchanged due to collision
        
        # Verify no event was published
        event_bus.publish.assert_not_called()
    
    def test_move_player_no_player(self):
        # Setup
        event_bus = Mock()
        config = get_config()
        use_case = MovePlayerUseCase(
            event_bus,
            tile_size=config.rendering.tile_size,
            move_speed=config.movement.player_move_speed * config.rendering.tile_size
        )
        
        game_state = GameStateData(
            player=None,  # No player
            monsters={},
            items={},
            current_level_id="test_level",
            collision_map=[]
        )
        
        # Execute
        result = use_case.execute(game_state, Direction.NORTH, 0.016)  # 60 FPS delta time
        
        # Verify - state unchanged
        assert result.player is None
        event_bus.publish.assert_not_called()


class TestPlayerAttackUseCase:
    """Test the PlayerAttackUseCase."""
    
    def test_player_attack_hits_monster(self):
        # Setup
        event_bus = Mock()
        use_case = PlayerAttackUseCase(event_bus)
        
        player = Player(
            id="player1",
            name="Hero",
            position=Position(5, 5),
            asset_id="player.hero",
            stats=Stats(hp=100, max_hp=100, mp=50, max_mp=50,
                       strength=10, defense=5, speed=8),
            size=(32, 32),
            level=1,
            experience=0
        )
        
        monster = Monster(
            id="goblin1",
            name="Goblin",
            position=Position(6, 5),  # Adjacent to player
            asset_id="monster.goblin",
            stats=Stats(hp=30, max_hp=30, mp=0, max_mp=0,
                       strength=6, defense=2, speed=5),
            monster_type="goblin",
            experience_reward=15
        )
        
        game_state = GameStateData(
            player=player,
            monsters={"goblin1": monster},
            items={},
            current_level_id="test_level",
            collision_map=[]
        )
        
        # Mock the random function to ensure the attack hits
        with patch('src.game_core.game_logic.random.random', return_value=0.5):  # 50% < 81% hit chance
            # Execute attack to the east
            direction = Vector2(1.0, 0.0)
            result_game_state, attacked_monster_ids = use_case.execute(game_state, direction)

            # Verify - monster should be damaged
            assert "goblin1" in result_game_state.monsters
            damaged_monster = result_game_state.monsters["goblin1"]
            assert damaged_monster.stats.hp < 30  # Should be damaged
            
            # Verify events were published
            assert event_bus.publish.call_count >= 2  # Attack event + damage event
            
            # Check that attack event was published
            attack_event = event_bus.publish.call_args_list[0][0][0]
            assert attack_event.event_type == "player_attacked"
            assert attack_event.direction_vector == direction
    
    def test_player_attack_no_targets(self):
        # Setup
        event_bus = Mock()
        use_case = PlayerAttackUseCase(event_bus)
        
        player = Player(
            id="player1",
            name="Hero",
            position=Position(5, 5),
            asset_id="player.hero",
            stats=Stats(hp=100, max_hp=100, mp=50, max_mp=50,
                       strength=10, defense=5, speed=8),
            size=(32, 32),
            level=1,
            experience=0
        )
        
        game_state = GameStateData(
            player=player,
            monsters={},  # No monsters
            items={},
            current_level_id="test_level",
            collision_map=[]
        )
        
        # Execute
        direction = Vector2(1.0, 0.0)
        result_game_state, attacked_monster_ids = use_case.execute(game_state, direction)

        # Verify - only attack event should be published
        event_bus.publish.assert_called_once()
        attack_event = event_bus.publish.call_args[0][0]
        assert attack_event.event_type == "player_attacked"


class TestBuildLevelUseCase:
    """Test the BuildLevelUseCase."""
    
    def test_build_level_with_player_and_monster(self):
        # Setup
        event_bus = Mock()
        use_case = BuildLevelUseCase(event_bus)
        
        from src.application.interfaces import LevelLayoutData
        
        layout_data = LevelLayoutData(
            width=3,
            height=3,
            tiles=[
                ["tile.floor.dirt", "tile.floor.dirt", "tile.floor.dirt"],
                ["tile.floor.dirt", "tile.floor.dirt", "tile.floor.dirt"],
                ["tile.floor.dirt", "tile.floor.dirt", "tile.floor.dirt"]
            ],
            entities=[
                {
                    "type": "player",
                    "x": 1,
                    "y": 1,
                    "asset_id": "player.hero"
                },
                {
                    "type": "monster",
                    "data_id": "goblin_grunt",
                    "name": "Test Goblin",
                    "x": 2,
                    "y": 1,
                    "hp": 25,
                    "strength": 6,
                    "defense": 2,
                    "speed": 5
                }
            ],
            collision_map=[
                [False, False, False],
                [False, False, False],
                [False, False, False]
            ]
        )
        
        # Execute
        result = use_case.execute(layout_data, "test_level")
        
        # Verify player was created
        assert result.player is not None
        # Position should be in pixels: tile (1,1) with tile_size 128 = (192, 192)
        assert result.player.position == Position(192, 192)
        assert result.player.name == "Hero"
        
        # Verify monster was created
        assert len(result.monsters) == 1
        monster = list(result.monsters.values())[0]
        # Position should be in pixels: tile (2,1) with tile_size 128 = (320, 192)
        assert monster.position == Position(320, 192)
        assert monster.name == "Test Goblin"
        assert monster.stats.hp == 25
        
        # Verify level metadata
        assert result.current_level_id == "test_level"
        assert result.collision_map == layout_data.collision_map
    
    def test_build_level_empty(self):
        # Setup
        event_bus = Mock()
        use_case = BuildLevelUseCase(event_bus)
        
        from src.application.interfaces import LevelLayoutData
        
        layout_data = LevelLayoutData(
            width=2,
            height=2,
            tiles=[
                ["tile.floor.dirt", "tile.floor.dirt"],
                ["tile.floor.dirt", "tile.floor.dirt"]
            ],
            entities=[],  # No entities
            collision_map=[
                [False, False],
                [False, False]
            ]
        )
        
        # Execute
        result = use_case.execute(layout_data, "empty_level")
        
        # Verify empty level
        assert result.player is None
        assert len(result.monsters) == 0
        assert len(result.items) == 0
        assert result.current_level_id == "empty_level"
