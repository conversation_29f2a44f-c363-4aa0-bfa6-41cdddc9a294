"""
Unit tests for the improved store UI functionality.
"""

import pytest
import pygame
from unittest.mock import Mock, MagicMock

from src.presentation.ui.store_ui import StoreUIController, StoreData, StoreMode
from src.application.use_cases import BuyFromNPCUseCase, SellToNPCUseCase
from src.application.interfaces import GameStateData
from src.game_core.entities import Player, Position, Stats


class TestStoreUIImprovements:
    """Test the improved store UI functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        # Initialize pygame for UI testing
        pygame.init()
        pygame.display.set_mode((800, 600))
        
        # Mock use cases
        self.buy_use_case = Mock(spec=BuyFromNPCUseCase)
        self.sell_use_case = Mock(spec=SellToNPCUseCase)
        
        # Create store UI controller
        self.store_ui = StoreUIController(self.buy_use_case, self.sell_use_case)
        
        # Create test player
        self.player = Player(
            id="test_player",
            name="Test Hero",
            position=Position(100, 100),
            asset_id="player.hero",
            stats=Stats(hp=100, max_hp=100, mp=50, max_mp=50, strength=10, defense=5, speed=8),
            size=(32, 32),
            level=1,
            experience=0,
            inventory={"gold_coin": 50, "bread": 2}
        )
        
        # Create test game state
        self.game_state = GameStateData(
            player=self.player,
            monsters={},
            items={},
            npcs={},
            current_level_id="test_level",
            collision_map=[],
            level_tiles=[]
        )
    
    def teardown_method(self):
        """Clean up after tests."""
        pygame.quit()
    
    def test_store_data_creation(self):
        """Test that store data is created correctly."""
        npc_inventory = {"bread": 10, "ale": 5, "cheese": 3}
        player_inventory = {"gold_coin": 50, "bread": 2}
        
        self.store_ui.show_store(
            npc_id="innkeeper_1",
            npc_name="Friendly Innkeeper",
            npc_type="innkeeper",
            npc_inventory=npc_inventory,
            player_inventory=player_inventory,
            player_gold=50
        )
        
        assert self.store_ui.store_data is not None
        assert self.store_ui.store_data.npc_id == "innkeeper_1"
        assert self.store_ui.store_data.npc_name == "Friendly Innkeeper"
        assert self.store_ui.store_data.npc_type == "innkeeper"
        assert self.store_ui.store_data.npc_inventory == npc_inventory
        assert self.store_ui.store_data.player_inventory == player_inventory
        assert self.store_ui.store_data.player_gold == 50
        assert self.store_ui.store_data.is_visible is True
    
    def test_item_selection_without_purchase(self):
        """Test that clicking an item selects it without immediately purchasing."""
        npc_inventory = {"bread": 10, "ale": 5}
        
        self.store_ui.show_store(
            npc_id="innkeeper_1",
            npc_name="Friendly Innkeeper", 
            npc_type="innkeeper",
            npc_inventory=npc_inventory,
            player_inventory={"gold_coin": 50},
            player_gold=50
        )
        
        # Initially no item should be selected
        assert self.store_ui.selected_item is None
        
        # Simulate clicking on an item (this would normally be done through _handle_item_click)
        self.store_ui.selected_item = "bread"
        
        # Item should be selected but no transaction should have occurred
        assert self.store_ui.selected_item == "bread"
        self.buy_use_case.execute.assert_not_called()
        self.sell_use_case.execute.assert_not_called()
    
    def test_buy_button_functionality(self):
        """Test that the buy button works correctly when an item is selected."""
        npc_inventory = {"bread": 10, "ale": 5}
        
        self.store_ui.show_store(
            npc_id="innkeeper_1",
            npc_name="Friendly Innkeeper",
            npc_type="innkeeper", 
            npc_inventory=npc_inventory,
            player_inventory={"gold_coin": 50},
            player_gold=50
        )
        
        # Select an item
        self.store_ui.selected_item = "bread"
        
        # Mock successful purchase
        updated_player = Player(
            id="test_player",
            name="Test Hero",
            position=Position(100, 100),
            asset_id="player.hero",
            stats=Stats(hp=100, max_hp=100, mp=50, max_mp=50, strength=10, defense=5, speed=8),
            size=(32, 32),
            level=1,
            experience=0,
            inventory={"gold_coin": 48, "bread": 3}  # 2 gold spent, 1 bread added
        )
        
        updated_game_state = GameStateData(
            player=updated_player,
            monsters={},
            items={},
            npcs={},
            current_level_id="test_level",
            collision_map=[],
            level_tiles=[]
        )
        
        self.buy_use_case.execute.return_value = updated_game_state
        
        # Simulate buy button click
        self.store_ui._handle_item_transaction("bread", self.game_state)
        
        # Verify purchase was attempted
        self.buy_use_case.execute.assert_called_once_with(
            self.game_state, "innkeeper_1", "bread", 1
        )
        
        # Verify store data was updated
        assert self.store_ui.store_data.player_inventory == {"gold_coin": 48, "bread": 3}
        assert self.store_ui.store_data.player_gold == 48
    
    def test_sell_mode_functionality(self):
        """Test that sell mode works correctly."""
        player_inventory = {"gold_coin": 50, "bread": 5}
        
        self.store_ui.show_store(
            npc_id="innkeeper_1",
            npc_name="Friendly Innkeeper",
            npc_type="innkeeper",
            npc_inventory={"bread": 10},
            player_inventory=player_inventory,
            player_gold=50
        )
        
        # Switch to sell mode
        self.store_ui.current_mode = StoreMode.SELL
        self.store_ui.selected_item = "bread"
        
        # Mock successful sale
        updated_player = Player(
            id="test_player",
            name="Test Hero",
            position=Position(100, 100),
            asset_id="player.hero",
            stats=Stats(hp=100, max_hp=100, mp=50, max_mp=50, strength=10, defense=5, speed=8),
            size=(32, 32),
            level=1,
            experience=0,
            inventory={"gold_coin": 51, "bread": 4}  # 1 gold gained, 1 bread sold
        )
        
        updated_game_state = GameStateData(
            player=updated_player,
            monsters={},
            items={},
            npcs={},
            current_level_id="test_level",
            collision_map=[],
            level_tiles=[]
        )
        
        self.sell_use_case.execute.return_value = updated_game_state
        
        # Simulate sell button click
        self.store_ui._handle_item_transaction("bread", self.game_state)
        
        # Verify sale was attempted
        self.sell_use_case.execute.assert_called_once_with(
            self.game_state, "innkeeper_1", "bread", 1
        )
    
    def test_store_visibility_toggle(self):
        """Test that store visibility can be toggled correctly."""
        # Initially store should not be visible
        assert not self.store_ui.is_store_visible()
        
        # Show store
        self.store_ui.show_store(
            npc_id="innkeeper_1",
            npc_name="Friendly Innkeeper",
            npc_type="innkeeper",
            npc_inventory={"bread": 10},
            player_inventory={"gold_coin": 50},
            player_gold=50
        )
        
        # Store should now be visible
        assert self.store_ui.is_store_visible()
        
        # Hide store
        self.store_ui.hide_store()
        
        # Store should no longer be visible
        assert not self.store_ui.is_store_visible()
        assert self.store_ui.store_data is None
