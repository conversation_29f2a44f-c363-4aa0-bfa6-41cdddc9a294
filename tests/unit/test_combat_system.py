"""
Unit tests for the combat system.
"""

import pytest
import math
from src.game_core import (
    <PERSON><PERSON><PERSON>, Vector2, St<PERSON>, Monster, Player,
    find_entities_in_attack_arc, find_entities_in_attack_arc_with_hitboxes,
    get_weapon_stats, calculate_damage, apply_damage, check_hit
)


class TestWeaponStats:
    """Test weapon stats extraction."""
    
    def test_get_weapon_stats_with_all_properties(self):
        """Test extracting weapon stats when all properties are present."""
        weapon_props = {
            "damage": 5,
            "attack_speed": 1.2,
            "range": 1.5,
            "damage_arc": 90.0
        }
        
        damage, speed, range_val, arc = get_weapon_stats(weapon_props)
        
        assert damage == 5
        assert speed == 1.2
        assert range_val == 1.5
        assert arc == 90.0
    
    def test_get_weapon_stats_with_missing_properties(self):
        """Test extracting weapon stats with missing properties uses defaults."""
        weapon_props = {"damage": 3}
        
        damage, speed, range_val, arc = get_weapon_stats(weapon_props)
        
        assert damage == 3
        assert speed == 1.0  # default
        assert range_val == 1.0  # default
        assert arc == 60.0  # default
    
    def test_get_weapon_stats_empty_properties(self):
        """Test extracting weapon stats from empty properties uses all defaults."""
        weapon_props = {}
        
        damage, speed, range_val, arc = get_weapon_stats(weapon_props)
        
        assert damage == 1
        assert speed == 1.0
        assert range_val == 1.0
        assert arc == 60.0


class TestAttackArcDetection:
    """Test attack arc detection algorithms."""
    
    def test_find_entities_in_attack_arc_direct_hit(self):
        """Test hitting entity directly in front."""
        attacker_pos = Position(0, 0)
        direction = Vector2(1.0, 0.0)  # Attacking right
        weapon_range = 100.0
        damage_arc = 60.0
        
        entity_positions = [
            ("target1", Position(50, 0)),   # Directly in front
            ("target2", Position(-50, 0)),  # Behind
            ("target3", Position(0, 50)),   # To the side
        ]
        
        targets = find_entities_in_attack_arc(
            attacker_pos, direction, weapon_range, damage_arc, entity_positions
        )
        
        assert "target1" in targets
        assert "target2" not in targets
        assert "target3" not in targets
    
    def test_find_entities_in_attack_arc_range_limit(self):
        """Test that entities outside range are not hit."""
        attacker_pos = Position(0, 0)
        direction = Vector2(1.0, 0.0)
        weapon_range = 50.0
        damage_arc = 60.0
        
        entity_positions = [
            ("close", Position(30, 0)),    # Within range
            ("far", Position(100, 0)),     # Outside range
        ]
        
        targets = find_entities_in_attack_arc(
            attacker_pos, direction, weapon_range, damage_arc, entity_positions
        )
        
        assert "close" in targets
        assert "far" not in targets
    
    def test_find_entities_in_attack_arc_angle_limit(self):
        """Test that entities outside arc angle are not hit."""
        attacker_pos = Position(0, 0)
        direction = Vector2(1.0, 0.0)  # Attacking right
        weapon_range = 100.0
        damage_arc = 60.0  # ±30 degrees
        
        entity_positions = [
            ("front", Position(50, 0)),      # 0 degrees - should hit
            ("slight_up", Position(50, 25)), # ~26 degrees - should hit
            ("too_far_up", Position(50, 50)), # 45 degrees - should not hit
        ]
        
        targets = find_entities_in_attack_arc(
            attacker_pos, direction, weapon_range, damage_arc, entity_positions
        )
        
        assert "front" in targets
        assert "slight_up" in targets
        assert "too_far_up" not in targets


class TestHitboxAwareAttackDetection:
    """Test hitbox-aware attack detection."""
    
    def test_find_entities_with_hitboxes_center_hit(self):
        """Test hitting entity center with hitbox detection."""
        attacker_pos = Position(0, 0)
        direction = Vector2(1.0, 0.0)
        weapon_range = 100.0
        damage_arc = 60.0
        
        entity_data = [
            ("target1", Position(50, 0), (32, 32)),  # 32x32 hitbox at (50,0)
        ]
        
        targets = find_entities_in_attack_arc_with_hitboxes(
            attacker_pos, direction, weapon_range, damage_arc, entity_data
        )
        
        assert "target1" in targets
    
    def test_find_entities_with_hitboxes_corner_hit(self):
        """Test hitting entity corner with hitbox detection."""
        attacker_pos = Position(0, 0)
        direction = Vector2(1.0, 0.0)  # Attacking right
        weapon_range = 100.0
        damage_arc = 60.0
        
        # Entity center is slightly off-angle, but corner should be in arc
        entity_data = [
            ("target1", Position(50, 20), (32, 32)),  # Center at (50,20), corners at (34,4) to (66,36)
        ]
        
        targets = find_entities_in_attack_arc_with_hitboxes(
            attacker_pos, direction, weapon_range, damage_arc, entity_data
        )
        
        # Should hit because one of the corners (50-16, 20-16) = (34, 4) is within the arc
        assert "target1" in targets
    
    def test_find_entities_with_hitboxes_miss(self):
        """Test missing entity even with hitbox detection."""
        attacker_pos = Position(0, 0)
        direction = Vector2(1.0, 0.0)  # Attacking right
        weapon_range = 100.0
        damage_arc = 30.0  # Narrow arc
        
        # Entity and all corners are outside the narrow arc
        entity_data = [
            ("target1", Position(50, 50), (16, 16)),  # Far to the side
        ]
        
        targets = find_entities_in_attack_arc_with_hitboxes(
            attacker_pos, direction, weapon_range, damage_arc, entity_data
        )
        
        assert "target1" not in targets


class TestCombatDamageCalculation:
    """Test combat damage and hit calculations."""
    
    def test_calculate_damage_basic(self):
        """Test basic damage calculation (deterministic)."""
        attacker_stats = Stats(hp=100, max_hp=100, mp=50, max_mp=50,
                              strength=10, defense=5, speed=8)
        defender_stats = Stats(hp=50, max_hp=50, mp=0, max_mp=0,
                              strength=5, defense=2, speed=6)
        weapon_damage = 3

        damage = calculate_damage(attacker_stats, defender_stats, weapon_damage)

        # Should be strength + weapon_damage - defense = 10 + 3 - 2 = 11
        assert damage == 11
    
    def test_calculate_damage_minimum(self):
        """Test that damage has a minimum value."""
        attacker_stats = Stats(hp=100, max_hp=100, mp=50, max_mp=50,
                              strength=1, defense=5, speed=8)
        defender_stats = Stats(hp=50, max_hp=50, mp=0, max_mp=0,
                              strength=5, defense=10, speed=6)
        weapon_damage = 1
        
        damage = calculate_damage(attacker_stats, defender_stats, weapon_damage)
        
        # Should be at least 1 damage even if defense is higher
        assert damage >= 1
    
    def test_apply_damage(self):
        """Test applying damage to stats."""
        original_stats = Stats(hp=20, max_hp=20, mp=10, max_mp=10,
                              strength=5, defense=3, speed=6)
        
        new_stats = apply_damage(original_stats, 8)
        
        assert new_stats.hp == 12
        assert new_stats.max_hp == 20  # Unchanged
        assert new_stats.strength == 5  # Unchanged
    
    def test_apply_damage_lethal(self):
        """Test applying lethal damage."""
        original_stats = Stats(hp=5, max_hp=20, mp=10, max_mp=10,
                              strength=5, defense=3, speed=6)
        
        new_stats = apply_damage(original_stats, 10)
        
        assert new_stats.hp == 0  # Should not go below 0
        assert new_stats.max_hp == 20  # Unchanged


class TestCombatIntegration:
    """Integration tests for the complete combat system."""
    
    def test_combat_scenario_player_vs_animal(self):
        """Test a complete combat scenario: player attacking an animal."""
        # Create player with rusty sword stats
        player_stats = Stats(hp=100, max_hp=100, mp=50, max_mp=50,
                            strength=10, defense=5, speed=8)
        
        # Create chicken with low HP and no armor
        chicken_stats = Stats(hp=5, max_hp=5, mp=0, max_mp=0,
                             strength=2, defense=0, speed=12)
        
        # Weapon stats for rusty sword
        weapon_damage = 3
        weapon_range = 153.6  # 1.2 tiles * 128 pixels
        damage_arc = 60.0
        
        # Position player and chicken close together
        player_pos = Position(100, 100)
        chicken_pos = Position(150, 100)  # 50 pixels away, within range
        
        # Attack direction toward chicken
        direction = Vector2(1.0, 0.0)
        
        # Test attack arc detection
        entity_data = [("chicken", chicken_pos, (64, 64))]
        targets = find_entities_in_attack_arc_with_hitboxes(
            player_pos, direction, weapon_range, damage_arc, entity_data
        )
        
        assert "chicken" in targets
        
        # Test hit calculation
        hit = check_hit(player_stats, chicken_stats)
        assert hit  # Should always hit (no randomness)

        # Test damage calculation
        damage = calculate_damage(player_stats, chicken_stats, weapon_damage)
        # Base damage = 10 + 3 - 0 = 13 (deterministic)
        assert damage == 13

        # Test applying damage
        new_chicken_stats = apply_damage(chicken_stats, damage)
        assert new_chicken_stats.hp == 0  # Chicken should be defeated (5 HP - 13 damage)
    
    def test_combat_scenario_miss_due_to_range(self):
        """Test combat scenario where attack misses due to range."""
        player_pos = Position(0, 0)
        target_pos = Position(200, 0)  # Far away
        direction = Vector2(1.0, 0.0)
        weapon_range = 100.0  # Not enough range
        damage_arc = 60.0
        
        entity_data = [("target", target_pos, (64, 64))]
        targets = find_entities_in_attack_arc_with_hitboxes(
            player_pos, direction, weapon_range, damage_arc, entity_data
        )
        
        assert "target" not in targets
    
    def test_combat_scenario_miss_due_to_angle(self):
        """Test combat scenario where attack misses due to angle."""
        player_pos = Position(0, 0)
        target_pos = Position(0, 100)  # Directly above
        direction = Vector2(1.0, 0.0)  # Attacking right
        weapon_range = 150.0  # Enough range
        damage_arc = 30.0  # Narrow arc
        
        entity_data = [("target", target_pos, (64, 64))]
        targets = find_entities_in_attack_arc_with_hitboxes(
            player_pos, direction, weapon_range, damage_arc, entity_data
        )
        
        assert "target" not in targets
