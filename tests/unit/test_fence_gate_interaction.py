"""
Unit tests for fence gate tile interactions.

These tests verify that fence gates can be opened and closed properly.
"""

import pytest
import unittest
from unittest.mock import Mock
import yaml
import os
import sys

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from src.application.use_cases import TileInteractionUseCase
from src.application.interfaces import GameStateData
from src.infrastructure.repositories.map_parser import MapParser
from src.game_core.entities import Player, Position, Stats


class TestFenceGateInteraction(unittest.TestCase):
    """Test that fence gate interactions work for both opening and closing."""
    
    def setUp(self):
        """Set up test objects."""
        # Load base legend
        base_legend_path = os.path.join(os.path.dirname(__file__), '..', '..', 'src', 'game_data', 'base_legend.yaml')
        with open(base_legend_path, 'r') as f:
            self.base_legend = yaml.safe_load(f)
        
        # Create mock map parser
        self.mock_map_parser = Mock()
        self.mock_map_parser.base_legend = self.base_legend
        
        # Create mock event bus
        self.mock_event_bus = Mock()
        
        # Create use case
        self.use_case = TileInteractionUseCase(self.mock_event_bus, self.mock_map_parser)
    
    def test_closed_fence_gate_detection(self):
        """Test that a closed fence gate is detected as interactive."""
        closed_gate_asset = "tile.gate.wood_fence"
        tile_def = self.use_case._get_base_tile_definition_for_interactive_tile(closed_gate_asset)
        
        self.assertIsNotNone(tile_def, "Closed fence gate should be detected as interactive")
        self.assertTrue(tile_def.get('properties', {}).get('can_interact', False))
        self.assertEqual(tile_def.get('asset_id'), "tile.gate.wood_fence")
    
    def test_open_fence_gate_detection(self):
        """Test that an open fence gate is detected as interactive."""
        open_gate_asset = "tile.gate.wood_fence.open"
        tile_def = self.use_case._get_base_tile_definition_for_interactive_tile(open_gate_asset)
        
        self.assertIsNotNone(tile_def, "Open fence gate should be detected as interactive")
        self.assertTrue(tile_def.get('properties', {}).get('can_interact', False))
        self.assertEqual(tile_def.get('asset_id'), "tile.gate.wood_fence")  # Should return the base gate definition
    
    def test_get_tile_definition_direct(self):
        """Test that _get_tile_definition works for closed fence gates."""
        closed_gate_asset = "tile.gate.wood_fence"
        tile_def = self.use_case._get_tile_definition(closed_gate_asset)
        
        self.assertIsNotNone(tile_def, "Direct lookup should work for closed fence gates")
        self.assertTrue(tile_def.get('properties', {}).get('can_interact', False))
    
    def test_get_tile_definition_open_gate_fails(self):
        """Test that _get_tile_definition returns None for open fence gates (expected behavior)."""
        open_gate_asset = "tile.gate.wood_fence.open"
        tile_def = self.use_case._get_tile_definition(open_gate_asset)
        
        self.assertIsNone(tile_def, "Direct lookup should fail for open fence gates - that's why we need the smart method")
    
    def test_base_legend_contains_fence_gate(self):
        """Test that the base legend contains the fence gate definition."""
        # Find the fence gate entry in base legend
        gate_entry = None
        for symbol, definition in self.base_legend.items():
            if definition.get('asset_id') == 'tile.gate.wood_fence':
                gate_entry = definition
                break
        
        self.assertIsNotNone(gate_entry, "Base legend should contain fence gate definition")
        self.assertTrue(gate_entry.get('properties', {}).get('can_interact', False))
        self.assertEqual(gate_entry.get('properties', {}).get('closed_asset_id'), 'tile.gate.wood_fence')
        self.assertEqual(gate_entry.get('properties', {}).get('open_asset_id'), 'tile.gate.wood_fence.open')
    
    def test_fence_gate_symbol_mapping(self):
        """Test that the 'F' symbol maps to fence gate in base legend."""
        gate_definition = self.base_legend.get('F')

        self.assertIsNotNone(gate_definition, "Symbol 'F' should be defined in base legend")
        self.assertEqual(gate_definition.get('type'), 'fence_gate')
        self.assertEqual(gate_definition.get('asset_id'), 'tile.gate.wood_fence')
        self.assertTrue(gate_definition.get('solid', False), "Closed fence gate should be solid")
        self.assertTrue(gate_definition.get('properties', {}).get('can_interact', False))


def test_fence_gate_interaction_full():
    """Integration test that fence gates can be both opened and closed."""
    # Create mock event bus
    event_bus = Mock()
    
    # Load base legend
    base_legend_path = os.path.join(os.path.dirname(__file__), '..', '..', 'src', 'game_data', 'base_legend.yaml')
    map_parser = MapParser(base_legend_path)
    
    # Create tile interaction use case
    tile_interaction = TileInteractionUseCase(event_bus, map_parser)
    
    # Create initial game state with a closed fence gate
    level_tiles = [
        ["tile.floor.grass", "tile.floor.grass", "tile.floor.grass"],
        ["tile.floor.grass", "tile.gate.wood_fence", "tile.floor.grass"],
        ["tile.floor.grass", "tile.floor.grass", "tile.floor.grass"]
    ]
    
    collision_map = [
        [False, False, False],
        [False, True, False],   # Gate is initially solid
        [False, False, False]
    ]
    
    # Create a test player
    player = Player(
        id="test_player",
        name="Test Player",
        position=Position(32, 32),
        asset_id="player.hero",
        stats=Stats(hp=100, max_hp=100, mp=50, max_mp=50,
                   strength=10, defense=5, speed=8),
        size=(32, 32),
        level=1,
        experience=0,
        inventory={}
    )
    
    game_state = GameStateData(
        player=player,
        monsters={},
        items={},
        npcs={},
        current_level_id="test_level",
        collision_map=collision_map,
        level_tiles=level_tiles,
        tile_states={}
    )
    
    # Test 1: Open the closed fence gate
    result_state = tile_interaction.execute(game_state, 1, 1)  # Gate at position (1,1)
    
    # Verify gate is now open
    assert result_state.level_tiles[1][1] == "tile.gate.wood_fence.open", f"Expected open gate, got {result_state.level_tiles[1][1]}"
    assert result_state.collision_map[1][1] == False, "Open gate should not be solid"
    assert "1,1" in result_state.tile_states, "Gate should have state entry"
    assert result_state.tile_states["1,1"]["is_open"] == True, "Gate state should be open"
    
    # Test 2: Close the open fence gate
    result_state2 = tile_interaction.execute(result_state, 1, 1)  # Same gate position
    
    # Verify gate is now closed
    assert result_state2.level_tiles[1][1] == "tile.gate.wood_fence", f"Expected closed gate, got {result_state2.level_tiles[1][1]}"
    assert result_state2.collision_map[1][1] == True, "Closed gate should be solid"
    assert result_state2.tile_states["1,1"]["is_open"] == False, "Gate state should be closed"


if __name__ == '__main__':
    print("Testing fence gate interaction...")
    unittest.main(verbosity=2)
