"""
Unit tests for AI behavior system.

Tests the new AI behavior system including chase, flee, and attack behaviors.
"""

import pytest
import time
from unittest.mock import Mo<PERSON>, patch
from src.game_core import <PERSON><PERSON><PERSON>, Stats, Monster, Player, Direction
from src.game_core.wander_system import (
    AIBehavior, AIState, WanderBehavior, WanderState,
    calculate_distance, calculate_direction_to_target, find_flee_direction,
    _update_ai_behavior, get_monsters_ready_to_attack, update_wandering_entities,
    get_alternative_directions, is_direction_passable, find_best_direction
)
from src.application import MonsterAttackUseCase, GameStateData
from src.application.interfaces import IEventBus


class MockEventBus(IEventBus):
    """Mock event bus for testing."""
    
    def __init__(self):
        self.published_events = []
    
    def publish(self, event):
        self.published_events.append(event)
    
    def subscribe(self, event_type, handler):
        pass
    
    def unsubscribe(self, event_type, handler):
        pass


class TestAIBehavior:
    """Test AI behavior configuration and state management."""
    
    def test_ai_behavior_initialization(self):
        """Test AI behavior initializes with correct defaults."""
        ai_behavior = AIBehavior()
        
        assert ai_behavior.enabled is True
        assert ai_behavior.detection_radius == 6.0
        assert ai_behavior.chase_speed_multiplier == 1.5
        assert ai_behavior.flee_speed_multiplier == 2.0
        assert ai_behavior.attack_range == 1.5
        assert ai_behavior.flee_duration == 5.0
        assert ai_behavior.current_state == AIState.WANDERING
        assert ai_behavior.target_position is None
        assert ai_behavior.has_been_attacked is False
    
    def test_ai_behavior_state_transitions(self):
        """Test AI behavior state transitions."""
        ai_behavior = AIBehavior()
        target_pos = Position(100, 100)
        
        # Test transition to chasing
        ai_behavior.transition_to_state(AIState.CHASING, target_pos)
        assert ai_behavior.current_state == AIState.CHASING
        assert ai_behavior.target_position == target_pos
        
        # Test transition to fleeing
        ai_behavior.transition_to_state(AIState.FLEEING, target_pos)
        assert ai_behavior.current_state == AIState.FLEEING
        assert ai_behavior.flee_start_time is not None
    
    def test_flee_duration_expiry(self):
        """Test flee duration expiry check."""
        ai_behavior = AIBehavior(flee_duration=0.1)  # Very short duration for testing
        
        # Initially not fleeing
        assert ai_behavior.is_fleeing_expired() is True
        
        # Start fleeing
        ai_behavior.transition_to_state(AIState.FLEEING)
        assert ai_behavior.is_fleeing_expired() is False
        
        # Wait for expiry
        time.sleep(0.2)
        assert ai_behavior.is_fleeing_expired() is True


class TestAIUtilityFunctions:
    """Test AI utility functions."""
    
    def test_calculate_distance(self):
        """Test distance calculation between positions."""
        pos1 = Position(0, 0)
        pos2 = Position(128, 0)  # 1 tile away (128 pixels)
        
        distance = calculate_distance(pos1, pos2)
        assert distance == 1.0  # Should be 1 tile
        
        pos3 = Position(128, 128)  # Diagonal
        distance = calculate_distance(pos1, pos3)
        assert abs(distance - 1.414) < 0.01  # Should be sqrt(2) tiles
    
    def test_calculate_direction_to_target(self):
        """Test direction calculation to target."""
        from_pos = Position(100, 100)
        
        # Test cardinal directions
        target_right = Position(200, 100)
        direction = calculate_direction_to_target(from_pos, target_right)
        assert direction.name == "EAST"

        target_left = Position(50, 100)
        direction = calculate_direction_to_target(from_pos, target_left)
        assert direction.name == "WEST"

        target_down = Position(100, 200)
        direction = calculate_direction_to_target(from_pos, target_down)
        assert direction.name == "SOUTH"

        target_up = Position(100, 50)
        direction = calculate_direction_to_target(from_pos, target_up)
        assert direction.name == "NORTH"
    
    def test_find_flee_direction(self):
        """Test flee direction calculation."""
        from_pos = Position(100, 100)
        threat_pos = Position(50, 100)  # Threat to the left
        
        flee_direction = find_flee_direction(from_pos, threat_pos)
        assert flee_direction.name == "EAST"  # Should flee right

        threat_pos = Position(100, 50)  # Threat above
        flee_direction = find_flee_direction(from_pos, threat_pos)
        assert flee_direction.name == "SOUTH"  # Should flee down


class TestMonsterAIBehavior:
    """Test monster AI behavior updates."""
    
    def create_test_monster(self, ai_behavior_type="aggressive", position=None):
        """Create a test monster with AI behavior."""
        if position is None:
            position = Position(100, 100)
            
        monster = Monster(
            id="test_monster",
            name="Test Monster",
            position=position,
            asset_id="test_asset",
            stats=Stats(hp=20, max_hp=20, mp=0, max_mp=0, strength=5, defense=2, speed=6),
            monster_type="test",
            ai_behavior=ai_behavior_type,
            experience_reward=10
        )
        
        # Add AI behavior
        monster.ai_behavior_instance = AIBehavior(
            enabled=True,
            detection_radius=6.0,
            chase_speed_multiplier=1.5,
            flee_speed_multiplier=2.0,
            attack_range=1.5
        )
        
        return monster
    
    def test_aggressive_monster_detection(self):
        """Test aggressive monster player detection."""
        monster = self.create_test_monster("aggressive", Position(100, 100))
        player_position = Position(200, 100)  # 100 pixels = ~0.78 tiles away
        collision_map = [[False] * 10 for _ in range(10)]
        
        # Player is within detection range, should start chasing
        new_position = _update_ai_behavior(
            monster, monster.ai_behavior_instance, player_position, 0.1, collision_map
        )
        
        assert monster.ai_behavior_instance.current_state == AIState.CHASING
        assert monster.ai_behavior_instance.last_known_player_position == player_position
    
    def test_aggressive_monster_chase(self):
        """Test aggressive monster chasing behavior."""
        monster = self.create_test_monster("aggressive", Position(100, 100))
        player_position = Position(300, 100)  # Further away
        collision_map = [[False] * 10 for _ in range(10)]
        
        # Set monster to chasing state
        monster.ai_behavior_instance.transition_to_state(AIState.CHASING, player_position)
        
        # Update AI behavior - should move toward player
        new_position = _update_ai_behavior(
            monster, monster.ai_behavior_instance, player_position, 0.1, collision_map
        )
        
        # Should have moved toward the player (to the right)
        assert new_position.x > monster.position.x
        assert monster.ai_behavior_instance.current_state == AIState.CHASING
    
    def test_aggressive_monster_attack_range(self):
        """Test aggressive monster entering attack range."""
        monster = self.create_test_monster("aggressive", Position(100, 100))
        player_position = Position(150, 100)  # Close enough to attack
        collision_map = [[False] * 10 for _ in range(10)]
        
        # Set monster to chasing state
        monster.ai_behavior_instance.transition_to_state(AIState.CHASING, player_position)
        
        # Update AI behavior - should transition to attacking
        new_position = _update_ai_behavior(
            monster, monster.ai_behavior_instance, player_position, 0.1, collision_map
        )
        
        assert monster.ai_behavior_instance.current_state == AIState.ATTACKING
    
    def test_passive_monster_flee_when_attacked(self):
        """Test passive monster fleeing when attacked."""
        monster = self.create_test_monster("peaceful", Position(100, 100))
        player_position = Position(150, 100)
        collision_map = [[False] * 10 for _ in range(10)]
        attacked_monsters = [monster.id]
        
        # Update AI behavior with monster being attacked
        new_position = _update_ai_behavior(
            monster, monster.ai_behavior_instance, player_position, 0.1, collision_map, 64.0, attacked_monsters
        )
        
        assert monster.ai_behavior_instance.current_state == AIState.FLEEING
        assert monster.ai_behavior_instance.has_been_attacked is True
    
    def test_passive_monster_flee_movement(self):
        """Test passive monster movement when fleeing."""
        monster = self.create_test_monster("peaceful", Position(200, 100))
        player_position = Position(100, 100)  # Player to the left
        collision_map = [[False] * 10 for _ in range(10)]
        
        # Set monster to fleeing state
        monster.ai_behavior_instance.transition_to_state(AIState.FLEEING, player_position)
        
        # Update AI behavior - should move away from player
        new_position = _update_ai_behavior(
            monster, monster.ai_behavior_instance, player_position, 0.1, collision_map
        )
        
        # Should have moved away from the player (to the right)
        assert new_position.x > monster.position.x
        assert monster.ai_behavior_instance.current_state == AIState.FLEEING


class TestMonsterAttackSystem:
    """Test monster attack system."""
    
    def test_get_monsters_ready_to_attack(self):
        """Test getting monsters ready to attack."""
        # Create attacking monster
        monster = Monster(
            id="attacking_monster",
            name="Attacking Monster",
            position=Position(100, 100),
            asset_id="test_asset",
            stats=Stats(hp=20, max_hp=20, mp=0, max_mp=0, strength=5, defense=2, speed=6),
            monster_type="test",
            ai_behavior="aggressive",
            experience_reward=10
        )
        
        monster.ai_behavior_instance = AIBehavior(
            current_state=AIState.ATTACKING,
            attack_range=2.0,
            attack_cooldown=1.0,
            last_attack_time=0.0  # Ready to attack
        )
        
        monsters = {"attacking_monster": monster}
        player_position = Position(150, 100)  # Within attack range
        
        attacking_monsters = get_monsters_ready_to_attack(monsters, player_position)
        
        assert "attacking_monster" in attacking_monsters
    
    def test_monster_attack_use_case(self):
        """Test monster attack use case."""
        event_bus = MockEventBus()
        use_case = MonsterAttackUseCase(event_bus)
        
        # Create player
        player = Player(
            id="test_player",
            name="Test Player",
            position=Position(100, 100),
            asset_id="player.human.male.side",
            stats=Stats(hp=50, max_hp=50, mp=20, max_mp=20, strength=8, defense=3, speed=7),
            size=(64, 64),
            inventory={},
            experience=0,
            level=1
        )
        
        # Create attacking monster
        monster = Monster(
            id="attacking_monster",
            name="Attacking Monster",
            position=Position(150, 100),
            asset_id="test_asset",
            stats=Stats(hp=20, max_hp=20, mp=0, max_mp=0, strength=6, defense=1, speed=5),
            monster_type="test",
            ai_behavior="aggressive",
            experience_reward=10
        )
        
        game_state = GameStateData(
            player=player,
            monsters={"attacking_monster": monster},
            items={},
            npcs={},
            current_level_id="test_level",
            collision_map=[[False] * 10 for _ in range(10)],
            level_tiles=[["tile.floor.dirt"] * 10 for _ in range(10)],
            tile_states={}
        )
        
        # Execute monster attack
        updated_game_state = use_case.execute(game_state, ["attacking_monster"])
        
        # Player should have taken damage
        assert updated_game_state.player.stats.hp < player.stats.hp
        
        # Should have published damage event
        assert len(event_bus.published_events) > 0
        damage_event = event_bus.published_events[0]
        assert damage_event.event_type == "entity_damaged"
        assert damage_event.target_id == player.id
        assert damage_event.source_id == monster.id


class TestWanderingEntitiesIntegration:
    """Test integration of AI behavior with wandering entities system."""

    def test_nearby_passive_monsters_flee(self):
        """Test that passive monsters within 6 tiles flee when another is attacked."""
        # Create multiple passive monsters
        monster1 = Monster(
            id="monster1",
            name="Peaceful Monster 1",
            position=Position(100, 100),
            asset_id="test_asset",
            stats=Stats(hp=10, max_hp=10, mp=0, max_mp=0, strength=2, defense=1, speed=8),
            monster_type="peaceful_animal",
            ai_behavior="peaceful",
            experience_reward=5
        )
        monster1.ai_behavior_instance = AIBehavior(current_state=AIState.WANDERING)

        monster2 = Position(400, 100)  # 300 pixels = ~2.3 tiles away
        monster2 = Monster(
            id="monster2",
            name="Peaceful Monster 2",
            position=monster2,
            asset_id="test_asset",
            stats=Stats(hp=10, max_hp=10, mp=0, max_mp=0, strength=2, defense=1, speed=8),
            monster_type="peaceful_animal",
            ai_behavior="peaceful",
            experience_reward=5
        )
        monster2.ai_behavior_instance = AIBehavior(current_state=AIState.WANDERING)

        monsters = {"monster1": monster1, "monster2": monster2}
        npcs = {}
        collision_map = [[False] * 20 for _ in range(20)]
        player_position = Position(150, 100)
        attacked_monsters = ["monster1"]  # monster1 was attacked

        # Update wandering entities
        updated_npcs, updated_monsters = update_wandering_entities(
            npcs, monsters, 0.1, collision_map, None, player_position, attacked_monsters
        )

        # Both monsters should be fleeing
        assert updated_monsters["monster1"].ai_behavior_instance.current_state == AIState.FLEEING
        assert updated_monsters["monster2"].ai_behavior_instance.current_state == AIState.FLEEING

    def test_aggressive_monster_detection_and_chase(self):
        """Test aggressive monster detection and chase through wandering entities system."""
        # Create aggressive monster
        monster = Monster(
            id="aggressive_monster",
            name="Aggressive Monster",
            position=Position(100, 100),
            asset_id="test_asset",
            stats=Stats(hp=25, max_hp=25, mp=0, max_mp=0, strength=6, defense=2, speed=7),
            monster_type="goblin",
            ai_behavior="aggressive",
            experience_reward=15
        )
        monster.ai_behavior_instance = AIBehavior(
            current_state=AIState.WANDERING,
            detection_radius=6.0
        )

        monsters = {"aggressive_monster": monster}
        npcs = {}
        collision_map = [[False] * 20 for _ in range(20)]
        player_position = Position(300, 100)  # Within detection range

        # Update wandering entities
        updated_npcs, updated_monsters = update_wandering_entities(
            npcs, monsters, 0.1, collision_map, None, player_position, None
        )

        # Monster should start chasing
        assert updated_monsters["aggressive_monster"].ai_behavior_instance.current_state == AIState.CHASING

        # Run another update to see movement (detection happens first, then movement)
        updated_npcs2, updated_monsters2 = update_wandering_entities(
            updated_npcs, updated_monsters, 0.1, collision_map, None, player_position, None
        )

        # Monster should have moved toward player
        assert updated_monsters2["aggressive_monster"].position.x > monster.position.x

    def test_mixed_monster_behaviors(self):
        """Test mixed aggressive and passive monsters in the same update."""
        # Create one aggressive and one passive monster
        aggressive_monster = Monster(
            id="aggressive",
            name="Aggressive Monster",
            position=Position(100, 100),
            asset_id="test_asset",
            stats=Stats(hp=25, max_hp=25, mp=0, max_mp=0, strength=6, defense=2, speed=7),
            monster_type="goblin",
            ai_behavior="aggressive",
            experience_reward=15
        )
        aggressive_monster.ai_behavior_instance = AIBehavior(current_state=AIState.WANDERING)

        passive_monster = Monster(
            id="passive",
            name="Passive Monster",
            position=Position(200, 100),
            asset_id="test_asset",
            stats=Stats(hp=10, max_hp=10, mp=0, max_mp=0, strength=2, defense=1, speed=8),
            monster_type="peaceful_animal",
            ai_behavior="peaceful",
            experience_reward=5
        )
        passive_monster.ai_behavior_instance = AIBehavior(current_state=AIState.WANDERING)

        monsters = {"aggressive": aggressive_monster, "passive": passive_monster}
        npcs = {}
        collision_map = [[False] * 20 for _ in range(20)]
        player_position = Position(300, 100)
        attacked_monsters = ["passive"]  # Passive monster was attacked

        # Update wandering entities
        updated_npcs, updated_monsters = update_wandering_entities(
            npcs, monsters, 0.1, collision_map, None, player_position, attacked_monsters
        )

        # Aggressive monster should chase player
        assert updated_monsters["aggressive"].ai_behavior_instance.current_state == AIState.CHASING

        # Passive monster should flee
        assert updated_monsters["passive"].ai_behavior_instance.current_state == AIState.FLEEING


class TestEnhancedAIBehaviors:
    """Test enhanced AI behaviors including pathfinding and proximity detection."""

    def test_proximity_flee_behavior(self):
        """Test that skittish monsters flee when player gets close."""
        monster = Monster(
            id="proximity_test",
            name="Proximity Test Monster",
            position=Position(100, 100),
            asset_id="test_asset",
            stats=Stats(hp=10, max_hp=10, mp=0, max_mp=0, strength=2, defense=1, speed=8),
            monster_type="skittish_animal",
            ai_behavior="skittish",
            experience_reward=5
        )

        # Set up AI behavior with proximity detection
        monster.ai_behavior_instance = AIBehavior(
            current_state=AIState.WANDERING,
            proximity_flee_radius=4.0,
            min_safe_distance=8.0
        )

        # Player approaches within proximity flee radius
        player_position = Position(200, 100)  # About 0.78 tiles away
        collision_map = [[False] * 10 for _ in range(10)]

        # Update AI behavior - should start fleeing due to proximity
        new_position = _update_ai_behavior(
            monster, monster.ai_behavior_instance, player_position, 0.1, collision_map
        )

        assert monster.ai_behavior_instance.current_state == AIState.FLEEING

    def test_safe_distance_stop_fleeing(self):
        """Test that monsters stop fleeing when player is at safe distance."""
        monster = Monster(
            id="safe_distance_test",
            name="Safe Distance Test Monster",
            position=Position(100, 100),
            asset_id="test_asset",
            stats=Stats(hp=10, max_hp=10, mp=0, max_mp=0, strength=2, defense=1, speed=8),
            monster_type="peaceful_animal",
            ai_behavior="peaceful",
            experience_reward=5
        )

        # Set up AI behavior in fleeing state
        monster.ai_behavior_instance = AIBehavior(
            current_state=AIState.FLEEING,
            min_safe_distance=8.0
        )

        # Player is far away (safe distance)
        player_position = Position(1200, 100)  # About 8.6 tiles away
        collision_map = [[False] * 20 for _ in range(20)]

        # Update AI behavior - should stop fleeing
        new_position = _update_ai_behavior(
            monster, monster.ai_behavior_instance, player_position, 0.1, collision_map
        )

        assert monster.ai_behavior_instance.current_state == AIState.WANDERING

    def test_alternative_directions(self):
        """Test that alternative directions are provided when primary direction is blocked."""
        alternatives = get_alternative_directions(Direction.NORTH)

        # Should have alternatives in logical order
        assert len(alternatives) > 0
        assert Direction.NORTHEAST in alternatives or Direction.NORTHWEST in alternatives
        assert Direction.SOUTH in alternatives  # Opposite direction should be last resort

    def test_direction_passability_check(self):
        """Test direction passability checking."""
        current_pos = Position(128, 128)  # Tile (1, 1)

        # Create collision map with blocked tile to the north
        collision_map = [
            [False, True, False],   # Row 0: tile (1,0) is blocked
            [False, False, False],  # Row 1: current position
            [False, False, False]   # Row 2
        ]

        # North should be blocked
        assert not is_direction_passable(current_pos, Direction.NORTH, collision_map)

        # East should be passable
        assert is_direction_passable(current_pos, Direction.EAST, collision_map)

        # South should be passable
        assert is_direction_passable(current_pos, Direction.SOUTH, collision_map)

    def test_best_direction_with_obstacles(self):
        """Test that best direction finding works around obstacles."""
        current_pos = Position(128, 128)  # Tile (1, 1)
        target_pos = Position(128, 0)     # Tile (1, 0) - directly north

        # Create collision map with blocked tile to the north
        collision_map = [
            [False, True, False],   # Row 0: target tile is blocked
            [False, False, False],  # Row 1: current position
            [False, False, False]   # Row 2
        ]

        # Should find alternative direction since direct path is blocked
        best_direction = find_best_direction(current_pos, target_pos, collision_map, is_fleeing=False)

        # Should not be NORTH since that's blocked
        assert best_direction != Direction.NORTH
        # Should be one of the alternatives (NORTHEAST or NORTHWEST)
        assert best_direction in [Direction.NORTHEAST, Direction.NORTHWEST, Direction.EAST, Direction.WEST]

    def test_flee_direction_with_obstacles(self):
        """Test that flee direction finding works around obstacles."""
        current_pos = Position(128, 128)  # Tile (1, 1)
        threat_pos = Position(128, 256)   # Tile (1, 2) - directly south

        # Create collision map with blocked tile to the north (ideal flee direction)
        collision_map = [
            [False, True, False],   # Row 0: ideal flee direction is blocked
            [False, False, False],  # Row 1: current position
            [False, False, False]   # Row 2: threat position
        ]

        # Should find alternative flee direction since direct path is blocked
        best_direction = find_best_direction(current_pos, threat_pos, collision_map, is_fleeing=True)

        # Should not be NORTH since that's blocked
        assert best_direction != Direction.NORTH
        # Should be one of the alternatives
        assert best_direction in [Direction.NORTHEAST, Direction.NORTHWEST, Direction.EAST, Direction.WEST]

    def test_pathfinding_integration_with_chase(self):
        """Test that aggressive monsters use pathfinding when chasing."""
        monster = Monster(
            id="chase_pathfinding_test",
            name="Chase Pathfinding Monster",
            position=Position(128, 128),
            asset_id="test_asset",
            stats=Stats(hp=25, max_hp=25, mp=0, max_mp=0, strength=6, defense=2, speed=7),
            monster_type="goblin",
            ai_behavior="aggressive",
            experience_reward=15
        )

        # Set up AI behavior in chasing state with smaller attack range
        monster.ai_behavior_instance = AIBehavior(
            current_state=AIState.CHASING,
            chase_speed_multiplier=1.5,
            attack_range=0.5  # Smaller attack range so monster won't immediately attack
        )

        # Player position with obstacle in direct path
        player_position = Position(256, 128)  # To the east, 1 tile away

        # Create collision map with blocked direct path (larger map to accommodate movement)
        collision_map = [
            [False, False, False, False, False],  # Row 0
            [False, False, True, False, False],   # Row 1: monster at (1,1), blocked tile at (2,1), player at (2,1)
            [False, False, False, False, False],  # Row 2
            [False, False, False, False, False],  # Row 3
            [False, False, False, False, False]   # Row 4
        ]

        # Update AI behavior - should use pathfinding to go around obstacle
        new_position = _update_ai_behavior(
            monster, monster.ai_behavior_instance, player_position, 0.1, collision_map, 64.0
        )

        # Monster should have moved (found alternative path)
        assert new_position != monster.position
        assert monster.ai_behavior_instance.current_state == AIState.CHASING

    def test_pathfinding_integration_with_flee(self):
        """Test that passive monsters use pathfinding when fleeing."""
        monster = Monster(
            id="flee_pathfinding_test",
            name="Flee Pathfinding Monster",
            position=Position(128, 128),
            asset_id="test_asset",
            stats=Stats(hp=10, max_hp=10, mp=0, max_mp=0, strength=2, defense=1, speed=8),
            monster_type="peaceful_animal",
            ai_behavior="peaceful",
            experience_reward=5
        )

        # Set up AI behavior in fleeing state with larger min_safe_distance
        monster.ai_behavior_instance = AIBehavior(
            current_state=AIState.FLEEING,
            flee_speed_multiplier=2.0,
            min_safe_distance=15.0  # Larger safe distance so monster won't stop fleeing
        )

        # Player position with obstacle in direct flee path
        player_position = Position(128, 256)  # Directly south, 1 tile away

        # Create collision map with blocked ideal flee direction (north)
        collision_map = [
            [False, True, False, False, False],   # Row 0: blocked ideal flee direction
            [False, False, False, False, False],  # Row 1: monster position
            [False, False, False, False, False],  # Row 2: player position
            [False, False, False, False, False],  # Row 3
            [False, False, False, False, False]   # Row 4
        ]

        # Update AI behavior - should use pathfinding to find alternative flee route
        new_position = _update_ai_behavior(
            monster, monster.ai_behavior_instance, player_position, 0.1, collision_map, 64.0
        )

        # Monster should have moved (found alternative flee path) or at least tried to move
        # The key test is that it stays in FLEEING state and doesn't crash
        assert monster.ai_behavior_instance.current_state == AIState.FLEEING

        # Test that the pathfinding logic at least finds a direction
        flee_direction = find_best_direction(monster.position, player_position, collision_map, is_fleeing=True)
        # Should find an alternative since north is blocked
        assert flee_direction != Direction.NORTH
