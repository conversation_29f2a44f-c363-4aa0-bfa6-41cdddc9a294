"""
Unit tests for player equipment and inventory system.
"""

import pytest
from src.game_core.entities import Player, Position, Stats
from src.game_core.config import get_config
from src.game_data.items import DEFAULT_STARTING_EQUIPMENT, DEFAULT_STARTING_INVENTORY


class TestPlayerEquipmentSystem:
    """Test the player equipment system."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.player = Player(
            id="test_player",
            name="Test Hero",
            position=Position(100, 100),
            asset_id="player.hero",
            stats=Stats(hp=100, max_hp=100, mp=50, max_mp=50, strength=10, defense=5, speed=8),
            size=(32, 32),
            level=1,
            experience=0
        )
    
    def test_player_has_equipment_slots(self):
        """Test that player has all required equipment slots."""
        assert hasattr(self.player, 'head_equipment')
        assert hasattr(self.player, 'chest_equipment')
        assert hasattr(self.player, 'legs_equipment')
        assert hasattr(self.player, 'boots_equipment')
        assert hasattr(self.player, 'main_hand_weapon')
        assert hasattr(self.player, 'off_hand_equipment')
        
        # All slots should start empty
        assert self.player.head_equipment is None
        assert self.player.chest_equipment is None
        assert self.player.legs_equipment is None
        assert self.player.boots_equipment is None
        assert self.player.main_hand_weapon is None
        assert self.player.off_hand_equipment is None
    
    def test_player_has_inventory_system(self):
        """Test that player has inventory system."""
        config = get_config()
        expected_max_size = config.inventory_ui.inventory_max_slots

        assert hasattr(self.player, 'inventory')
        assert hasattr(self.player, 'inventory_max_size')
        assert isinstance(self.player.inventory, dict)
        assert self.player.inventory_max_size == expected_max_size
    
    def test_add_item_to_inventory(self):
        """Test adding items to inventory."""
        # Add new item
        updated_player = self.player.add_item_to_inventory("bread", 3)
        assert updated_player.inventory["bread"] == 3
        assert updated_player is not self.player  # Immutable
        
        # Add to existing item
        updated_player2 = updated_player.add_item_to_inventory("bread", 2)
        assert updated_player2.inventory["bread"] == 5
    
    def test_add_item_inventory_full(self):
        """Test adding items when inventory is full."""
        config = get_config()
        max_slots = config.inventory_ui.inventory_max_slots

        # Fill inventory to max capacity
        player_with_items = self.player
        for i in range(max_slots):
            player_with_items = player_with_items.add_item_to_inventory(f"item_{i}", 1)
        
        # Try to add one more unique item - should fail
        with pytest.raises(ValueError, match="Inventory is full"):
            player_with_items.add_item_to_inventory("new_item", 1)
    
    def test_remove_item_from_inventory(self):
        """Test removing items from inventory."""
        # Add items first
        player_with_items = self.player.add_item_to_inventory("bread", 5)
        
        # Remove some items
        updated_player = player_with_items.remove_item_from_inventory("bread", 2)
        assert updated_player.inventory["bread"] == 3
        
        # Remove all remaining items
        updated_player2 = updated_player.remove_item_from_inventory("bread", 3)
        assert "bread" not in updated_player2.inventory
    
    def test_remove_item_not_found(self):
        """Test removing items that don't exist."""
        with pytest.raises(ValueError, match="not found in inventory"):
            self.player.remove_item_from_inventory("nonexistent", 1)
    
    def test_remove_item_insufficient_quantity(self):
        """Test removing more items than available."""
        player_with_items = self.player.add_item_to_inventory("bread", 2)
        
        with pytest.raises(ValueError, match="Not enough"):
            player_with_items.remove_item_from_inventory("bread", 5)
    
    def test_has_item(self):
        """Test checking if player has items."""
        player_with_items = self.player.add_item_to_inventory("bread", 3)
        
        assert player_with_items.has_item("bread", 1)
        assert player_with_items.has_item("bread", 3)
        assert not player_with_items.has_item("bread", 4)
        assert not player_with_items.has_item("nonexistent", 1)
    
    def test_inventory_space_methods(self):
        """Test inventory space tracking methods."""
        config = get_config()
        max_slots = config.inventory_ui.inventory_max_slots

        # Empty inventory
        assert self.player.get_inventory_space_used() == 0
        assert self.player.get_inventory_space_remaining() == max_slots

        # Add some items
        player_with_items = self.player.add_item_to_inventory("bread", 3)
        player_with_items = player_with_items.add_item_to_inventory("sword", 1)

        assert player_with_items.get_inventory_space_used() == 2
        assert player_with_items.get_inventory_space_remaining() == max_slots - 2
    
    def test_level_up_preserves_equipment_and_inventory(self):
        """Test that leveling up preserves equipment and inventory."""
        # Add equipment and inventory
        player_with_gear = self.player.add_item_to_inventory("bread", 3)
        player_with_gear = Player(
            id=player_with_gear.id,
            name=player_with_gear.name,
            position=player_with_gear.position,
            asset_id=player_with_gear.asset_id,
            stats=player_with_gear.stats,
            size=player_with_gear.size,
            level=player_with_gear.level,
            experience=player_with_gear.experience,
            head_equipment="cloth_cap",
            chest_equipment="cloth_shirt",
            legs_equipment="cloth_pants",
            boots_equipment="simple_shoes",
            main_hand_weapon="rusty_sword",
            off_hand_equipment=None,
            inventory=player_with_gear.inventory,
            inventory_max_size=player_with_gear.inventory_max_size
        )
        
        # Level up
        leveled_player = player_with_gear.level_up()
        
        # Check that equipment and inventory are preserved
        assert leveled_player.head_equipment == "cloth_cap"
        assert leveled_player.chest_equipment == "cloth_shirt"
        assert leveled_player.inventory["bread"] == 3
        assert leveled_player.level == 2


class TestStartingEquipmentData:
    """Test starting equipment data definitions."""
    
    def test_starting_equipment_constants_exist(self):
        """Test that starting equipment constants are defined."""
        assert DEFAULT_STARTING_EQUIPMENT is not None
        assert DEFAULT_STARTING_INVENTORY is not None
        assert isinstance(DEFAULT_STARTING_EQUIPMENT, dict)
        assert isinstance(DEFAULT_STARTING_INVENTORY, dict)
    
    def test_starting_equipment_has_all_slots(self):
        """Test that starting equipment covers all equipment slots."""
        expected_slots = [
            "head_equipment", "chest_equipment", "legs_equipment", 
            "boots_equipment", "main_hand_weapon", "off_hand_equipment"
        ]
        
        for slot in expected_slots:
            assert slot in DEFAULT_STARTING_EQUIPMENT
    
    def test_starting_equipment_items(self):
        """Test specific starting equipment items."""
        assert DEFAULT_STARTING_EQUIPMENT["head_equipment"] == "cloth_cap"
        assert DEFAULT_STARTING_EQUIPMENT["chest_equipment"] == "cloth_shirt"
        assert DEFAULT_STARTING_EQUIPMENT["legs_equipment"] == "cloth_pants"
        assert DEFAULT_STARTING_EQUIPMENT["boots_equipment"] == "simple_shoes"
        assert DEFAULT_STARTING_EQUIPMENT["main_hand_weapon"] == "rusty_sword"
        assert DEFAULT_STARTING_EQUIPMENT["off_hand_equipment"] is None
    
    def test_starting_inventory_items(self):
        """Test starting inventory items."""
        assert "bread" in DEFAULT_STARTING_INVENTORY
        assert "health_potion" in DEFAULT_STARTING_INVENTORY
        assert "gold_coin" in DEFAULT_STARTING_INVENTORY
        
        assert DEFAULT_STARTING_INVENTORY["bread"] == 3
        assert DEFAULT_STARTING_INVENTORY["health_potion"] == 2
        assert DEFAULT_STARTING_INVENTORY["gold_coin"] == 25
