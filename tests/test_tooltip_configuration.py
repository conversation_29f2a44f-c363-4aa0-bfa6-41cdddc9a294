"""
Test tooltip configuration and sizing.
"""

import pytest
import pygame
from unittest.mock import Mock

from src.game_core.config import initialize_config, get_config
from src.presentation.ui.tooltip import <PERSON>lt<PERSON><PERSON><PERSON><PERSON>, TooltipData
from src.application.interfaces import GameStateData


class TestTooltipConfiguration:
    """Test tooltip configuration and rendering."""
    
    def setup_method(self):
        """Set up test environment."""
        pygame.init()
        # Initialize configuration to ensure it's loaded
        initialize_config("game_config.yaml")
    
    def teardown_method(self):
        """Clean up after tests."""
        pygame.quit()
    
    def test_tooltip_config_values(self):
        """Test that tooltip configuration values are loaded correctly."""
        config = get_config().inventory_ui

        # Verify the base values (before scaling)
        assert config.tooltip_max_width == 300       # Base width
        assert config.tooltip_padding == 8           # Base padding
        assert config.tooltip_line_spacing == 2      # Base line spacing

        # Font sizes are now handled by centralized UI scaling
        ui_config = get_config().ui_scaling
        assert ui_config.scale_factor >= 0.5  # Reasonable scale factor
    
    def test_tooltip_renderer_uses_config(self):
        """Test that TooltipRenderer uses the configuration values."""
        renderer = TooltipRenderer()
        config = get_config()

        # Check that the renderer uses configured values (should be scaled)
        # The exact scaling depends on the UI config, so we just verify they're reasonable values
        assert renderer.padding >= config.inventory_ui.tooltip_padding
        assert renderer.line_spacing >= config.inventory_ui.tooltip_line_spacing
        assert renderer.max_width >= config.inventory_ui.tooltip_max_width

        # Verify they're reasonable values (either raw config or scaled)
        # In test environment, scaling might not work due to pygame initialization issues
        assert renderer.padding > 0
        assert renderer.line_spacing > 0
        assert renderer.max_width > 0
        
        # Check that fonts are created (we can't easily test exact sizes due to pygame font rendering)
        assert renderer.fonts["title"] is not None
        assert renderer.fonts["text"] is not None
        assert renderer.fonts["small"] is not None
    
    def test_tooltip_data_max_width(self):
        """Test that TooltipData uses the configured max width."""
        renderer = TooltipRenderer()
        config = get_config()

        # Create a mock game state
        mock_game_state = Mock(spec=GameStateData)
        mock_game_state.player = None

        # Test tooltip creation (this will use the configured max_width)
        tooltip = TooltipData(
            title="Test Item",
            lines=["Test line 1", "Test line 2"],
            position=(0, 0),
            max_width=renderer.max_width
        )

        # Check that the tooltip uses the renderer's max width (which should be scaled)
        assert tooltip.max_width == renderer.max_width
        assert tooltip.max_width >= config.inventory_ui.tooltip_max_width
    
    def test_tooltip_font_sizes_are_scalable(self):
        """Test that tooltip font sizes use the centralized UI scaling system."""
        from src.editor.ui_config import get_font_size, FONT_LARGE, FONT_NORMAL, FONT_SMALL

        # Font sizes should be scalable through the centralized system
        title_size = get_font_size(FONT_LARGE)
        text_size = get_font_size(FONT_NORMAL)
        small_size = get_font_size(FONT_SMALL)

        # Sizes should be reasonable and properly scaled
        assert title_size > text_size > small_size
        assert 12 <= small_size <= 100  # Reasonable range
        assert 16 <= text_size <= 120   # Reasonable range
        assert 20 <= title_size <= 150  # Reasonable range
    
    def test_tooltip_spacing_and_padding_use_scaling(self):
        """Test that spacing and padding values use the centralized scaling system."""
        from src.editor.ui_config import scale_value
        config = get_config().inventory_ui

        # Base values (before scaling)
        base_padding = 8
        base_line_spacing = 2
        base_max_width = 300

        # Values should match the base values
        assert config.tooltip_padding == base_padding
        assert config.tooltip_line_spacing == base_line_spacing
        assert config.tooltip_max_width == base_max_width

        # Scaling should be applied through the centralized system
        scaled_padding = scale_value(config.tooltip_padding)
        scaled_spacing = scale_value(config.tooltip_line_spacing)
        scaled_width = scale_value(config.tooltip_max_width)

        # Scaled values should be reasonable
        assert scaled_padding >= config.tooltip_padding
        assert scaled_spacing >= config.tooltip_line_spacing
        assert scaled_width >= config.tooltip_max_width
    
    def test_tooltip_renderer_initialization(self):
        """Test that TooltipRenderer initializes without errors."""
        # This should not raise any exceptions
        renderer = TooltipRenderer()
        
        # Basic sanity checks
        assert renderer.colors is not None
        assert renderer.fonts is not None
        assert renderer.padding > 0
        assert renderer.line_spacing >= 0
        assert renderer.max_width > 0
    
    def test_tooltip_configuration_is_customizable(self):
        """Test that tooltip configuration can be customized through centralized UI scaling."""
        config = get_config().inventory_ui
        ui_config = get_config().ui_scaling

        # Base values should be reasonable
        assert 200 <= config.tooltip_max_width <= 800
        assert 4 <= config.tooltip_padding <= 20
        assert 0 <= config.tooltip_line_spacing <= 10

        # UI scaling should be configurable
        assert 0.5 <= ui_config.scale_factor <= 3.0
