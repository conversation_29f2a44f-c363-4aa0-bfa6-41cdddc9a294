"""
Integration tests for the complete system.

These tests verify that the layers work together correctly.
"""

import pytest
import tempfile
import os
from pathlib import Path

from src.infrastructure import PygameEventBus, MapParser, LevelRepository
from src.application import MovePlayerUseCase, GameStateData
from src.game_core import Player, Position, Direction, Stats
from src.game_core.config import get_config


class TestEventBusIntegration:
    """Test that the event bus works correctly with use cases."""
    
    def test_event_bus_publishes_player_move_event(self):
        # Setup
        event_bus = PygameEventBus()
        
        # Create a mock subscriber
        received_events = []
        def event_handler(event):
            received_events.append(event)
        
        event_bus.subscribe("player_moved", event_handler)
        
        # Create use case and execute
        config = get_config()
        use_case = MovePlayerUseCase(
            event_bus,
            tile_size=config.rendering.tile_size,
            move_speed=config.movement.player_move_speed * config.rendering.tile_size
        )
        
        player = Player(
            id="player1",
            name="Hero",
            position=Position(192, 192),  # Center of tile (1,1) with tile_size=128
            asset_id="player.hero",
            stats=Stats(hp=100, max_hp=100, mp=50, max_mp=50,
                       strength=10, defense=5, speed=8),
            size=(32, 32),
            level=1,
            experience=0
        )
        
        game_state = GameStateData(
            player=player,
            monsters={},
            items={},
            current_level_id="test_level",
            collision_map=[[False, False, False] for _ in range(3)]
        )
        
        # Execute move
        new_game_state = use_case.execute(game_state, Direction.NORTH, 0.016)  # 60 FPS delta time

        # Verify movement happened (player should have moved north)
        assert new_game_state.player.position.y < game_state.player.position.y

        # Verify event was received
        assert len(received_events) == 1
        event = received_events[0]
        assert event.event_type == "player_moved"
        assert event.old_position == Position(192, 192)
        expected_distance = config.movement.player_move_speed * config.rendering.tile_size * 0.016
        assert abs(event.new_position.y - (192 - expected_distance)) < 0.1  # Moved north by expected distance


class TestMapParserIntegration:
    """Test that the map parser works with real files."""
    
    def test_parse_simple_map(self):
        # Create temporary base legend
        base_legend_content = """
'#':
  category: tile
  type: wall
  solid: true
  asset_id: "tile.wall.stone"

'.':
  category: tile
  type: floor
  solid: false
  asset_id: "tile.floor.dirt"

'@':
  category: entity
  type: player
  data_id: "player"

'g':
  category: entity
  type: monster
  data_id: "goblin_grunt"
"""
        
        # Create temporary map file
        map_content = """
# Simple test map
legend:
  'T':
    category: tile
    type: tree
    solid: true
    asset_id: "tile.tree.oak"

---
####
#@.#
#.g#
####
"""
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Write files
            base_legend_path = Path(temp_dir) / "base_legend.yaml"
            map_file_path = Path(temp_dir) / "test.map"
            
            with open(base_legend_path, 'w') as f:
                f.write(base_legend_content)
            
            with open(map_file_path, 'w') as f:
                f.write(map_content)
            
            # Test parsing
            parser = MapParser(str(base_legend_path))
            layout_data = parser.parse_map_file(str(map_file_path))
            
            # Verify results
            assert layout_data.width == 4
            assert layout_data.height == 4
            
            # Check collision map
            assert layout_data.collision_map[0][0] is True   # Wall
            assert layout_data.collision_map[1][1] is False  # Floor
            
            # Check entities
            assert len(layout_data.entities) == 2
            
            # Find player and monster
            player_entity = next(e for e in layout_data.entities if e["type"] == "player")
            monster_entity = next(e for e in layout_data.entities if e["type"] == "monster")
            
            assert player_entity["x"] == 1
            assert player_entity["y"] == 1
            assert monster_entity["x"] == 2
            assert monster_entity["y"] == 2


class TestLevelRepositoryIntegration:
    """Test that the level repository can load actual levels."""
    
    def test_load_town_caledon_level(self):
        # Test loading the sample level we created
        base_legend_path = "src/game_data/base_legend.yaml"
        levels_dir = "src/levels"
        
        # Skip if files don't exist (for CI environments)
        if not os.path.exists(base_legend_path) or not os.path.exists(levels_dir):
            pytest.skip("Level files not found")
        
        repository = LevelRepository(levels_dir, base_legend_path)
        
        # Test getting available levels
        available_levels = repository.get_available_levels()
        
        if "town_caledon" in available_levels:
            # Test loading the level
            layout_data = repository.load_level("town_caledon")
            
            assert layout_data.width > 0
            assert layout_data.height > 0
            assert len(layout_data.entities) > 0
            
            # Check that player exists
            player_entities = [e for e in layout_data.entities if e["type"] == "player"]
            assert len(player_entities) == 1
        
        else:
            pytest.skip("town_caledon level not found")


class TestSystemIntegration:
    """Test that multiple components work together."""
    
    def test_complete_workflow(self):
        """Test a complete workflow from level loading to player movement."""
        
        # This test demonstrates the complete architecture working together
        # In a real game, this would be the main game loop
        
        # 1. Setup infrastructure
        event_bus = PygameEventBus()
        
        # 2. Create use cases
        config = get_config()
        move_use_case = MovePlayerUseCase(
            event_bus,
            tile_size=config.rendering.tile_size,
            move_speed=config.movement.player_move_speed * config.rendering.tile_size
        )
        
        # 3. Create a simple game state
        player = Player(
            id="player1",
            name="Hero",
            position=Position(192, 192),  # Center of tile (1,1) with tile_size=128
            asset_id="player.hero",
            stats=Stats(hp=100, max_hp=100, mp=50, max_mp=50,
                       strength=10, defense=5, speed=8),
            size=(32, 32),
            level=1,
            experience=0
        )
        
        game_state = GameStateData(
            player=player,
            monsters={},
            items={},
            current_level_id="test_level",
            collision_map=[
                [False, False, False],
                [False, False, False],
                [False, False, False]
            ]
        )
        
        # 4. Set up event tracking
        events_received = []
        def track_events(event):
            events_received.append(event)
        
        event_bus.subscribe("player_moved", track_events)
        
        # 5. Execute player actions
        # Move right
        game_state = move_use_case.execute(game_state, Direction.EAST, 0.016)  # 60 FPS delta time
        # With pixel-based movement, player should have moved slightly east
        expected_distance = config.movement.player_move_speed * config.rendering.tile_size * 0.016
        expected_x_after_east = 192 + expected_distance
        assert abs(game_state.player.position.x - expected_x_after_east) < 0.1  # X should increase by expected distance
        assert game_state.player.position.y == 192  # Y should remain the same

        # Move down
        game_state = move_use_case.execute(game_state, Direction.SOUTH, 0.016)  # 60 FPS delta time
        # After moving east then south, position should be (expected_x_after_east, 192 + expected_distance)
        expected_y_after_south = 192 + expected_distance
        assert abs(game_state.player.position.x - expected_x_after_east) < 0.1  # X should remain the same
        assert abs(game_state.player.position.y - expected_y_after_south) < 0.1  # Y should increase by expected distance
        
        # 6. Verify events were published
        assert len(events_received) == 2
        assert events_received[0].event_type == "player_moved"
        assert events_received[1].event_type == "player_moved"
        
        # 7. Verify game state consistency
        assert game_state.current_level_id == "test_level"
        assert game_state.player.name == "Hero"
        assert game_state.player.level == 1
        
        print("✓ Complete system integration test passed!")
        print(f"  - Player moved from (1,1) to {game_state.player.position}")
        print(f"  - {len(events_received)} events were published")
        print(f"  - Event bus has {event_bus.get_statistics()['total_events_published']} total events")
