"""
Integration tests for the wander system.

These tests verify the wander system works correctly with the full application stack.
"""

import pytest
import time
from unittest.mock import Mock, patch
from src.game_core import <PERSON><PERSON><PERSON>, NPC, Monster, Stats
from src.game_core.wander_system import <PERSON><PERSON>Behavior, WanderState
from src.application import UpdateWanderingEntitiesUseCase, CreateNPCUseCase, GameStateData
from src.application.interfaces import IEventBus


class MockEventBus(IEventBus):
    """Mock event bus for testing."""
    
    def __init__(self):
        self.published_events = []
    
    def publish(self, event):
        self.published_events.append(event)
    
    def subscribe(self, event_type, handler):
        pass
    
    def unsubscribe(self, event_type, handler):
        pass


class TestWanderSystemIntegration:
    """Integration tests for the complete wander system."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.event_bus = MockEventBus()
        self.create_npc_use_case = CreateNPCUseCase(self.event_bus)
        self.update_wander_use_case = UpdateWanderingEntitiesUseCase(self.event_bus)
    
    def create_test_game_state(self):
        """Create a test game state with wandering entities."""
        # Create NPCs using the use case (this will set up wander behavior correctly)
        commoner = self.create_npc_use_case.execute("commoner", Position(256, 256))
        merchant = self.create_npc_use_case.execute("merchant", Position(384, 256))
        guard = self.create_npc_use_case.execute("guard", Position(512, 256))
        
        # Create a monster with wander behavior
        monster = Monster(
            id="test_monster",
            name="Test Monster",
            position=Position(640, 256),
            asset_id="monster.test",
            stats=Stats(hp=50, max_hp=50, mp=0, max_mp=0, strength=10, defense=5, speed=8),
            monster_type="test_monster"
        )
        monster.wander_behavior = WanderBehavior(enabled=True, wander_radius=3.0)
        
        # Create collision map (10x10, all passable)
        collision_map = [[False for _ in range(10)] for _ in range(10)]
        
        return GameStateData(
            npcs={
                commoner.id: commoner,
                merchant.id: merchant,
                guard.id: guard
            },
            monsters={
                monster.id: monster
            },
            collision_map=collision_map,
            level_tiles=[["grass" for _ in range(10)] for _ in range(10)]
        )
    
    def test_npc_creation_with_wander_behavior(self):
        """Test that NPCs are created with correct wander behavior."""
        # Commoner should wander
        commoner = self.create_npc_use_case.execute("commoner", Position(256, 256))
        assert commoner.wander_behavior is not None
        assert commoner.wander_behavior.enabled is True
        assert commoner.wander_behavior.current_state == WanderState.IDLE
        
        # Merchant should not wander
        merchant = self.create_npc_use_case.execute("merchant", Position(384, 256))
        assert merchant.wander_behavior is not None
        assert merchant.wander_behavior.enabled is False
        
        # Guard should not wander
        guard = self.create_npc_use_case.execute("guard", Position(512, 256))
        assert guard.wander_behavior is not None
        assert guard.wander_behavior.enabled is False
    
    def test_wander_use_case_execution(self):
        """Test the UpdateWanderingEntitiesUseCase."""
        game_state = self.create_test_game_state()
        
        # Mock config
        config = Mock()
        config.rendering = Mock()
        config.rendering.tile_size = 128
        config.movement = Mock()
        config.movement.player_move_speed = 6.67
        
        # Execute the use case
        updated_state = self.update_wander_use_case.execute(game_state, 0.1, config)
        
        # Should return updated game state
        assert isinstance(updated_state, GameStateData)
        assert len(updated_state.npcs) == len(game_state.npcs)
        assert len(updated_state.monsters) == len(game_state.monsters)
        
        # All entities should still have their wander behaviors
        for npc_id, npc in updated_state.npcs.items():
            assert hasattr(npc, 'wander_behavior')
            assert npc.wander_behavior is not None
        
        for monster_id, monster in updated_state.monsters.items():
            assert hasattr(monster, 'wander_behavior')
            assert monster.wander_behavior is not None
    
    @patch('time.time')
    def test_commoner_wandering_behavior(self, mock_time):
        """Test that commoners actually wander over time."""
        mock_time.return_value = 100.0
        
        game_state = self.create_test_game_state()
        
        # Find the commoner
        commoner = None
        for npc in game_state.npcs.values():
            if npc.npc_type == "commoner":
                commoner = npc
                break
        
        assert commoner is not None
        original_position = commoner.position
        
        # Mock config
        config = Mock()
        config.rendering = Mock()
        config.rendering.tile_size = 128
        config.movement = Mock()
        config.movement.player_move_speed = 6.67
        
        # Force the commoner's idle state to expire
        commoner.wander_behavior.state_start_time = 90.0  # 10 seconds ago
        commoner.wander_behavior.state_duration = 5.0     # Should have expired 5 seconds ago
        
        # Update multiple times to allow movement
        updated_state = game_state
        for i in range(10):  # Multiple updates
            mock_time.return_value = 100.0 + i * 0.1
            updated_state = self.update_wander_use_case.execute(updated_state, 0.1, config)
        
        # Find the updated commoner
        updated_commoner = None
        for npc in updated_state.npcs.values():
            if npc.npc_type == "commoner":
                updated_commoner = npc
                break
        
        assert updated_commoner is not None
        
        # The commoner should have either moved or at least changed state
        # (movement depends on finding valid targets, but state should change)
        assert (updated_commoner.position != original_position or 
                updated_commoner.wander_behavior.current_state != WanderState.IDLE)
    
    def test_merchant_stays_stationary(self):
        """Test that merchants don't wander."""
        game_state = self.create_test_game_state()
        
        # Find the merchant
        merchant = None
        for npc in game_state.npcs.values():
            if npc.npc_type == "merchant":
                merchant = npc
                break
        
        assert merchant is not None
        original_position = merchant.position
        
        # Mock config
        config = Mock()
        config.rendering = Mock()
        config.rendering.tile_size = 128
        config.movement = Mock()
        config.movement.player_move_speed = 6.67
        
        # Update multiple times
        updated_state = game_state
        for _ in range(10):
            updated_state = self.update_wander_use_case.execute(updated_state, 0.1, config)
        
        # Find the updated merchant
        updated_merchant = None
        for npc in updated_state.npcs.values():
            if npc.npc_type == "merchant":
                updated_merchant = npc
                break
        
        assert updated_merchant is not None
        
        # Merchant should not have moved
        assert updated_merchant.position == original_position
    
    def test_wander_with_obstacles(self):
        """Test wandering behavior with obstacles in the environment."""
        game_state = self.create_test_game_state()
        
        # Create collision map with some obstacles
        collision_map = [[False for _ in range(10)] for _ in range(10)]
        # Add some walls
        for i in range(10):
            collision_map[5][i] = True  # Horizontal wall
        
        game_state.collision_map = collision_map
        
        # Mock config
        config = Mock()
        config.rendering = Mock()
        config.rendering.tile_size = 128
        config.movement = Mock()
        config.movement.player_move_speed = 6.67
        
        # Update multiple times - should not crash even with obstacles
        updated_state = game_state
        for _ in range(5):
            updated_state = self.update_wander_use_case.execute(updated_state, 0.1, config)
        
        # Should complete without errors
        assert isinstance(updated_state, GameStateData)
        assert len(updated_state.npcs) == len(game_state.npcs)
        assert len(updated_state.monsters) == len(game_state.monsters)
    
    def test_wander_configuration_integration(self):
        """Test that wander system respects configuration values."""
        # Create NPC with custom configuration
        with patch('src.game_core.config.get_config') as mock_get_config:
            # Mock config with custom wander settings
            mock_config = Mock()
            mock_config.wander = Mock()
            mock_config.wander.npc_wander_radius = 6.0
            mock_config.wander.npc_move_speed_multiplier = 0.3
            mock_config.wander.npc_idle_time_min = 1.0
            mock_config.wander.npc_idle_time_max = 2.0
            mock_get_config.return_value = mock_config
            
            # Create NPC - should use custom config values
            commoner = self.create_npc_use_case.execute("commoner", Position(256, 256))
            
            assert commoner.wander_behavior.wander_radius == 6.0
            assert commoner.wander_behavior.move_speed == 0.3
            assert commoner.wander_behavior.idle_time_min == 1.0
            assert commoner.wander_behavior.idle_time_max == 2.0
    
    def test_error_handling_with_invalid_entities(self):
        """Test that the system handles entities without wander behavior gracefully."""
        # Create NPC without wander behavior
        npc_without_wander = NPC(
            id="no_wander_npc",
            name="No Wander NPC",
            position=Position(256, 256),
            asset_id="test_asset",
            npc_type="test",
            behavior="dialog"
        )
        # Explicitly don't set wander_behavior
        
        game_state = GameStateData(
            npcs={"no_wander": npc_without_wander},
            monsters={},
            collision_map=[[False for _ in range(5)] for _ in range(5)],
            level_tiles=[["grass" for _ in range(5)] for _ in range(5)]
        )
        
        # Mock config
        config = Mock()
        config.rendering = Mock()
        config.rendering.tile_size = 128
        config.movement = Mock()
        config.movement.player_move_speed = 6.67
        
        # Should handle gracefully without crashing
        updated_state = self.update_wander_use_case.execute(game_state, 0.1, config)
        
        assert isinstance(updated_state, GameStateData)
        assert len(updated_state.npcs) == 1
        # NPC should remain unchanged
        assert updated_state.npcs["no_wander"].position == npc_without_wander.position
