"""
Integration tests for the animation system.

Tests the animation system's integration with the game engine,
event system, and rendering pipeline.
"""

import pytest
import time
from unittest.mock import Mock, patch

from src.game_core import (
    Player, Position, Stats, Vector2, PlayerAttackedEvent,
    AnimationTransform
)
from src.application import GameStateData
from src.application.animation_manager import AnimationManager
from src.infrastructure.events.pygame_event_bus import PygameEventBus


class TestAnimationIntegration:
    """Test animation system integration."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.event_bus = PygameEventBus()
        self.animation_manager = AnimationManager(self.event_bus)
        
        # Create test player with weapon
        self.player = Player(
            id="test_player",
            name="Test Hero",
            position=Position(100, 100),
            asset_id="player.hero",
            stats=Stats(hp=100, max_hp=100, mp=50, max_mp=50, strength=10, defense=5, speed=8),
            size=(32, 32),
            level=1,
            experience=0,
            main_hand_weapon="rusty_sword"
        )
        
        self.game_state = GameStateData(
            player=self.player,
            monsters={},
            items={},
            current_level_id="test_level",
            collision_map=[[False]],
            level_tiles=[[]]
        )
    
    def test_animation_manager_responds_to_player_attack_event(self):
        """Test that animation manager responds to PlayerAttackedEvent."""
        # Set up event handler
        animation_started = []
        
        def on_attack_event(event):
            """Handle attack event by starting animation."""
            if hasattr(event, 'direction_vector'):
                self.animation_manager.start_attack_animation(self.player, event.direction_vector)
                animation_started.append(True)
        
        # Subscribe to attack events
        self.event_bus.subscribe("player_attacked", on_attack_event)
        
        # Create and publish attack event
        attack_event = PlayerAttackedEvent(
            player_id=self.player.id,
            direction_vector=Vector2(1.0, 0.0),
            attack_power=10,
            timestamp=time.time()
        )
        
        self.event_bus.publish(attack_event)
        
        # Verify animation was started
        assert len(animation_started) == 1
        assert self.animation_manager.is_animation_active()
        assert self.animation_manager.animation_state.animation_type == "attacking"
    
    @patch('src.application.animation_manager.get_item_definition')
    def test_animation_transform_calculation_with_real_weapon(self, mock_get_item_def):
        """Test animation transform calculation with real weapon data."""
        from src.game_data import ItemDefinition, ItemType
        
        # Mock sword weapon
        mock_get_item_def.return_value = ItemDefinition(
            id="rusty_sword", name="Rusty Sword", asset_id="test",
            item_type=ItemType.WEAPON, 
            properties={"weapon_type": "sword", "attack_speed": 1.0}
        )
        
        # Start attack animation
        direction = Vector2(1.0, 0.0)
        self.animation_manager.start_attack_animation(self.player, direction)
        
        # Get transform immediately after starting
        transform = self.animation_manager.get_current_animation_transform()
        
        # Should have some rotation for melee weapon
        assert isinstance(transform, AnimationTransform)
        # At the start of animation, rotation might be small but should be non-zero soon
        
        # Wait a bit and check again
        time.sleep(0.05)  # 50ms
        transform = self.animation_manager.get_current_animation_transform()
        
        # Should have rotation for melee weapon, no offset
        assert transform.offset_x == 0.0
        assert transform.offset_y == 0.0
    
    @patch('src.application.animation_manager.get_item_definition')
    def test_animation_transform_calculation_with_bow(self, mock_get_item_def):
        """Test animation transform calculation with bow weapon."""
        from src.game_data import ItemDefinition, ItemType
        
        # Mock bow weapon
        mock_get_item_def.return_value = ItemDefinition(
            id="wooden_bow", name="Wooden Bow", asset_id="test",
            item_type=ItemType.WEAPON, 
            properties={"weapon_type": "bow", "attack_speed": 0.8}
        )
        
        # Update player weapon
        bow_player = Player(
            id="test_player", name="Test Hero", position=Position(100, 100),
            asset_id="player.hero", stats=Stats(hp=100, max_hp=100, mp=50, max_mp=50, strength=10, defense=5, speed=8),
            size=(32, 32), level=1, experience=0, main_hand_weapon="wooden_bow"
        )
        
        # Start attack animation
        direction = Vector2(1.0, 0.0)
        self.animation_manager.start_attack_animation(bow_player, direction)
        
        # Wait a bit for animation to progress
        time.sleep(0.05)  # 50ms
        transform = self.animation_manager.get_current_animation_transform()
        
        # Should have offset for ranged weapon, no rotation
        assert transform.rotation == 0.0
        # Offset might be small at start, but should be present
        assert isinstance(transform.offset_x, float)
        assert isinstance(transform.offset_y, float)
    
    def test_animation_lifecycle(self):
        """Test complete animation lifecycle."""
        # Start with idle state
        assert not self.animation_manager.is_animation_active()
        assert self.animation_manager.get_current_animation_type() == "idle"
        
        # Start attack animation
        direction = Vector2(1.0, 0.0)
        self.animation_manager.start_attack_animation(self.player, direction)
        
        # Should be active
        assert self.animation_manager.is_animation_active()
        assert self.animation_manager.get_current_animation_type() == "attacking"
        
        # Update animation (simulate frame updates)
        for _ in range(10):
            self.animation_manager.update_animation(0.016)  # 60 FPS
            time.sleep(0.01)  # Small delay
        
        # Animation might still be active depending on duration
        # Let's wait for it to finish
        time.sleep(0.5)  # Wait 500ms
        self.animation_manager.update_animation(0.016)
        
        # Should return to idle
        assert not self.animation_manager.is_animation_active()
        assert self.animation_manager.get_current_animation_type() == "idle"
    
    def test_animation_configuration_update(self):
        """Test that animation configuration can be updated."""
        # Update configuration
        config = {
            'animations': {
                'attack_base_duration': 500,
                'melee_swing_angle': 20.0,
                'ranged_vibration_amplitude': 3.0,
                'ranged_vibration_frequency': 25.0
            }
        }
        
        self.animation_manager.update_config(config)
        
        # Verify configuration was applied
        assert self.animation_manager.config_attack_base_duration == 500
        assert self.animation_manager.config_melee_swing_angle == 20.0
        assert self.animation_manager.config_ranged_vibration_amplitude == 3.0
        assert self.animation_manager.config_ranged_vibration_frequency == 25.0
    
    def test_multiple_rapid_attacks(self):
        """Test that rapid attacks don't break the animation system."""
        direction = Vector2(1.0, 0.0)
        
        # Start multiple attacks rapidly
        for _ in range(5):
            self.animation_manager.start_attack_animation(self.player, direction)
            time.sleep(0.01)  # 10ms between attacks
        
        # Should still be in attacking state
        assert self.animation_manager.is_animation_active()
        assert self.animation_manager.get_current_animation_type() == "attacking"
        
        # Transform should still be valid
        transform = self.animation_manager.get_current_animation_transform()
        assert isinstance(transform, AnimationTransform)
    
    def test_animation_with_no_weapon(self):
        """Test animation behavior when player has no weapon."""
        # Create player with no weapon
        unarmed_player = Player(
            id="test_player", name="Test Hero", position=Position(100, 100),
            asset_id="player.hero", stats=Stats(hp=100, max_hp=100, mp=50, max_mp=50, strength=10, defense=5, speed=8),
            size=(32, 32), level=1, experience=0, main_hand_weapon=None
        )
        
        # Start attack animation
        direction = Vector2(1.0, 0.0)
        self.animation_manager.start_attack_animation(unarmed_player, direction)
        
        # Should still create animation (unarmed combat)
        assert self.animation_manager.is_animation_active()
        assert self.animation_manager.animation_state.weapon_type == "unarmed"
        
        # Transform should be minimal for unarmed
        transform = self.animation_manager.get_current_animation_transform()
        assert isinstance(transform, AnimationTransform)


class TestAnimationRendererIntegration:
    """Test animation integration with renderer."""
    
    def test_animation_transform_structure(self):
        """Test that AnimationTransform has the expected structure for renderer."""
        transform = AnimationTransform(rotation=15.0, offset_x=2.5, offset_y=-1.0)
        
        # Verify all required attributes exist
        assert hasattr(transform, 'rotation')
        assert hasattr(transform, 'offset_x')
        assert hasattr(transform, 'offset_y')
        
        # Verify types
        assert isinstance(transform.rotation, float)
        assert isinstance(transform.offset_x, float)
        assert isinstance(transform.offset_y, float)
        
        # Verify values
        assert transform.rotation == 15.0
        assert transform.offset_x == 2.5
        assert transform.offset_y == -1.0
