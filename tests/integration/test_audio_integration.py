"""
Integration tests for audio system

Tests for audio integration with the game engine including level audio loading,
background music and ambient sound management, and proper cleanup.
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import tempfile
import shutil
from pathlib import Path

from src.presentation.game_engine import GameEngine
from src.infrastructure.audio.pygame_audio_player import PygameAudioPlayer


class TestAudioIntegration(unittest.TestCase):
    """Integration tests for audio system with game engine."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        
        # Mock dependencies
        self.mock_event_bus = Mock()
        self.mock_asset_manager = Mock()
        self.mock_level_repository = Mock()
        self.mock_move_player_use_case = Mock()
        self.mock_attack_use_case = Mock()
        self.mock_build_level_use_case = Mock()
        self.mock_interact_npc_use_case = Mock()
        self.mock_buy_from_npc_use_case = Mock()
        self.mock_sell_to_npc_use_case = Mock()
        self.mock_update_wandering_entities_use_case = Mock()
        self.mock_tile_interaction_use_case = Mock()
        self.mock_audio_player = Mock(spec=PygameAudioPlayer)
        self.mock_save_repository = Mock()
        
        # Mock level configuration
        self.mock_level_config = Mock()
        self.mock_level_config.background_music = "town_theme"
        self.mock_level_config.ambient_sounds = ["birds", "wind"]
        
        # Create a proper mock layout_data that the level repository returns
        mock_layout_data = Mock()
        mock_layout_data.entities = []  # Empty list instead of Mock
        mock_layout_data.tiles = []
        mock_layout_data.width = 10
        mock_layout_data.height = 10
        mock_layout_data.collision_map = []
        self.mock_level_repository.load_level.return_value = mock_layout_data
        
        # Mock build level use case to return a valid game state
        from src.application.interfaces import GameStateData
        from src.game_core.entities import Player, Position, Stats

        # Create a real Player object for the mock game state
        test_player = Player(
            id="test_player",
            name="Test Hero",
            position=Position(100, 100),
            asset_id="player.hero",
            stats=Stats(hp=100, max_hp=100, mp=50, max_mp=50,
                       strength=10, defense=5, speed=8),
            size=(32, 32),
            level=1,
            experience=0
        )

        mock_game_state = GameStateData(
            player=test_player,
            monsters={},
            items={},
            npcs={},
            current_level_id="test_level",
            collision_map=[],
            level_tiles=[],
            tile_states={}
        )
        self.mock_build_level_use_case.execute.return_value = mock_game_state
        
        # Create game engine with mocked dependencies
        self.game_engine = GameEngine(
            event_bus=self.mock_event_bus,
            asset_manager=self.mock_asset_manager,
            level_repository=self.mock_level_repository,
            move_player_use_case=self.mock_move_player_use_case,
            attack_use_case=self.mock_attack_use_case,
            build_level_use_case=self.mock_build_level_use_case,
            interact_npc_use_case=self.mock_interact_npc_use_case,
            buy_from_npc_use_case=self.mock_buy_from_npc_use_case,
            sell_to_npc_use_case=self.mock_sell_to_npc_use_case,
            update_wandering_entities_use_case=self.mock_update_wandering_entities_use_case,
            tile_interaction_use_case=self.mock_tile_interaction_use_case,
            audio_player=self.mock_audio_player,
            save_repository=self.mock_save_repository
        )
    
    def tearDown(self):
        """Clean up test fixtures."""
        shutil.rmtree(self.temp_dir)
    
    @patch('builtins.__import__')
    def test_load_level_audio_with_background_music_and_ambient(self, mock_import):
        """Test loading level audio with both background music and ambient sounds."""
        # Mock the level module import
        mock_level_module = Mock()
        mock_level_module.LEVEL_CONFIG = {
            "background_music": "town_theme",
            "ambient_sounds": ["birds", "wind"]
        }
        mock_import.return_value = mock_level_module
        
        # Load level with audio
        self.game_engine._load_level("town_caledon")
        
        # Verify background music is played
        self.mock_audio_player.play_music.assert_called_once_with("town_theme", loop=True)
        
        # Verify ambient sounds are played
        expected_ambient_calls = [
            unittest.mock.call("birds", volume=0.3),
            unittest.mock.call("wind", volume=0.3)
        ]
        self.mock_audio_player.play_ambient.assert_has_calls(expected_ambient_calls, any_order=True)
        self.assertEqual(self.mock_audio_player.play_ambient.call_count, 2)
    
    @patch('builtins.__import__')
    def test_load_level_audio_background_music_only(self, mock_import):
        """Test loading level audio with only background music."""
        # Configure level with only background music
        mock_level_module = Mock()
        mock_level_module.LEVEL_CONFIG = {
            "background_music": "dungeon_theme",
            "ambient_sounds": []
        }
        mock_import.return_value = mock_level_module
        
        self.game_engine._load_level("dungeon")
        
        # Verify background music is played
        self.mock_audio_player.play_music.assert_called_once_with("dungeon_theme", loop=True)
        
        # Verify no ambient sounds are played
        self.mock_audio_player.play_ambient.assert_not_called()
    
    @patch('builtins.__import__')
    def test_load_level_audio_ambient_sounds_only(self, mock_import):
        """Test loading level audio with only ambient sounds."""
        # Configure level with only ambient sounds
        mock_level_module = Mock()
        mock_level_module.LEVEL_CONFIG = {
            "ambient_sounds": ["water", "echo"]
        }
        mock_import.return_value = mock_level_module
        
        self.game_engine._load_level("cave")
        
        # Verify no background music is played
        self.mock_audio_player.play_music.assert_not_called()
        
        # Verify ambient sounds are played
        expected_ambient_calls = [
            unittest.mock.call("water", volume=0.3),
            unittest.mock.call("echo", volume=0.3)
        ]
        self.mock_audio_player.play_ambient.assert_has_calls(expected_ambient_calls, any_order=True)
        self.assertEqual(self.mock_audio_player.play_ambient.call_count, 2)
    
    @patch('builtins.__import__')
    def test_load_level_audio_no_audio_config(self, mock_import):
        """Test loading level with no audio configuration."""
        # Configure level with no audio
        mock_level_module = Mock()
        mock_level_module.LEVEL_CONFIG = {}
        mock_import.return_value = mock_level_module
        self.mock_level_config.ambient_sounds = []
        
        mock_level_module = Mock()
        mock_level_module.LEVEL_CONFIG = {}
        mock_import.return_value = mock_level_module
        
        self.game_engine._load_level("silent_level")
        
        # Verify no audio is played
        self.mock_audio_player.play_music.assert_not_called()
        self.mock_audio_player.play_ambient.assert_not_called()
    
    @patch('builtins.__import__')
    def test_load_level_audio_missing_config_attributes(self, mock_import):
        """Test loading level with missing audio configuration attributes."""
        # Configure level config without audio attributes
        mock_level_module = Mock()
        mock_level_module.LEVEL_CONFIG = {}  # Empty config
        mock_import.return_value = mock_level_module
        
        # Should not raise an error, just skip audio loading
        self.game_engine._load_level("basic_level")
        
        # Verify no audio is played
        self.mock_audio_player.play_music.assert_not_called()
        self.mock_audio_player.play_ambient.assert_not_called()
    
    @patch('builtins.__import__')
    def test_load_level_audio_import_error(self, mock_import):
        """Test handling of level import errors during audio loading."""
        # Simulate import error
        mock_import.side_effect = ImportError("Level module not found")
        
        # Should not raise an error, just skip audio loading
        self.game_engine._load_level("nonexistent_level")
        
        # Verify no audio is played
        self.mock_audio_player.play_music.assert_not_called()
        self.mock_audio_player.play_ambient.assert_not_called()
    
    def test_stop_engine_cleans_up_audio(self):
        """Test that stopping the engine properly cleans up audio."""
        self.game_engine.stop()
        
        # Verify all ambient sounds are stopped
        self.mock_audio_player.stop_all_ambient.assert_called_once()
    
    @patch('builtins.__import__')
    def test_multiple_level_loads_clean_up_previous_audio(self, mock_import):
        """Test that loading a new level cleans up previous level's audio."""
        # First level
        mock_level_module1 = Mock()
        mock_level_module1.LEVEL_CONFIG = {
            "background_music": "town_theme",
            "ambient_sounds": ["birds"]
        }
        
        # Second level  
        mock_level_module2 = Mock()
        mock_level_module2.LEVEL_CONFIG = {
            "background_music": "dungeon_theme",
            "ambient_sounds": ["drips"]
        }
        
        # Configure mock to return different modules based on the import call
        def mock_import_side_effect(*args, **kwargs):
            if "level1" in args[0]:
                return mock_level_module1
            elif "level2" in args[0]:
                return mock_level_module2
            return Mock()
        
        mock_import.side_effect = mock_import_side_effect
        
        # Load first level
        self.game_engine._load_level("level1")
        
        # Load second level
        self.game_engine._load_level("level2")
        
        # Verify audio was stopped and restarted between level loads
        # Should have 2 calls to stop_music and stop_all_ambient (once for each level load)
        self.assertEqual(self.mock_audio_player.stop_music.call_count, 2)
        self.assertEqual(self.mock_audio_player.stop_all_ambient.call_count, 2)
        
        # Verify final audio state matches second level
        self.mock_audio_player.play_music.assert_called_with("dungeon_theme", loop=True)
        self.mock_audio_player.play_ambient.assert_called_with("drips", volume=0.3)
        self.mock_audio_player.stop_all_ambient.assert_called()


class TestAudioPlayerInitialization(unittest.TestCase):
    """Test audio player initialization in main.py context."""
    
    @patch('src.infrastructure.audio.pygame_audio_player.pygame')
    def test_audio_player_initialization_with_directories(self, mock_pygame):
        """Test that audio player is initialized with correct directories."""
        # Mock pygame initialization
        mock_pygame.mixer.get_init.return_value = None
        mock_pygame.mixer.init.return_value = None
        
        # Create audio player as done in main.py
        audio_player = PygameAudioPlayer(
            sound_directory="assets/audio/sounds",
            music_directory="assets/audio/music"
        )
        
        # Verify directories are set correctly
        self.assertEqual(str(audio_player.sound_directory), "assets/audio/sounds")
        self.assertEqual(str(audio_player.music_directory), "assets/audio/music")
        
        # Verify pygame initialization
        mock_pygame.mixer.init.assert_called_once_with(
            frequency=22050, size=-16, channels=2, buffer=512
        )


class TestLevelConfigurationAudio(unittest.TestCase):
    """Test level configuration audio settings."""
    
    def test_town_caledon_audio_config(self):
        """Test that Town of Caledon level has correct audio configuration."""
        # Import the actual level config
        try:
            from src.levels.town_caledon.level_config import level_config
            
            # Verify audio settings
            self.assertEqual(level_config.background_music, "town_theme")
            self.assertIn("birds", level_config.ambient_sounds)
            self.assertIn("wind", level_config.ambient_sounds)
            self.assertEqual(len(level_config.ambient_sounds), 2)
            
        except ImportError:
            self.skipTest("Town Caledon level config not available")
    
    def test_level_config_audio_attributes_exist(self):
        """Test that level config has required audio attributes."""
        try:
            from src.levels.town_caledon.level_config import level_config
            
            # Verify audio attributes exist
            self.assertTrue(hasattr(level_config, 'background_music'))
            self.assertTrue(hasattr(level_config, 'ambient_sounds'))
            
            # Verify types
            self.assertIsInstance(level_config.background_music, (str, type(None)))
            self.assertIsInstance(level_config.ambient_sounds, list)
            
        except ImportError:
            self.skipTest("Town Caledon level config not available")


if __name__ == '__main__':
    unittest.main()
