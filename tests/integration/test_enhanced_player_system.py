"""
Integration tests for the enhanced player asset system.
"""

import pytest
import pygame
from unittest.mock import Mock

from src.application.use_cases import (
    CreateNewPlayerUseCase, EquipItemUseCase, UnequipItemUseCase,
    GetPlayerVisualDataUseCase, BuildLevelUseCase
)
from src.application.interfaces import GameStateData
from src.game_core.config import get_config
from src.infrastructure.events.pygame_event_bus import PygameEventBus
from src.infrastructure.assets.asset_manager import AssetManager
from src.game_core.entities import Position
from src.game_core.config import initialize_config


class TestEnhancedPlayerSystemIntegration:
    """Integration tests for the complete enhanced player system."""
    
    def setup_method(self):
        """Set up test fixtures."""
        pygame.init()
        self.config = initialize_config("game_config.yaml")
        self.event_bus = PygameEventBus()
        self.asset_manager = AssetManager()
    
    def test_complete_player_creation_and_equipment_workflow(self):
        """Test complete workflow from player creation to equipment changes."""
        # 1. Create new player with starting equipment
        create_player_use_case = CreateNewPlayerUseCase(self.event_bus)
        player = create_player_use_case.execute("Integration Test Hero", Position(192, 192))
        
        # Verify starting state
        assert player.head_equipment == "cloth_cap"
        assert player.chest_equipment == "cloth_shirt"
        assert player.main_hand_weapon == "rusty_sword"
        assert player.has_item("bread", 3)
        
        # 2. Create game state
        game_state = GameStateData(
            player=player,
            monsters={},
            items={},
            current_level_id="test",
            collision_map=[[False]],
            level_tiles=[[]]
        )
        
        # 3. Get initial visual data
        visual_use_case = GetPlayerVisualDataUseCase(self.event_bus)
        initial_visual = visual_use_case.execute(game_state)
        
        assert initial_visual["head_equipment"] == "cloth_cap"
        assert initial_visual["main_hand_weapon"] == "rusty_sword"
        
        # 4. Generate initial sprite
        initial_sprite = self.asset_manager.get_equipped_player_asset(
            initial_visual, (self.config.rendering.base_entity_size, self.config.rendering.base_entity_size)
        )
        assert initial_sprite is not None
        assert initial_sprite.get_size() == (128, 128)
        
        # 5. Add new equipment to inventory
        player_with_iron_sword = player.add_item_to_inventory("iron_sword", 1)
        game_state_with_iron_sword = GameStateData(
            player=player_with_iron_sword,
            monsters=game_state.monsters,
            items=game_state.items,
            current_level_id=game_state.current_level_id,
            collision_map=game_state.collision_map,
            level_tiles=game_state.level_tiles
        )
        
        # 6. Equip new weapon
        equip_use_case = EquipItemUseCase(self.event_bus)
        equipped_game_state = equip_use_case.execute(game_state_with_iron_sword, "iron_sword", "main_hand_weapon")
        
        # Verify equipment change
        assert equipped_game_state.player.main_hand_weapon == "iron_sword"
        assert equipped_game_state.player.has_item("rusty_sword", 1)  # Old weapon in inventory
        
        # 7. Get new visual data
        new_visual = visual_use_case.execute(equipped_game_state)
        assert new_visual["main_hand_weapon"] == "iron_sword"
        
        # 8. Generate new sprite
        new_sprite = self.asset_manager.get_equipped_player_asset(
            new_visual, (self.config.rendering.base_entity_size, self.config.rendering.base_entity_size)
        )
        assert new_sprite is not None
        assert new_sprite is not initial_sprite  # Should be different sprite
        
        # 9. Unequip weapon
        unequip_use_case = UnequipItemUseCase(self.event_bus)
        unequipped_game_state = unequip_use_case.execute(equipped_game_state, "main_hand_weapon")
        
        # Verify unequip
        assert unequipped_game_state.player.main_hand_weapon is None
        assert unequipped_game_state.player.has_item("iron_sword", 1)
        
        # 10. Verify visual data reflects unequip
        final_visual = visual_use_case.execute(unequipped_game_state)
        assert final_visual["main_hand_weapon"] is None
    
    def test_multiple_equipment_combinations(self):
        """Test multiple equipment combinations produce unique visuals."""
        create_player_use_case = CreateNewPlayerUseCase(self.event_bus)
        player = create_player_use_case.execute("Test Hero", Position(192, 192))
        
        # Define different equipment combinations
        combinations = [
            {  # Starting equipment
                "head_equipment": "cloth_cap",
                "chest_equipment": "cloth_shirt",
                "legs_equipment": "cloth_pants",
                "boots_equipment": "simple_shoes",
                "main_hand_weapon": "rusty_sword",
                "off_hand_equipment": None
            },
            {  # Light armor
                "head_equipment": "leather_cap",
                "chest_equipment": "leather_armor",
                "legs_equipment": "cloth_pants",
                "boots_equipment": "leather_boots",
                "main_hand_weapon": "iron_sword",
                "off_hand_equipment": "wooden_shield"
            },
            {  # Heavy armor
                "head_equipment": "iron_helmet",
                "chest_equipment": "leather_armor",
                "legs_equipment": "iron_greaves",
                "boots_equipment": "leather_boots",
                "main_hand_weapon": "iron_sword",
                "off_hand_equipment": "iron_shield"
            },
            {  # Archer setup
                "head_equipment": "leather_cap",
                "chest_equipment": "leather_armor",
                "legs_equipment": "cloth_pants",
                "boots_equipment": "simple_shoes",
                "main_hand_weapon": "wooden_bow",
                "off_hand_equipment": None
            }
        ]
        
        sprites = []
        for i, combo in enumerate(combinations):
            sprite = self.asset_manager.get_equipped_player_asset(combo, (128, 128))
            sprites.append(sprite)
            assert sprite is not None
            assert sprite.get_size() == (128, 128)
        
        # All sprites should be unique
        for i in range(len(sprites)):
            for j in range(i + 1, len(sprites)):
                assert sprites[i] is not sprites[j], f"Combination {i} and {j} produced same sprite"
    
    def test_asset_caching_performance(self):
        """Test that asset caching improves performance."""
        equipment = {
            "head_equipment": "cloth_cap",
            "chest_equipment": "cloth_shirt",
            "legs_equipment": "cloth_pants",
            "boots_equipment": "simple_shoes",
            "main_hand_weapon": "rusty_sword",
            "off_hand_equipment": None
        }
        
        # First generation (should cache)
        initial_cache_size = self.asset_manager.get_cache_size()
        sprite1 = self.asset_manager.get_equipped_player_asset(equipment, (128, 128))
        cache_size_after_first = self.asset_manager.get_cache_size()
        
        # Second generation (should use cache)
        sprite2 = self.asset_manager.get_equipped_player_asset(equipment, (128, 128))
        cache_size_after_second = self.asset_manager.get_cache_size()
        
        # Verify caching behavior
        assert cache_size_after_first > initial_cache_size  # Cache grew
        assert cache_size_after_second == cache_size_after_first  # No additional growth
        assert sprite1 is sprite2  # Same object returned
    
    def test_build_level_with_enhanced_player(self):
        """Test that BuildLevelUseCase creates player with starting equipment."""
        from src.application.interfaces import LevelLayoutData

        # Create a simple level definition
        level_data = LevelLayoutData(
            width=3,
            height=3,
            tiles=[
                ["tile.floor.dirt", "tile.floor.dirt", "tile.floor.dirt"],
                ["tile.floor.dirt", "tile.floor.dirt", "tile.floor.dirt"],
                ["tile.floor.dirt", "tile.floor.dirt", "tile.floor.dirt"]
            ],
            collision_map=[
                [False, False, False],
                [False, False, False],
                [False, False, False]
            ],
            entities=[
                {
                    "type": "player",
                    "x": 1,
                    "y": 1,
                    "asset_id": "player.hero"
                }
            ]
        )

        build_level_use_case = BuildLevelUseCase(self.event_bus)
        game_state = build_level_use_case.execute(level_data, "test_level")
        
        # Verify player was created with starting equipment
        assert game_state.player is not None
        assert game_state.player.head_equipment == "cloth_cap"
        assert game_state.player.chest_equipment == "cloth_shirt"
        assert game_state.player.legs_equipment == "cloth_pants"
        assert game_state.player.boots_equipment == "simple_shoes"
        assert game_state.player.main_hand_weapon == "rusty_sword"
        assert game_state.player.off_hand_equipment is None
        
        # Verify starting inventory
        assert game_state.player.has_item("bread", 3)
        assert game_state.player.has_item("health_potion", 2)
        assert game_state.player.has_item("gold_coin", 25)
    
    def test_configuration_integration(self):
        """Test that configuration values are properly used."""
        # Verify base_entity_size configuration
        assert self.config.rendering.base_entity_size == 128
        
        # Test sprite generation with config size
        equipment = {
            "head_equipment": "cloth_cap",
            "chest_equipment": "cloth_shirt",
            "legs_equipment": None,
            "boots_equipment": None,
            "main_hand_weapon": "rusty_sword",
            "off_hand_equipment": None
        }
        
        sprite = self.asset_manager.get_equipped_player_asset(
            equipment, 
            (self.config.rendering.base_entity_size, self.config.rendering.base_entity_size)
        )
        
        assert sprite.get_size() == (128, 128)
    
    def test_equipment_visual_distinctiveness(self):
        """Test that different equipment pieces create visually distinct sprites."""
        base_equipment = {
            "head_equipment": None,
            "chest_equipment": None,
            "legs_equipment": None,
            "boots_equipment": None,
            "main_hand_weapon": None,
            "off_hand_equipment": None
        }
        
        # Test each equipment slot individually
        equipment_variations = [
            {**base_equipment, "head_equipment": "cloth_cap"},
            {**base_equipment, "head_equipment": "leather_cap"},
            {**base_equipment, "head_equipment": "iron_helmet"},
            {**base_equipment, "chest_equipment": "cloth_shirt"},
            {**base_equipment, "chest_equipment": "leather_armor"},
            {**base_equipment, "main_hand_weapon": "rusty_sword"},
            {**base_equipment, "main_hand_weapon": "iron_sword"},
            {**base_equipment, "main_hand_weapon": "wooden_bow"},
            {**base_equipment, "off_hand_equipment": "wooden_shield"},
            {**base_equipment, "off_hand_equipment": "iron_shield"},
        ]
        
        sprites = []
        for equipment in equipment_variations:
            sprite = self.asset_manager.get_equipped_player_asset(equipment, (128, 128))
            sprites.append(sprite)
        
        # Convert sprites to comparable data
        sprite_data = []
        for sprite in sprites:
            data = pygame.image.tostring(sprite, 'RGBA')
            sprite_data.append(data)
        
        # Verify all sprites are visually different
        for i in range(len(sprite_data)):
            for j in range(i + 1, len(sprite_data)):
                assert sprite_data[i] != sprite_data[j], f"Equipment variation {i} and {j} produced identical sprites"
    
    def test_inventory_space_limits(self):
        """Test that inventory space limits are enforced."""
        config = get_config()
        max_slots = config.inventory_ui.inventory_max_slots

        create_player_use_case = CreateNewPlayerUseCase(self.event_bus)
        player = create_player_use_case.execute("Test Hero", Position(192, 192))

        # Player starts with 3 items in inventory (bread, health_potion, gold_coin)
        # Equipment items are equipped, not in inventory
        starting_items = 3
        assert player.get_inventory_space_used() == starting_items
        assert player.get_inventory_space_remaining() == max_slots - starting_items

        # Fill remaining inventory space
        current_player = player
        remaining_slots = max_slots - starting_items
        for i in range(remaining_slots):
            current_player = current_player.add_item_to_inventory(f"test_item_{i}", 1)

        # Inventory should now be full
        assert current_player.get_inventory_space_used() == max_slots
        assert current_player.get_inventory_space_remaining() == 0
        
        # Adding another unique item should fail
        with pytest.raises(ValueError, match="Inventory is full"):
            current_player.add_item_to_inventory("overflow_item", 1)
        
        # But adding to existing items should still work
        updated_player = current_player.add_item_to_inventory("bread", 5)
        assert updated_player.inventory["bread"] == 8  # 3 + 5
