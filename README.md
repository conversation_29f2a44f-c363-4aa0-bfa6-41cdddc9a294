# <PERSON><PERSON><PERSON>'s Revenge - Clean Architecture RPG

A ground-up rebuild of an old-school Nintendo-style RPG using **Clean Architecture** principles. This project demonstrates how to create a maintainable, testable, and extensible game codebase that will support growth for years to come.

## 🏗️ Architecture Overview

This project implements **Clean Architecture** with strict dependency rules:

```
┌─────────────────────────────────────────────────────────────┐
│                    PRESENTATION LAYER                       │
│  ┌─────────────────────────────────────────────────────┐   │
│  │              APPLICATION LAYER                      │   │
│  │  ┌─────────────────────────────────────────────┐   │   │
│  │  │              CORE LAYER                     │   │   │
│  │  │         (Business Logic)                    │   │   │
│  │  └─────────────────────────────────────────────┘   │   │
│  └─────────────────────────────────────────────────────┐   │
│                                                        │   │
│                 INFRASTRUCTURE LAYER                   │   │
└─────────────────────────────────────────────────────────────┘
```

### **The Golden Rule**: Dependencies only point inward!

- **Core** (`src/game_core/`) - Pure game logic, no external dependencies
- **Application** (`src/application/`) - Use cases and interfaces
- **Infrastructure** (`src/infrastructure/`) - Pygame, file I/O, concrete implementations  
- **Presentation** (`src/presentation/`) - Game loop, input handling, UI

## 🚀 Quick Start

### Setup
```bash
# Setup development environment
./run.sh setup

# Run tests to verify installation
./run.sh test

# Start the game
./run.sh run
```

### Development Commands
```bash
./run.sh setup          # Create venv and install dependencies
./run.sh test            # Run all tests with coverage  
./run.sh test-unit       # Run only unit tests (core + application)
./run.sh test-integration # Run integration tests
./run.sh typecheck       # Run mypy type checking
./run.sh run             # Start the game
./run.sh clean           # Remove generated files
```

## 🎮 Game Controls

- **WASD** - Move player (including diagonals)
- **Mouse Click** - Attack in the direction of cursor
- **R** - Reload current level
- **ESC** - Quit game

## 🏛️ Architecture Deep Dive

### Core Layer (`src/game_core/`)
**NO EXTERNAL DEPENDENCIES ALLOWED**
- `entities.py` - Pure data classes (Player, Monster, Item, etc.)
- `events.py` - Immutable event definitions
- `game_logic.py` - Pure functions for game mechanics

### Application Layer (`src/application/`)
- `interfaces.py` - Abstract base classes for outer layers
- `use_cases.py` - Business logic orchestration (MovePlayerUseCase, etc.)

### Infrastructure Layer (`src/infrastructure/`)
**The only layer that imports Pygame**
- `assets/` - Procedural asset generation and management
- `audio/` - Audio system with procedural sound generation
- `events/` - Event bus implementation  
- `repositories/` - Level loading and map parsing
- `rendering/` - Pygame rendering system

### Presentation Layer (`src/presentation/`)
- `game_engine.py` - Main game loop and input handling

## 🎨 Asset System

All visual assets are **procedurally generated** using pure code:

```python
# Game data defines what exists
MONSTERS = {
    "green_drake": MonsterDefinition(
        name="Green Drake",
        asset_id="monster.drake.green.side",  # Links to generation function
        max_hp=80,
        strength=12
    )
}

# Infrastructure generates the visuals
def get_green_drake_sprite(size=(64, 64)):
    # Detailed procedural drawing code...
    return pygame_surface
```

Benefits:
- No external art assets required
- Consistent visual style
- Easy to modify and extend
- Version control friendly

## 🗺️ Level System

Levels are self-contained packages with human-readable `.map` files:

```
src/levels/town_caledon/
├── caledon_main.map     # Visual map layout
├── level_config.py      # Metadata and settings
├── events.py           # Level-specific quest logic
└── __init__.py         # Python package
```

### Map File Format
```yaml
# YAML header with overrides
legend:
  'T':
    category: tile
    type: tree
    solid: true
    asset_id: "tile.tree.oak"

placements:
  - char: 'g'
    position: { x: 15, y: 8 }
    overrides:
      name: "Goblin Boss"
      max_hp: 100

---
# ASCII visual layout
######################
#..................#
#..@.......g.......#
#..................#
######################
```

## 🔄 Event-Driven Architecture

Systems communicate through events, not direct method calls:

```python
# Instead of: player.attack(monster) -> monster.take_damage() -> ui.show_damage()

# We use events:
combat_system.execute_attack()  # Calculates damage
event_bus.publish(EntityDamagedEvent(target="goblin1", damage=10))

# Multiple systems react independently:
health_system.handle_damage(event)  # Updates HP
ui_system.show_damage_number(event)  # Shows "10" on screen  
audio_system.play_hit_sound(event)   # Plays sound
```

## 🧪 Testing Philosophy

**Test-Driven Development** with layer-appropriate testing:

```bash
# Unit tests (fast, no I/O)
./run.sh test-unit

# Integration tests (slower, tests layer interactions)  
./run.sh test-integration
```

- **Core Layer**: 100% unit tested, no mocks needed
- **Application Layer**: Use case testing with mocked infrastructure
- **Infrastructure Layer**: Integration tests with real Pygame
- **80%+ test coverage** target

## 📁 Project Structure

```
treebeards_revenge/
├── src/
│   ├── game_core/          # 🏛️ Pure game logic (NO external deps)
│   ├── application/        # 🎯 Use cases and interfaces  
│   ├── infrastructure/     # 🔧 Pygame, I/O, concrete implementations
│   ├── presentation/       # 🎮 Game loop and input
│   ├── game_data/         # 📊 Shared data definitions
│   └── levels/            # 🗺️ Self-contained level packages
├── tests/
│   ├── unit/              # Fast, isolated tests
│   └── integration/       # Cross-layer tests
├── run.sh                 # Development commands
└── pyproject.toml         # Dependencies and configuration
```

## 🎯 Design Principles

1. **Dependency Inversion** - Core depends on nothing, outer layers implement interfaces
2. **Single Responsibility** - Each class has one reason to change
3. **Open/Closed Principle** - Extend through new implementations, not modifications
4. **Event-Driven** - Loose coupling through pub/sub messaging
5. **Immutable Data** - Game state changes through pure functions
6. **Type Safety** - Full type hints, passes `mypy --strict`

## 🔧 Adding New Features

### New Monster Type
1. Add definition to `src/game_data/monsters.py`
2. Create sprite function in `src/infrastructure/assets/procedural_monsters.py`  
3. Register in `src/infrastructure/assets/registry.py`
4. Use in map files with the `data_id`

### New Game Mechanic  
1. Add core logic to `src/game_core/game_logic.py`
2. Create use case in `src/application/use_cases.py`
3. Wire up in presentation layer
4. Write tests first! (TDD)

### New Level
1. Create directory in `src/levels/`
2. Add `.map` file with layout
3. Add `level_config.py` and `events.py`
4. Level is automatically discoverable

## 🎪 Why This Architecture?

The original codebase became unmaintainable due to:
- ❌ Monolithic design
- ❌ Tight coupling between systems  
- ❌ Business logic mixed with UI code
- ❌ Difficult to test
- ❌ Hard to extend

This rebuild provides:
- ✅ **Maintainable** - Clear separation of concerns
- ✅ **Testable** - Core logic has no external dependencies
- ✅ **Extensible** - Add features without breaking existing code
- ✅ **Portable** - Could switch from Pygame to Godot by changing infrastructure
- ✅ **Understandable** - New developers can join easily

## 🎓 Learning Resources

This project demonstrates:
- Clean Architecture (Uncle Bob)
- Domain-Driven Design patterns
- Event-driven architecture
- Test-driven development
- Modern Python practices (type hints, Pydantic, etc.)

## 🤝 Contributing

1. **Follow the architecture** - Respect the dependency rules
2. **Write tests first** - TDD is mandatory
3. **Type everything** - Code must pass `mypy --strict`
4. **Document your changes** - Update README for new features

---

**Ready to explore clean, maintainable game development? Let's build something amazing! 🎮**
