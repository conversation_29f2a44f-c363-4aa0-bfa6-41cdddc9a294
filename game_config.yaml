# Tree<PERSON><PERSON>'s Revenge - Game Configuration
#
# This file allows you to customize game settings without modifying code.
# Changes to this file require restarting the game to take effect.

# Game Settings
game:
  # Starting map when creating a new game
  starting_map: "town_west_haven"  # New starter town with Mayor and sequential dialog

# Rendering and Display Settings
rendering:
  tile_size: 128              # Size of each tile in pixels (32=small, 64=medium, 96=large)
  base_entity_size: 128       # Default size for entity sprites in pixels
  screen_width: 3060         # Window width in pixels
  screen_height: 1280         # Window height in pixels
  target_fps: 60             # Target frames per second
  camera_lerp_factor: 0.15   # Camera smoothness (0.0=instant, 1.0=very smooth)
  camera_boundary_margin: 2  # Tiles of margin before camera boundaries

# UI Scaling Configuration
ui_scaling:
  # Global UI scale factor (1.0 = normal, 1.5 = 50% larger, 0.8 = 20% smaller)
  scale_factor: 2.0

  # Standard font sizes (before scaling)
  font_sizes:
    tiny: 12
    small: 16
    normal: 20
    medium: 24
    large: 28
    xlarge: 32
    xxlarge: 36

  # Standard UI element sizes (before scaling)
  element_sizes:
    button_height: 30
    input_height: 25
    menu_height: 30
    toolbar_button: 40
    status_height: 25
    header_height: 60
    panel_width: 250
    asset_grid_size: 48

  # Standard spacing (before scaling)
  spacing:
    tiny: 2
    small: 4
    normal: 8
    medium: 12
    large: 16
    xlarge: 20

# Inventory UI Configuration
inventory_ui:
  # Panel layout (percentages of screen width/height)
  character_panel_width_percent: 30    # Character panel width as % of screen
  inventory_panel_width_percent: 45    # Inventory panel width as % of screen
  stats_panel_width_percent: 25       # Stats panel width as % of screen
  panel_height_percent: 90            # All panels height as % of screen
  panel_margin: 40                    # Margin around panels in pixels
  panel_spacing: 20                   # Spacing between panels in pixels

  # Inventory grid settings
  inventory_max_slots: 10             # Maximum inventory slots (2x5 grid)
  inventory_slot_size: 160            # Size of each inventory slot in pixels
  inventory_slot_padding: 20          # Padding between inventory slots

  # Equipment slot settings
  equipment_slot_size: 120            # Size of equipment slots in pixels

  # Typography - now uses centralized ui_scaling configuration

  # Colors (RGBA values 0-255)
  background_color: [139, 69, 19, 200]     # Semi-transparent brown
  border_color: [101, 67, 33, 255]         # Dark brown border
  text_color: [255, 248, 220, 255]         # Cornsilk text
  highlight_color: [255, 215, 0, 100]      # Gold highlight (semi-transparent)

# Settings UI Configuration
settings_ui:
  # Show settings on startup
  show_on_startup: true

  # Default save slot for new games
  default_save_slot: "slot_1"

  # Settings menu layout
  menu_width_percent: 25          # Percentage of screen width
  menu_height_percent: 60         # Percentage of screen height
  button_spacing: 15              # Spacing between buttons

  # Save slot grid layout
  slots_per_row: 2                # Number of save slots per row
  slot_grid_spacing: 20           # Spacing between save slot buttons

# Movement and Controls
movement:
  player_move_cooldown: 0.15    # Seconds between player moves (0.1=fast, 0.2=slow)
  player_move_speed: 6.67       # Tiles per second (multiplied by tile_size to get pixels/sec)
  monster_move_cooldown: 0.5    # Monster movement speed
  npc_move_cooldown: 1.0        # NPC movement speed

# Wander System Configuration
wander:
  # Default wander settings for NPCs
  npc_wander_radius: 4.0        # Maximum distance NPCs can wander from spawn (in tiles)
  npc_move_speed_multiplier: 0.7 # Speed multiplier for wandering NPCs (relative to player)
  npc_idle_time_min: 3.0        # Minimum time NPCs stay idle (seconds)
  npc_idle_time_max: 10.0       # Maximum time NPCs stay idle (seconds)
  npc_move_time_min: 2.0        # Minimum time NPCs move in one direction (seconds)
  npc_move_time_max: 6.0        # Maximum time NPCs move in one direction (seconds)

  # Default wander settings for monsters (when implemented)
  monster_wander_radius: 6.0    # Maximum distance monsters can wander from spawn (in tiles)
  monster_move_speed_multiplier: 0.8 # Speed multiplier for wandering monsters
  monster_idle_time_min: 1.0    # Minimum time monsters stay idle (seconds)
  monster_idle_time_max: 5.0    # Maximum time monsters stay idle (seconds)
  monster_move_time_min: 1.5    # Minimum time monsters move in one direction (seconds)
  monster_move_time_max: 4.0    # Maximum time monsters move in one direction (seconds)

# Combat and Gameplay Balance
combat:
  base_damage_variance: 0.2        # ±20% damage variance
  critical_hit_chance: 0.05        # 5% base critical hit chance
  critical_hit_multiplier: 2.0     # 2x damage on critical hits
  speed_hit_modifier: 0.02         # 2% hit chance per speed point difference
  base_experience_per_level: 100   # Base experience required per level
  experience_scaling_factor: 1.5   # Experience scaling (each level needs 50% more)

# Animation Settings
animations:
  attack_base_duration: 300  # Base attack animation duration in milliseconds
  melee_swing_angle: 15      # Maximum swing angle for player body with melee weapons in degrees
  ranged_vibration_amplitude: 2  # Vibration amplitude for player body with ranged weapons in pixels
  ranged_vibration_frequency: 20 # Vibration frequency for player body with ranged weapons in Hz

  # Weapon-specific animation settings
  weapon_swing_angle: 45     # Maximum swing angle for weapon sprites in degrees
  weapon_vibration_amplitude: 1  # Vibration amplitude for weapon sprites in pixels

# Audio Settings
audio:
  master_volume: 0.8    # Master volume (0.0 = mute, 1.0 = full)
  sfx_volume: 0.7       # Sound effects volume
  music_volume: 0.6     # Background music volume
  ui_volume: 0.5        # UI sound volume
  sample_rate: 22050    # Audio sample rate
  audio_bit_depth: 16   # Audio bit depth

# Debug and Development
debug_mode: false              # Enable debug features
show_collision_boxes: false    # Show collision boundaries
show_interaction_areas: false   # Show interaction area rectangles for player and NPCs
show_fps: false               # Display FPS counter
verbose_logging: false        # Enable detailed logging
show_weapon_direction: false   # Show weapon attack direction arrow
show_attack_arc: false         # Show weapon attack arc visualization
show_entity_hitboxes: false    # Show entity collision/hit detection boxes
