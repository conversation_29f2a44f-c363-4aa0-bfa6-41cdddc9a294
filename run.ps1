# <PERSON>be<PERSON>'s Revenge - Development Runner Script (PowerShell)
#
# If you get an execution policy error, run one of these commands in PowerShell as Administrator:
# Option 1 (Recommended): Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
# Option 2 (Less secure): Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope CurrentUser
# 
# Or run this script with: PowerShell -ExecutionPolicy Bypass -File .\run.ps1 <command>

param(
    [Parameter(Position=0)]
    [string]$Command = "help"
)

# Set error action preference to stop on any error
$ErrorActionPreference = "Stop"

# Get the script directory and change to it
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $ScriptDir

# Function to ensure virtual environment exists and is up to date
function Ensure-Venv {
    Write-Host "Checking virtual environment..." -ForegroundColor Cyan
    
    # Check if uv is installed
    try {
        uv --version | Out-Null
    }
    catch {
        Write-Host "Installing uv..." -ForegroundColor Yellow
        # Install uv using the PowerShell installer
        Invoke-RestMethod https://astral.sh/uv/install.ps1 | Invoke-Expression
        
        # Refresh the PATH for the current session
        $env:PATH = [System.Environment]::GetEnvironmentVariable("PATH", "User") + ";" + [System.Environment]::GetEnvironmentVariable("PATH", "Machine")
    }
    
    # Check if virtual environment exists
    if (-not (Test-Path ".venv")) {
        Write-Host "Creating virtual environment..." -ForegroundColor Yellow
        uv venv
        if ($LASTEXITCODE -ne 0) {
            throw "Failed to create virtual environment"
        }
    }
    
    # Check if dependencies need to be installed/updated
    $venvConfigFile = ".venv\pyvenv.cfg"
    $needsSync = $false
    
    if (-not (Test-Path $venvConfigFile)) {
        $needsSync = $true
    }
    elseif ((Get-Item "pyproject.toml").LastWriteTime -gt (Get-Item $venvConfigFile).LastWriteTime) {
        $needsSync = $true
    }
    elseif ((Test-Path "uv.lock") -and ((Get-Item "uv.lock").LastWriteTime -gt (Get-Item $venvConfigFile).LastWriteTime)) {
        $needsSync = $true
    }
    
    if ($needsSync) {
        Write-Host "Installing/updating dependencies..." -ForegroundColor Yellow
        uv sync
        if ($LASTEXITCODE -ne 0) {
            throw "Failed to sync dependencies"
        }
    }
    
    Write-Host "Virtual environment ready!" -ForegroundColor Green
}

# Function to run uv commands with proper error handling
function Invoke-UvCommand {
    param([string]$Arguments)
    
    Write-Host "Running: uv $Arguments" -ForegroundColor Gray
    Start-Process -FilePath "uv" -ArgumentList $Arguments.Split(' ') -NoNewWindow -Wait
    if ($LASTEXITCODE -ne 0) {
        throw "Command failed: uv $Arguments"
    }
}

# Main command switch
switch ($Command.ToLower()) {
    "setup" {
        Write-Host "Setting up development environment..." -ForegroundColor Cyan
        Ensure-Venv
        Write-Host "Setup complete! Run '.\run.ps1 test' to verify installation." -ForegroundColor Green
    }
    
    "test" {
        Write-Host "Running test suite..." -ForegroundColor Cyan
        Ensure-Venv
        Invoke-UvCommand "run pytest"
    }
    
    "test-unit" {
        Write-Host "Running unit tests (core and application layers only)..." -ForegroundColor Cyan
        Ensure-Venv
        Invoke-UvCommand "run pytest tests/unit/"
    }
    
    "test-integration" {
        Write-Host "Running integration tests..." -ForegroundColor Cyan
        Ensure-Venv
        Invoke-UvCommand "run pytest tests/integration/"
    }
    
    "typecheck" {
        Write-Host "Running type checks..." -ForegroundColor Cyan
        Ensure-Venv
        Invoke-UvCommand "run mypy src/"
    }
    
    "run" {
        Write-Host "Starting the game..." -ForegroundColor Cyan
        Ensure-Venv
        Push-Location "src"
        try {
            Invoke-UvCommand "run python main.py"
        }
        finally {
            Pop-Location
        }
    }
    
    "editor" {
        Write-Host "Starting map editor..." -ForegroundColor Cyan
        Ensure-Venv
        Push-Location "src"
        try {
            Invoke-UvCommand "run python -m editor.main"
        }
        finally {
            Pop-Location
        }
    }
    
    "clean" {
        Write-Host "Cleaning up..." -ForegroundColor Cyan
        
        # Remove directories if they exist
        $dirsToRemove = @(".venv", ".pytest_cache", "htmlcov")
        foreach ($dir in $dirsToRemove) {
            if (Test-Path $dir) {
                Write-Host "Removing $dir..." -ForegroundColor Yellow
                Remove-Item $dir -Recurse -Force
            }
        }
        
        # Remove __pycache__ directories
        Write-Host "Removing __pycache__ directories..." -ForegroundColor Yellow
        Get-ChildItem -Path . -Name "__pycache__" -Recurse -Directory | ForEach-Object {
            $fullPath = Join-Path $PWD $_
            Write-Host "  Removing $fullPath" -ForegroundColor Gray
            Remove-Item $fullPath -Recurse -Force
        }
        
        Write-Host "Cleanup complete!" -ForegroundColor Green
    }
    
    default {
        Write-Host "Treebeard's Revenge - Development Commands" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "Usage: .\run.ps1 <command>" -ForegroundColor White
        Write-Host ""
        Write-Host "Commands:" -ForegroundColor White
        Write-Host "  setup          Create virtual environment and install dependencies" -ForegroundColor Gray
        Write-Host "  test           Run all tests with coverage" -ForegroundColor Gray
        Write-Host "  test-unit      Run only unit tests (core + application layers)" -ForegroundColor Gray
        Write-Host "  test-integration Run only integration tests" -ForegroundColor Gray
        Write-Host "  typecheck      Run mypy type checking" -ForegroundColor Gray
        Write-Host "  run            Start the game" -ForegroundColor Gray
        Write-Host "  editor         Start the map editor" -ForegroundColor Gray
        Write-Host "  clean          Remove generated files and virtual environment" -ForegroundColor Gray
        Write-Host "  help           Show this help message" -ForegroundColor Gray
        Write-Host ""
        Write-Host "Examples:" -ForegroundColor White
        Write-Host "  .\run.ps1 setup" -ForegroundColor Yellow
        Write-Host "  .\run.ps1 run" -ForegroundColor Yellow
        Write-Host "  .\run.ps1 test" -ForegroundColor Yellow
    }
}
