#!/usr/bin/env python3
"""
Script to generate the Green Acres map with all requested features:
- 50x50 size
- Mountain ranges with hills as borders
- Forking river system
- Central lake with island
- Forest area with spiders
- Portal to town_west_haven
- Grassy meadows
"""

import random
from typing import List, Tuple, Set

# Map dimensions
MAP_WIDTH = 50
MAP_HEIGHT = 50

# Terrain symbols
MOUNTAIN = '^'
HILL = 'h'
GRASS = ','
WATER = '~'
TREE = 'T'
SPIDER = 's'
PORTAL = '0'

class MapGenerator:
    def __init__(self):
        # Initialize empty map with grass
        self.map_grid = [[GRASS for _ in range(MAP_WIDTH)] for _ in range(MAP_HEIGHT)]
        
    def generate_map(self) -> List[List[str]]:
        """Generate the complete Green Acres map."""
        # 1. Create mountain border
        self._create_mountain_border()
        
        # 2. Add hills inside mountains
        self._create_hills()
        
        # 3. Create forking river system
        self._create_river_system()
        
        # 4. Create central lake with island
        self._create_central_lake()
        
        # 5. Create forest area
        self._create_forest()
        
        # 6. Add spiders to forest
        self._add_spiders()
        
        # 7. Place portal
        self._place_portal()
        
        return self.map_grid
    
    def _create_mountain_border(self):
        """Create mountain ranges around the border."""
        # Top and bottom borders
        for x in range(MAP_WIDTH):
            self.map_grid[0][x] = MOUNTAIN
            self.map_grid[MAP_HEIGHT-1][x] = MOUNTAIN
        
        # Left and right borders
        for y in range(MAP_HEIGHT):
            self.map_grid[y][0] = MOUNTAIN
            self.map_grid[y][MAP_WIDTH-1] = MOUNTAIN
    
    def _create_hills(self):
        """Add hills inside the mountain border."""
        # Hills along the inner edge of mountains
        for x in range(1, MAP_WIDTH-1):
            self.map_grid[1][x] = HILL
            self.map_grid[MAP_HEIGHT-2][x] = HILL
        
        for y in range(1, MAP_HEIGHT-1):
            self.map_grid[y][1] = HILL
            self.map_grid[y][MAP_WIDTH-2] = HILL
        
        # Add some scattered hills for variety
        hill_positions = [
            (8, 8), (12, 6), (15, 10), (20, 8), (25, 12),
            (35, 15), (40, 20), (38, 35), (30, 40), (20, 42),
            (10, 38), (6, 30), (8, 25), (15, 35), (25, 30)
        ]
        
        for x, y in hill_positions:
            if 2 <= x < MAP_WIDTH-2 and 2 <= y < MAP_HEIGHT-2:
                self.map_grid[y][x] = HILL
    
    def _create_river_system(self):
        """Create a forking river system."""
        # Main river starts from top-left area and flows southeast
        river_points = []
        
        # Main branch: starts at (10, 5) and flows to (25, 25)
        main_branch = self._create_river_path((10, 5), (25, 25))
        river_points.extend(main_branch)
        
        # Fork 1: from (20, 20) flows to (35, 30)
        fork1 = self._create_river_path((20, 20), (35, 30))
        river_points.extend(fork1)
        
        # Fork 2: from (22, 22) flows to (15, 40)
        fork2 = self._create_river_path((22, 22), (15, 40))
        river_points.extend(fork2)
        
        # Place water tiles
        for x, y in river_points:
            if 2 <= x < MAP_WIDTH-2 and 2 <= y < MAP_HEIGHT-2:
                self.map_grid[y][x] = WATER
    
    def _create_river_path(self, start: Tuple[int, int], end: Tuple[int, int]) -> List[Tuple[int, int]]:
        """Create a winding river path between two points."""
        path = []
        x1, y1 = start
        x2, y2 = end
        
        current_x, current_y = x1, y1
        
        while abs(current_x - x2) > 1 or abs(current_y - y2) > 1:
            path.append((current_x, current_y))
            
            # Move towards target with some randomness for natural curves
            if current_x < x2:
                current_x += 1
            elif current_x > x2:
                current_x -= 1
            
            if current_y < y2:
                current_y += 1
            elif current_y > y2:
                current_y -= 1
            
            # Add some random curves
            if random.random() < 0.3:
                current_x += random.choice([-1, 0, 1])
                current_y += random.choice([-1, 0, 1])
        
        path.append((x2, y2))
        return path
    
    def _create_central_lake(self):
        """Create a central lake with a small island."""
        # Lake center
        lake_center_x, lake_center_y = 30, 25
        lake_radius = 6
        
        # Create circular lake
        for y in range(max(2, lake_center_y - lake_radius), 
                      min(MAP_HEIGHT-2, lake_center_y + lake_radius + 1)):
            for x in range(max(2, lake_center_x - lake_radius), 
                          min(MAP_WIDTH-2, lake_center_x + lake_radius + 1)):
                distance = ((x - lake_center_x) ** 2 + (y - lake_center_y) ** 2) ** 0.5
                if distance <= lake_radius:
                    self.map_grid[y][x] = WATER
        
        # Create small island in the center
        island_positions = [
            (lake_center_x, lake_center_y),
            (lake_center_x-1, lake_center_y),
            (lake_center_x+1, lake_center_y),
            (lake_center_x, lake_center_y-1),
            (lake_center_x, lake_center_y+1)
        ]
        
        for x, y in island_positions:
            if 2 <= x < MAP_WIDTH-2 and 2 <= y < MAP_HEIGHT-2:
                self.map_grid[y][x] = GRASS
    
    def _create_forest(self):
        """Create a forest area in the bottom-left corner."""
        # Forest area roughly from (5, 30) to (20, 45)
        forest_area = []
        
        for y in range(30, 46):
            for x in range(5, 21):
                if 2 <= x < MAP_WIDTH-2 and 2 <= y < MAP_HEIGHT-2:
                    # Don't overwrite water or mountains
                    if self.map_grid[y][x] == GRASS:
                        # Random tree placement for natural look
                        if random.random() < 0.7:  # 70% chance of tree
                            self.map_grid[y][x] = TREE
                            forest_area.append((x, y))
        
        return forest_area
    
    def _add_spiders(self):
        """Add spiders to the forest area."""
        spider_positions = [
            (8, 35), (12, 38), (15, 33), (18, 40), (10, 42)
        ]
        
        for x, y in spider_positions:
            if 2 <= x < MAP_WIDTH-2 and 2 <= y < MAP_HEIGHT-2:
                # Only place spiders in forest areas or grass
                if self.map_grid[y][x] in [TREE, GRASS]:
                    self.map_grid[y][x] = SPIDER
    
    def _place_portal(self):
        """Place the portal to town_west_haven."""
        # Portal in top-left area as specified
        portal_x, portal_y = 2, 2
        self.map_grid[portal_y][portal_x] = PORTAL
    
    def export_to_map_file(self, filename: str):
        """Export the generated map to a .map file format."""
        header = '''metadata:
  name: Green Acres
  description: A vast meadow with mountain ranges, a forking river, and a central lake with an island

legend:
  '0':
    category: tile
    type: exit
    solid: false
    asset_id: "tile.exit.town_west_haven"
    properties:
      exit_number: 0

---
'''
        
        with open(filename, 'w') as f:
            f.write(header)
            
            for row in self.map_grid:
                f.write(''.join(row) + '\n')

def main():
    """Generate and save the Green Acres map."""
    print("Generating Green Acres map...")
    
    # Set random seed for reproducible results
    random.seed(42)
    
    generator = MapGenerator()
    map_grid = generator.generate_map()
    
    # Export to map file
    output_file = "src/levels/green_acres/green_acres.map"
    generator.export_to_map_file(output_file)
    
    print(f"Map generated and saved to {output_file}")
    print("Map features:")
    print("- 50x50 size with mountain borders")
    print("- Hills scattered throughout")
    print("- Forking river system")
    print("- Central lake with island")
    print("- Forest area with spiders")
    print("- Portal to town_west_haven at (2,2)")

if __name__ == "__main__":
    main()
