I think we need a new global quest system since quests can have steps cross level. The levels themselves will need to interact, and have custom behaviours depending on quest state, and they may need to update on state change (This can be done on level load , but if the state changes after a level is loaded, then a listener might be required.

Write me a prompt to give an ai that does the above, then adds our first quest (goblin_loose_in_caledon). There is a guard 'G' at x=3,y=7. When the player talks to this gaurd, and the quest hasn't started yet, the gaurd will explain to the player that there is a goblin wandering the streets and needs to be dealt with, and to come back for a reward. If the player accepts the quest, the quest starts, and the gaurd dialog updates appropriately. When the player kills the golbin, the quest requirements are met. The player can go back to the gaurd who's dialog will update appropriately, who will give the player 10G.