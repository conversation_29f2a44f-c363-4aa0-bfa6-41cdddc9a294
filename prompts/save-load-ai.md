# Save/Load System Implementation Prompt

## Overview
Implement a comprehensive save/load system for <PERSON><PERSON><PERSON>'s Revenge, a 2D RPG built with Pygame using Clean Architecture patterns. The system should include a settings UI, 10 save slots, context-aware escape key handling, and a help panel.

## Current Game Architecture

### Key Components
- **Game Engine**: `src/presentation/game_engine.py` - Main game loop and event handling
- **Save Repository**: `src/infrastructure/repositories/json_save_game_repository.py` - Already exists with save/load functionality
- **UI System**: Modern blue-grey themed UI components in `src/presentation/ui/`
- **Configuration**: `src/game_core/config.py` with centralized UI scaling via `ui_scaling` config
- **Event System**: `src/infrastructure/events/pygame_event_bus.py` for decoupled communication

### Current UI Components (Templates)
- **InventoryUIController**: Full-featured UI with modern styling, drag/drop, tooltips
- **StoreUIController**: Store interface with item selection, buy/sell modes
- **DialogUIController**: NPC dialog system with fade animations

### Current Input Handling
- Escape key currently exits game (or fullscreen)
- 'I' key toggles inventory
- F5/F9 for quick save/load (already implemented)
- UI events are handled in priority order (store > dialog > inventory > game)

## Requirements

### 1. Settings UI (Main Menu)
Create a new `SettingsUIController` in `src/presentation/ui/settings_ui.py`:

**Features:**
- **New Game**: Start fresh game (load "town_caledon" level)
- **Load Game**: Show save slot selection with metadata (timestamp, level, player stats)
- **Save Game**: Save to current slot (no overwrite confirmation needed)
- **Help**: Show controls/keybindings panel
- **Exit Game**: Quit application

**Visual Design:**
- Use existing modern blue-grey theme from other UIs
- Large, clearly readable buttons using `ui_scaling` configuration
- Center-aligned layout with proper spacing
- Show current save slot info when in-game

### 2. Save Slot System
Extend existing `JsonSaveGameRepository`:

**Save Slots:**
- 10 numbered slots (1-10)
- Metadata: timestamp, level name, player level/stats, screenshot thumbnail (optional)
- Current slot tracking in game state
- Auto-save to current slot functionality

**Save Slot UI:**
- Grid layout showing all 10 slots
- Empty slots show "Empty" placeholder
- Occupied slots show: save date/time, level name, player info
- Click to select slot for loading
- Visual indication of current active slot

### 3. Context-Aware Escape Key
Modify `GameEngine._handle_events()`:

**Behavior:**
- **In Settings UI**: Close settings, return to game (if game active) or exit (if no game)
- **In any other UI**: Close that UI, return to game
- **In game**: Pause game and show settings UI
- **Priority order**: Settings > Store > Dialog > Inventory > Game

### 4. Help Panel
Create help overlay showing:
- **Movement**: WASD keys
- **Inventory**: I key
- **Interact**: E key  
- **Quick Save/Load**: F5/F9
- **Pause/Settings**: Escape key
- **Fullscreen**: F11
- **UI Navigation**: Mouse click, Escape to close

### 5. Game Startup Flow
Modify `GameEngine.run()`:
- Show settings UI immediately on startup (before loading any level)
- Only load initial level after "New Game" or "Load Game" is selected
- Track game state: MENU, PLAYING, PAUSED

## Implementation Guidelines

### Code Organization
```
src/presentation/ui/
├── settings_ui.py          # Main settings/menu controller
├── save_slot_ui.py         # Save slot selection component  
├── help_ui.py              # Help panel component
└── __init__.py             # Update imports
```

### Integration Points
1. **GameEngine**: Add settings UI controller, modify event handling and startup
2. **Config**: Add settings UI configuration to `game_config.yaml`
3. **Save Repository**: Extend with slot metadata and current slot tracking
4. **UI Scaling**: Use existing `ui_scaling` config for consistent sizing

### UI Styling Standards
- **Colors**: Use existing blue-grey theme from other UIs
- **Fonts**: Use `get_font()` with `FONT_LARGE`, `FONT_NORMAL`, `FONT_SMALL`
- **Sizing**: Use `scale_value()` and `get_element_size()` for responsive design
- **Spacing**: Use `get_spacing()` for consistent margins/padding

### Event Handling Pattern
Follow existing pattern from other UI controllers:
```python
def handle_event(self, event: pygame.event.Event) -> bool:
    """Return True if event was consumed"""
    if not self.is_visible():
        return False
    # Handle events...
    return event_consumed
```

### Save Data Structure
Extend existing save format with:
```python
{
    "metadata": {
        "save_slot": "slot_1",
        "timestamp": "2024-01-01T12:00:00",
        "level_name": "Town of Caledon", 
        "player_level": 5,
        "player_gold": 150,
        "version": "1.0.0"
    },
    # ... existing game state data
}
```

## Testing Approach
1. Test startup flow (settings UI appears first)
2. Test save/load with all 10 slots
3. Test context-aware escape key in all UI states
4. Test help panel display and navigation
5. Test game state persistence across save/load cycles

## Notes
- Maintain existing quick save/load (F5/F9) functionality
- Preserve existing UI pause behavior (game pauses when any UI is open)
- Use existing asset management and audio systems
- Follow Clean Architecture patterns used throughout the codebase
- Ensure all UI elements scale properly with `ui_scaling.scale_factor`
