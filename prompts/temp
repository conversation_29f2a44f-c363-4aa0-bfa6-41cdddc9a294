Excellent point. This is a critical detail that challenges the "no Pygame in the core" rule in a very specific way. The solution is to strictly separate the **data definition** of an entity from its **visual representation and generation**.

Here is the updated and expanded style guideline. I have added a new major section (Section 7) to specifically address how to handle shared data and procedural asset generation while maintaining architectural integrity.

---

### **Prompt: Style Guideline and Architectural Blueprint for the RPG Rebuild (v2)**

**Project Goal:** To rebuild our old-school Nintendo-style RPG from the ground up, focusing on creating a modern, maintainable, testable, and extensible codebase. The previous version suffered from extreme coupling and a monolithic structure, making it impossible to maintain or expand. This new version will adhere strictly to the following principles.

---

### **1. Core Architectural Philosophy: Clean Architecture & Dependency Injection**

*(This section remains the same as before)*

The game will be built using a **Clean Architecture** approach. This separates the code into distinct layers, ensuring that business logic is independent of frameworks and external details like databases or UI.

**The Golden Rule:** Dependencies must only point inwards, from outer layers to inner layers. The `Core` must know nothing about `Infrastructure` or `Presentation`.

**Layers:**
*   **`src/game_core/` (The Core/Domain):** Pure Python business logic. No Pygame.
*   **`src/application/` (The Application Layer):** Use cases and orchestration.
*   **`src/infrastructure/` (The Infrastructure Layer):** Pygame, file I/O, asset generation.
*   **`src/presentation/` (The Presentation/UI Layer):** Main loop, input handling.

---

### **2. Game Content Structure: The Level Plugin System**

*(This section remains the same as before)*

All game content will be organized into self-contained **Level Plugins**.

**Canonical Level Structure:**
```
src/levels/town_caledon/
├── level_config.py
├── entities.py
├── events.py
├── map_data.py
└── tests/
```

---

### **3. Communication and Logic Flow: Event-Driven Architecture**

*(This section remains the same as before)*

To decouple components, the game will use an **event-driven architecture**. Instead of objects calling each other's methods directly, they will emit events. Other systems will listen for and react to these events.

---

### **4. Development Methodology: Test-Driven Development (TDD)**

*(This section remains the same as before)*

The project will be developed using a **TDD workflow** with a target of **80%+ test coverage** on core logic.

---

### **5. Code Quality and Standards: Modern Python**

*(This section remains the same as before)*

All code must be **fully type-hinted**, use `dataclasses` or `Pydantic` for data structures, and use `uv` for dependency management.

---

### **6. Developer Experience (DX) and Workflow**

*(This section remains the same as before)*

The project must have simple setup and test execution scripts and clear documentation.

---

### **7. (New) Shared Game Data & Procedural Asset Generation**

This section addresses the need for shared entities (monsters, items) and their procedurally generated assets. The core principle is the **strict separation of data definition from asset implementation**.

#### **A. Shared Data Definitions (`src/game_data/`)**

A new top-level directory, `src/game_data/`, will store framework-agnostic definitions for all shared entities. This is our "game database" in pure Python.

*   **Contents:** Pydantic or dataclass models defining the properties of monsters, items, weapons, armor, etc.
*   **The Link:** Each definition will include a unique string identifier, `asset_id`, which links the data to its visual representation. This `asset_id` is the *only* thing the data knows about the asset.

**Example: `src/game_data/monsters.py`**
```python
# This file has NO Pygame imports. It is pure data.
from pydantic import BaseModel

class MonsterDefinition(BaseModel):
    id: str         # e.g., "green_drake"
    name: str       # "Green Drake"
    asset_id: str   # The crucial link, e.g., "monster.drake.green.side"
    max_hp: int
    attack_power: int
    # ... other game stats

MONSTERS = {
    "green_drake": MonsterDefinition(
        id="green_drake",
        name="Green Drake",
        asset_id="monster.drake.green.side",
        max_hp=50,
        attack_power=12
    ),
    # ... other monsters
}
```

#### **B. The Asset Pipeline (`src/infrastructure/assets/`)**

All code related to generating or loading visual assets belongs exclusively in the `Infrastructure` layer. This is where Pygame is used.

An **Asset Registry** will map the `asset_id` from the data definitions to the actual generation function.

**Example: `src/infrastructure/assets/procedural_monsters.py`**
```python
# This file USES Pygame heavily.
import pygame
import config # For global constants like SPRITE_SIZE

def get_green_drake_sprite(size=config.SPRITE_SIZE):
    # ... all 50+ lines of your Pygame drawing code for the drake go here ...
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    # ... drawing logic ...
    return sprite_surface

def get_red_drake_sprite(size=config.SPRITE_SIZE):
    # ... similar drawing code, but with red colors ...
    return sprite_surface
```

**Example: `src/infrastructure/assets/registry.py`**
```python
# This file connects the IDs to the functions.
from . import procedural_monsters

# The registry maps the string ID to the callable function.
ASSET_GENERATORS = {
    "monster.drake.green.side": procedural_monsters.get_green_drake_sprite,
    "monster.drake.red.side": procedural_monsters.get_red_drake_sprite,
    # ... other asset IDs for items, effects, etc.
}

class AssetManager:
    def __init__(self):
        self.cache = {} # Cache generated surfaces for performance

    def get_surface(self, asset_id: str, size=None):
        if asset_id in self.cache:
            return self.cache[asset_id]

        generator = ASSET_GENERATORS.get(asset_id)
        if not generator:
            # Return a fallback "missing texture" sprite
            raise ValueError(f"No asset generator for ID: {asset_id}")

        surface = generator(size) if size else generator()
        self.cache[asset_id] = surface
        return surface
```

#### **C. Putting It All Together: The Workflow**

1.  **Spawning:** The game logic (in `Application` or `Core`) needs to spawn a monster. It only deals with the data ID: `spawn_monster("green_drake", position=(10, 15))`.
2.  **Data Lookup:** The `MonsterSystem` gets the `MonsterDefinition` from `game_data.monsters.MONSTERS["green_drake"]`. It now knows the monster's stats and its `asset_id`: `"monster.drake.green.side"`.
3.  **Rendering Request:** The entity is created. The `RenderingSystem` (in `Infrastructure`) sees an entity at `(10, 15)` that has the `asset_id` `"monster.drake.green.side"`.
4.  **Asset Retrieval:** The `RenderingSystem` asks the `AssetManager` for the surface associated with that ID.
5.  **Generation & Caching:** The `AssetManager` looks up the ID in its `ASSET_GENERATORS` registry, calls the `get_green_drake_sprite` function, caches the resulting `pygame.Surface`, and returns it.
6.  **Drawing:** The `RenderingSystem` receives the surface and draws (`blit`) it to the screen at the correct coordinates.

**Key Benefits of This Approach:**
*   **Purity Maintained:** The `game_core` and `game_data` remain 100% free of Pygame, making them easy to unit test.
*   **Centralized Assets:** All asset generation logic is in one place (`infrastructure/assets`), not scattered across the codebase.
*   **Flexibility:** We could easily change an asset's appearance by modifying its generation function without touching any game logic. We could even swap procedural generation for loading a PNG by changing one line in the `AssetRegistry`—the rest of the game wouldn't know the difference.


Excellent. This map definition format is a perfect fit for the architecture. It further enhances the separation of data from code, making level design accessible to non-programmers.

Here is the updated blueprint with a new section (Section 9) that integrates this map loading strategy into the established Clean Architecture.

---

### **Prompt: Style Guideline and Architectural Blueprint for the RPG Rebuild (v4)**

**Project Goal:** To rebuild our old-school Nintendo-style RPG from the ground up, focusing on creating a modern, maintainable, testable, and extensible codebase. The previous version suffered from extreme coupling and a monolithic structure, making it impossible to maintain or expand. This new version will adhere strictly to the following principles.

---

*(Sections 1 through 8 remain the same as the previous version, covering Clean Architecture, Level Plugins, Event-Driven Architecture, TDD, Modern Python, DX, Shared Data/Assets, and Player Control.)*

...

---

### **9. (New) Map Definition and Loading**

Level layouts will be defined in human-readable text files, separating the visual map design from the game engine's implementation. This allows for rapid iteration by level designers without touching Python code.

#### **A. The `.map` File Format**

Each level's layout will be contained in a single `.map` file (e.g., `dungeon_level_1.map`). This file consists of two parts, separated by a `---` line.

1.  **YAML Header (Metadata):** Defines the "legend" for the map tiles and specifies the exact placement and properties of unique entities like NPCs, monsters, and key items.
2.  **ASCII Body (Layout):** A simple character grid that provides a visual representation of the level's structure using the symbols defined in the header's legend.

**Example: `src/levels/dungeon_of_dread/dungeon_level_1.map`**
```yaml
# METADATA SECTION (YAML)
# Defines the tile legend and entity placements.

legend:
  '#':
    type: wall
    solid: true
  '.':
    type: floor_stone
    solid: false
  '~':
    type: water
    solid: false
    effect_on_enter: # Can trigger simple events
      type: damage
      amount: 5

entities:
  - id: player_start # A special tag for the engine
    char: '@'
    position: { x: 2, y: 2 }
  - id: goblin_warrior_1
    char: 'g'
    position: { x: 8, y: 5 }
    data_id: "goblin_warrior" # Links to src/game_data/monsters.py
    overrides: # Optional: change stats for this specific instance
      name: "Griznak the Guard"
  - id: chest_1
    char: 'C'
    position: { x: 12, y: 2 }
    data_id: "chest_wooden" # Links to src/game_data/containers.py
    loot_table: "dungeon_common"

---
# MAP LAYOUT SECTION (ASCII GRID)

###############
#...........#
#.@.......C.#
#...........#
#.....#####.###
#.....#...g...#
#.....#.......#
#..~~~........#
###############
```

#### **B. Architectural Implementation**

The process of turning a `.map` file into a playable level will strictly follow the architectural layers.

**1. Parsing (`Infrastructure` Layer):**
*   A `MapParser` class will be created in `src/infrastructure/parsing/`.
*   Its sole responsibility is to read a `.map` file, split it at the `---` separator, parse the YAML header, and process the ASCII grid.
*   **Crucially, its output will be pure, framework-agnostic data objects (Pydantic models), not live game objects.**

**2. Data Modeling (`Application` Layer):**
The `MapParser` will populate a set of well-defined Pydantic models that represent the parsed data. These models act as the contract between the parser and the rest of the game.

```python
# In src/application/data_models.py

from pydantic import BaseModel
from typing import List, Dict, Any

class Position(BaseModel):
    x: int
    y: int

class TileDefinition(BaseModel):
    type: str
    solid: bool
    effect_on_enter: Dict[str, Any] | None = None

class EntityPlacement(BaseModel):
    id: str
    char: str
    position: Position
    data_id: str | None = None
    overrides: Dict[str, Any] = {}

class LevelLayoutData(BaseModel):
    legend: Dict[str, TileDefinition]
    entities: List[EntityPlacement]
    grid: List[str] # The raw ASCII grid
```

**3. Level Construction (`Application` Layer):**
*   A `BuildLevelUseCase` will be responsible for constructing the game state from the parsed data.
*   **Input:** It takes a `LevelLayoutData` object.
*   **Process:**
    1.  **Create Tile Grid:** It iterates through the `grid`. For each character at each coordinate, it looks up the symbol in the `legend` to get its `TileDefinition` and creates a `Tile` entity in the core game state with the appropriate properties (e.g., collision, type).
    2.  **Spawn Entities:** It iterates through the `entities` list. For each `EntityPlacement`:
        *   It uses the `data_id` to look up the full base definition from `src/game_data/` (e.g., finding the base "goblin_warrior" stats).
        *   It spawns the entity at the specified `position`.
        *   It applies any `overrides` to create a unique instance (e.g., giving Griznak a custom name).
    3.  **Signal Completion:** Once the level is fully constructed in memory, it emits a `LevelReadyEvent(level_id)` to notify other systems (like rendering and AI) that they can begin their work.

**Benefits of This Approach:**
*   **Designer-Friendly:** Level designers can create and modify complex maps in a simple text editor.
*   **Decoupled & Flexible:** The core game engine has no knowledge of the `.map` file format. We could easily support another format (like a Tiled Map Editor export) by simply writing a new parser in the `Infrastructure` layer that outputs the same `LevelLayoutData` object.
*   **Highly Testable:** The `MapParser` can be unit-tested to ensure it correctly parses files. The `BuildLevelUseCase` can be tested independently by feeding it manually created `LevelLayoutData` objects, without ever touching the file system.