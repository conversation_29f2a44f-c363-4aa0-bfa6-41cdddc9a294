Of course. Here is the final, consolidated version of the style guideline and architectural blueprint, incorporating all the requested sections into a single, comprehensive prompt.

---

### **Prompt: Final Architectural Blueprint & Style Guide for the RPG Rebuild**

**Project Goal:** To rebuild our old-school Nintendo-style RPG from the ground up, focusing on creating a modern, maintainable, testable, and extensible codebase. The previous version suffered from extreme coupling and a monolithic structure, making it impossible to maintain or expand. This new version will adhere strictly to the following principles.

### **1. Core Philosophy: Clean Architecture**

The game will be built using a **Clean Architecture** approach. This separates the code into distinct layers, ensuring that business logic is independent of frameworks and external details.

**The Golden Rule:** Dependencies must only point inwards. The `Core` layer must know nothing about the `Infrastructure` or `Presentation` layers.

*   **`src/game_core/` (The Core/Domain):** Contains pure Python business logic (game rules, entity stats, combat formulas). It must have **NO** external dependencies like Pygame or file I/O. It is the heart of the game.
*   **`src/application/` (The Application Layer):** Orchestrates the flow of data between the Core and outer layers. It contains use cases (e.g., `MovePlayerUseCase`) and defines interfaces (Abstract Base Classes) for the `Infrastructure` layer to implement.
*   **`src/infrastructure/` (The Infrastructure Layer):** Implements all external concerns: Pygame rendering, audio playback, file saving/loading, and procedural asset generation. It implements the interfaces defined in the `Application` layer.
*   **`src/presentation/` (The Presentation Layer):** The main game loop, UI rendering, and input handling. It drives the application by calling use cases in response to user input.

### **2. Code Quality & Developer Experience**

The project must be easy to set up, test, and contribute to.

*   **Type Hinting:** All new code **must** be fully type-hinted and pass static analysis with `mypy`.
*   **Data Structures:** Use `Pydantic` models or `dataclasses` for all structured data (e.g., `PlayerStats`, `ItemData`, `LevelLayoutData`).
*   **Dependency Management:** The project will use `uv` for fast and reliable dependency management.
*   **Setup Scripts:** A simple `./run.sh setup` script will create the virtual environment and install all dependencies. A `./run.sh test` script will execute the entire test suite.

### **3. Methodology: Test-Driven Development (TDD)**

The project will be developed using a TDD workflow. This is non-negotiable.

*   **Unit Tests:** Target the `game_core` logic. Must be fast and have zero Pygame dependencies.
*   **Integration Tests:** Verify the interaction between layers (e.g., Application and Infrastructure).
*   **Test Coverage:** A minimum of **80% test coverage** is required for the `game_core` and `application` layers.

### **4. Communication: Event-Driven Architecture**

Components will be decoupled using an event bus. Instead of calling each other's methods directly, systems will emit events that other systems can subscribe and react to. For example, a `CombatSystem` emits an `EntityDamagedEvent` rather than directly calling a method on the `RenderingSystem` to make a sprite flash.

### **5. Shared Game Data & Procedural Assets**

Game entities (monsters, items) and their assets are shared across levels. Their definition is strictly separated from their visual representation.

*   **Data Definitions (`src/game_data/`):** Pure Python/Pydantic models defining the properties of all shared entities. Each definition includes a unique string `asset_id` (e.g., `"monster.drake.green.side"`) but contains **no Pygame code**.
*   **Asset Pipeline (`src/infrastructure/assets/`):** This is where all Pygame code for generating or loading assets lives. An **Asset Registry** will map each `asset_id` to its corresponding Pygame generation function (e.g., `get_green_drake_sprite()`). An `AssetManager` will use this registry to retrieve and cache generated `pygame.Surface` objects on demand.

### **6. Content Structure: The Level Plugin System**

Each game area (a town, a dungeon) is a self-contained Python package that can be loaded dynamically.

**Canonical Level Structure:**
```
src/levels/town_caledon/
├── __init__.py
├── level_config.py      # Level metadata (e.g., music track)
├── caledon_docks.map    # Map layout file (see Section 7)
└── tests/               # Level-specific tests
```

### **7. Map Definition and Loading**

Level layouts will be defined in human-readable `.map` files using a "base-plus-override" model.

*   **Base Legend (`src/game_data/base_legend.yaml`):** A global YAML file defining all common tiles and entities (e.g., `'#': wall`, `'g': goblin_grunt`). This is the game's default palette.
*   **Level `.map` File:** A text file with two parts separated by `---`:
    1.  **YAML Header:** Contains a `legend` that can override base definitions (e.g., making `.` sand instead of dirt) or append new ones. It also has an optional `placements` list for defining unique entities with special properties at specific coordinates.
    2.  **ASCII Body:** A character grid where each character corresponds to an entry in the final, merged legend, providing a WYSIWYG layout.

**Example: `oasis.map`**
```yaml
# METADATA: This level's legend overrides and additions.
legend:
  '.': { category: tile, type: floor_sand, asset_id: "tile.floor.sand" }
  'S': { category: entity, data_id: "sand_scorpion" }
placements:
  - char: 'S', position: { x: 8, y: 5 }, overrides: { name: "Skarth" }
---
# MAP LAYOUT
~~~~~~~~~~~
~..S....g..~  # 'S' is Skarth, 'g' is a standard goblin from base_legend
~~~~~~~~~~~
```

*   **Implementation:** An infrastructure `MapParser` will load the base legend, merge it with the level's legend, and output a clean `LevelLayoutData` object for an application `BuildLevelUseCase` to consume.

### **8. Player Control: Movement and Attacking**

Player controls are a prime example of architectural separation.

*   **Movement (WASD):** The `Presentation` layer captures a `W` key press and translates it into a `MovePlayerCommand(direction=Direction.NORTH)`. This command is sent to an `Application` use case, which uses the `Core` logic to validate and execute the move. The `Core` does not know what a keyboard is.
*   **Attacking (Mouse Aim):**
    1.  The `Presentation` layer detects a mouse click.
    2.  It calculates a **normalized direction vector** from the player's on-screen position to the mouse's on-screen position.
    3.  It creates a `PlayerAttackCommand(direction_vector=Vector2(x, y))`.
    4.  This command is sent to a use case, which passes the vector to the `Core`'s combat system.
    5.  The `Core` logic uses the vector to determine the attack's area of effect or projectile path. The `RenderingSystem` uses the same vector to orient the attack animation. The core logic is completely independent of the mouse.