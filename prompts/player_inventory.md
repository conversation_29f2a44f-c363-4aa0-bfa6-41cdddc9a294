# Inventory and Character Sheet UI Prompt

## Project Overview

Examine the **Treebeard's Revenge** RPG game to understand its Clean Architecture design, procedural asset generation system, and the recently implemented Enhanced Player Asset System. Build a comprehensive inventory and character sheet UI that integrates seamlessly with the existing equipment and inventory systems.

## Current State

The game now has:
- Enhanced Player entity with equipment slots and inventory system
- Equipment and inventory use cases (EquipItemUseCase, GetInventoryUseCase, etc.)
- Procedural player sprite generation with equipment layering
- Starting equipment and inventory items
- Asset generation system for player sprites

## Task: Inventory and Character Sheet UI System

Create a comprehensive inventory and character sheet interface that provides:

### 1. UI Architecture Requirements

**Presentation Layer Enhancement (`src/presentation/`):**
- Create new UI components following Clean Architecture principles
- Integrate with existing pygame renderer system
- Maintain separation between UI logic and game logic
- Use application layer use cases for all data operations

**UI State Management:**
- Track UI open/closed state
- Handle equipment slot interactions
- Manage inventory grid interactions
- Coordinate between character sheet and inventory views

### 2. Character Sheet Panel

**Equipment Visualization:**
```python
# Character sheet layout with equipment slots
EQUIPMENT_SLOTS = {
    "head_equipment": {"position": (100, 50), "size": (40, 40)},
    "chest_equipment": {"position": (100, 100), "size": (40, 40)},
    "legs_equipment": {"position": (100, 150), "size": (40, 40)},
    "boots_equipment": {"position": (100, 200), "size": (40, 40)},
    "main_hand_weapon": {"position": (50, 125), "size": (40, 40)},
    "off_hand_equipment": {"position": (150, 125), "size": (40, 40)}
}
```

**Player Model Display:**
- Central character outline/silhouette showing equipped items
- Visual representation of current equipment on character
- Real-time updates when equipment changes
- Hover effects showing equipment details

**Equipment Slot Interactions:**
- Click to unequip items (return to inventory)
- Drag-and-drop from inventory to equip
- Right-click context menus for equipment actions
- Visual feedback for valid/invalid equipment slots

### 3. Inventory Grid System

**Grid Layout:**
```python
INVENTORY_CONFIG = {
    "grid_size": (5, 4),  # 5 columns, 4 rows = 20 slots
    "slot_size": (32, 32),
    "slot_padding": 4,
    "grid_position": (250, 50)
}
```

**Inventory Features:**
- Visual grid showing all 20 inventory slots
- Item icons with quantity indicators
- Empty slot placeholders
- Drag-and-drop item management
- Item stacking visualization
- Scroll support if needed for expansion

**Item Interaction:**
- Left-click to select/move items
- Right-click for item context menus
- Double-click to equip (if applicable)
- Drag between inventory slots
- Drag to equipment slots to equip
- Quantity splitting for stackable items

### 4. Procedural Item Icon Generation

**Icon Generation System (`src/infrastructure/assets/procedural_items.py`):**

#### A. Base Icon Framework
```python
def get_item_icon(item_id: str, size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate procedural icon for any item."""
    # Get item data from game_data/items.py
    # Route to specific item icon generator based on item_id
    # Each item has its own unique icon generation logic
    # Use item properties (visual_style, colors, etc.) for consistency

# Individual item icon generators - each item gets unique logic
def get_rusty_sword_icon(size: Tuple[int, int]) -> pygame.Surface:
    """Generate rusty sword icon - brown/orange blade with simple hilt."""

def get_iron_sword_icon(size: Tuple[int, int]) -> pygame.Surface:
    """Generate iron sword icon - metallic gray blade with detailed hilt."""

def get_wooden_bow_icon(size: Tuple[int, int]) -> pygame.Surface:
    """Generate wooden bow icon - curved brown bow with string."""

def get_cloth_cap_icon(size: Tuple[int, int]) -> pygame.Surface:
    """Generate cloth cap icon - simple brown cap shape."""

def get_leather_armor_icon(size: Tuple[int, int]) -> pygame.Surface:
    """Generate leather armor icon - chest piece with leather texture."""

def get_health_potion_icon(size: Tuple[int, int]) -> pygame.Surface:
    """Generate health potion icon - bottle with red liquid."""

def get_bread_icon(size: Tuple[int, int]) -> pygame.Surface:
    """Generate bread icon - loaf shape with brown crust."""

def get_gold_coin_icon(size: Tuple[int, int]) -> pygame.Surface:
    """Generate gold coin icon - circular with golden color and shine."""

# Icon registry mapping item IDs to their specific generators
ITEM_ICON_GENERATORS = {
    "rusty_sword": get_rusty_sword_icon,
    "iron_sword": get_iron_sword_icon,
    "wooden_bow": get_wooden_bow_icon,
    "cloth_cap": get_cloth_cap_icon,
    "leather_armor": get_leather_armor_icon,
    "health_potion": get_health_potion_icon,
    "bread": get_bread_icon,
    "gold_coin": get_gold_coin_icon,
    # ... add all other items
}
```

#### B. Icon Style Guidelines
- **Clarity**: Icons must be readable at 32x32 and 24x24 sizes
- **Consistency**: Unified art style across all item types
- **Color Coding**: Use item properties for consistent coloring
- **Symbolism**: Clear visual metaphors for item types
- **Contrast**: Ensure icons stand out against UI backgrounds

#### C. Specific Icon Requirements
Each item needs its own unique icon generator function. All items from `game_data/items.py`:

```python
# All items requiring unique icon generation:
REQUIRED_ITEM_ICONS = [
    # Weapons
    "rusty_sword",     # Starting weapon - metallic gray with rusty brown stripes on blade with simple hilt
    "iron_sword",      # Upgraded sword - metallic gray blade with simple hilt
    "wooden_bow",      # Ranged weapon - simple curved brown bow with visible string
    "wooden_shield",   # Defense - round wooden shield with wood grain texture
    "iron_shield",     # Upgraded defense - metallic shield with reinforced edges

    # Armor pieces
    "cloth_cap",       # Simple brown cloth cap with soft fabric appearance
    "cloth_shirt",     # Light gray/white shirt with fabric folds
    "cloth_pants",     # Brown simple pants with basic stitching
    "simple_shoes",    # Dark brown shoes with laces or straps
    "leather_cap",     # Leather headwear with stitched seams
    "leather_armor",   # Leather chest piece with buckles and straps
    "leather_boots",   # Leather footwear with detailed stitching
    "iron_helmet",     # Metallic helmet with visor and rivets
    "iron_greaves",    # Metallic leg armor with articulated plates

    # Consumables
    "health_potion",   # Glass bottle with red liquid and cork stopper
    "mana_potion",     # Glass bottle with blue liquid and mystical glow
    "bread",           # Loaf of bread with golden-brown crust and scoring

    # Miscellaneous
    "gold_coin",       # Circular golden coin with embossed design
    "ruby_gem",        # Red crystalline gem with faceted surface
    "ancient_key",     # Ornate key with decorative head and aged metal
]

# Each item should have a dedicated generator function:
# get_{item_id}_icon(size: Tuple[int, int]) -> pygame.Surface
```

### 5. Player Stats Panel

**Stats Display Layout:**
```python
STATS_PANEL_CONFIG = {
    "position": (450, 50),
    "size": (200, 300),
    "sections": [
        "basic_info",      # Name, level, experience
        "core_stats",      # HP, MP with bars
        "attributes",      # Strength, defense, speed
        "equipment_bonuses" # Calculated bonuses from gear
    ]
}
```

**Stats Information:**
- **Basic Info**: Player name, current level, experience points
- **Health/Mana**: Current/max HP and MP with visual bars
- **Core Attributes**: Strength, Defense, Speed values
- **Equipment Bonuses**: Calculated bonuses from equipped items
- **Inventory Status**: Used/total inventory space

**Visual Elements:**
- Progress bars for HP/MP with color coding
- Numeric displays for all stats
- Equipment bonus indicators (+2 Defense, etc.)
- Level progress visualization
- Clean, readable typography

### 6. UI State Management

**UI Controller (`src/presentation/ui/`):**
```python
class InventoryUIController:
    """Manages inventory and character sheet UI state."""
    
    def __init__(self, event_bus, asset_manager):
        self.is_open = False
        self.selected_item = None
        self.drag_state = None
        self.hover_slot = None
    
    def toggle_inventory(self):
        """Open/close inventory interface."""
    
    def handle_equipment_slot_click(self, slot_name: str):
        """Handle clicking on equipment slots."""
    
    def handle_inventory_slot_click(self, slot_index: int):
        """Handle clicking on inventory grid slots."""
    
    def handle_drag_drop(self, source, target):
        """Handle drag-and-drop operations."""
    
    def update_display(self, game_state: GameStateData):
        """Update UI based on current game state."""
```

**UI Events:**
- Inventory open/close toggle (I key)
- Equipment slot interactions
- Inventory slot interactions
- Drag-and-drop operations
- Item context menus
- Tooltip displays

### 7. Integration with Use Cases

**UI Use Case Integration:**
```python
# Equipment operations
def equip_item_from_inventory(self, item_id: str):
    """Use EquipItemUseCase to equip item."""
    
def unequip_item_to_inventory(self, slot_name: str):
    """Use UnequipItemUseCase to unequip item."""

# Inventory operations  
def move_inventory_item(self, from_slot: int, to_slot: int):
    """Rearrange items within inventory."""
    
def split_item_stack(self, slot_index: int, quantity: int):
    """Split stackable items."""

# Data retrieval
def get_current_inventory(self):
    """Use GetInventoryUseCase for current inventory state."""
    
def get_player_visual_data(self):
    """Use GetPlayerVisualDataUseCase for equipment display."""
    
def get_player_stats(self):
    """Get current player stats for display."""
```

### 8. Visual Design Requirements

**UI Theme:**
- Medieval/fantasy aesthetic matching game style
- Earth tones and muted colors
- Clear borders and backgrounds
- Consistent with existing UI elements

**Layout Specifications:**
```python
UI_LAYOUT = {
    "character_panel": {
        "position": (50, 50),
        "size": (180, 250),
        "background_color": (40, 30, 20, 200),  # Semi-transparent brown
        "border_color": (100, 80, 60)
    },
    "inventory_panel": {
        "position": (250, 50), 
        "size": (200, 200),
        "background_color": (40, 30, 20, 200),
        "border_color": (100, 80, 60)
    },
    "stats_panel": {
        "position": (470, 50),
        "size": (200, 300),
        "background_color": (40, 30, 20, 200),
        "border_color": (100, 80, 60)
    }
}
```

**Visual Feedback:**
- Hover effects on interactive elements
- Selection highlighting for active items
- Drag preview during drag-and-drop
- Equipment slot compatibility indicators
- Quantity badges on stackable items

### 9. Tooltip System

**Item Tooltips:**
```python
def generate_item_tooltip(item_id: str) -> List[str]:
    """Generate tooltip text for items."""
    # Item name and description
    # Stats and bonuses
    # Requirements and restrictions
    # Value and rarity information
    # Usage instructions
```

**Tooltip Features:**
- Detailed item information on hover
- Equipment stats and bonuses
- Item descriptions and lore
- Usage instructions
- Value and rarity indicators
- Comparison with currently equipped items

### 10. Performance Considerations

**Rendering Optimization:**
- Cache UI panels and only redraw when changed
- Batch icon generation and cache results
- Efficient drag-and-drop visual feedback
- Minimize full UI redraws

**Asset Management:**
- Pre-generate common item icons
- Cache equipment combinations
- Lazy load less common icons
- Memory management for UI textures

### 11. Accessibility Features

**User Experience:**
- Keyboard navigation support
- Clear visual feedback for all interactions
- Consistent interaction patterns
- Error prevention and recovery
- Undo functionality for accidental actions

**Visual Clarity:**
- High contrast for readability
- Clear iconography and symbols
- Consistent spacing and alignment
- Readable font sizes and styles

### 12. Expected Deliverables

1. **Character Sheet Panel** with equipment slots and player model
2. **Inventory Grid System** with 20-slot layout and interactions
3. **Procedural Item Icon Generation** for all item types
4. **Player Stats Display** with health, mana, and attribute information
5. **UI State Management** for opening/closing and interactions
6. **Drag-and-Drop System** for equipment and inventory management
7. **Tooltip System** for detailed item information
8. **Visual Design Implementation** with consistent theming
9. **Use Case Integration** for all inventory and equipment operations
10. **Performance Optimization** for smooth UI interactions

### 13. Technical Constraints

- **Maintain Clean Architecture** - UI in presentation layer only
- **Use Existing Use Cases** - no direct entity manipulation
- **Procedural Generation** - all icons generated, no external assets
- **Performance** - smooth 60fps with UI open
- **Scalability** - support for future item types and UI expansion
- **Integration** - seamless with existing game systems

### 14. Success Criteria

- Complete inventory management through intuitive UI
- Equipment changes reflected immediately in character display
- All items have clear, recognizable procedural icons
- Drag-and-drop works smoothly for all valid operations
- Player stats accurately reflect current equipment bonuses
- UI opens/closes smoothly without affecting game performance
- Tooltip system provides comprehensive item information
- Visual design matches game's medieval fantasy aesthetic
- System supports all existing equipment and inventory use cases
- Interface is intuitive for RPG players

This inventory and character sheet UI should provide players with a comprehensive interface for managing their character's equipment and items while maintaining the game's procedural generation philosophy and Clean Architecture principles. The system should feel familiar to RPG players while offering smooth, modern UI interactions.
