# Generic Global Quest System Implementation Prompt

Create a generic global quest system for a Python game that handles cross-level quests with state tracking and objectives. The quest system should be completely agnostic about levels, NPCs, or specific game entities - it only manages quest states and objectives. All game-specific logic should be handled by level-specific event handlers.

## Requirements:

### 1. Generic Global Quest Manager
- Create `src/game_core/quest_manager.py` with a `GlobalQuestManager` class
- Support quest states: NOT_STARTED, ACTIVE, COMPLETED, FAILED
- Handle generic objectives with completion tracking (no knowledge of what the objectives represent)
- Emit events when quest state changes (quest_started, objective_completed, quest_completed)
- Integrate with game state manager for save/load persistence
- Provide simple API: start_quest(), complete_objective(), get_quest_state(), etc.
- **NO** knowledge of NPCs, entities, levels, or dialogue

### 2. Level-Specific Quest Integration
- Level event handlers (`events.py`) are responsible for:
  - Listening to player interactions and triggering quest events
  - Managing NPC dialogue based on quest state
  - Spawning quest-related entities when needed
  - Handling rewards and quest completion actions
  - Translating game events into quest objective completions

### 3. Quest Definition Format
- Quests should be defined as simple data structures with:
  - Quest ID and metadata (name, description)
  - List of objective IDs (generic strings)
- **NO** hardcoded entity positions, dialogue, or level-specific logic

### 4. First Quest on the town_caledon map: "goblin_loose_in_caledon"

**Generic Quest Definition:**
```python
{
    "id": "goblin_loose_in_caledon",
    "name": "Goblin Problem",
    "description": "Deal with the goblin threat",
    "objectives": ["defeat_goblin"]
}
```

**Level-Specific Implementation (in events.py):**
- Player interacts with guard at (3,7) → Quest initializes, dialogue override shows quest offer
- Quest accepted → Level spawns goblin at (15,13), guard dialogue updates  
- Player kills goblin → Level calls `quest_manager.complete_objective("goblin_loose_in_caledon", "defeat_goblin")`
- Player returns to guard → Level checks quest state, shows completion dialogue, gives 10 gold

**Guard Dialogue Management (in events.py):**
```python
def get_guard_dialogue(self, quest_state):
    if quest_state == QuestState.NOT_STARTED:
        return "There's a dangerous goblin wandering our streets! Can you help us deal with it? I'll reward you with 10 gold if you can defeat it."
    elif quest_state == QuestState.ACTIVE:
        return "The goblin is still out there somewhere. Be careful, and come back when you've dealt with it."
    elif quest_state == QuestState.COMPLETED:
        return "You've done it! You defeated the goblin! Here's your reward of 10 gold."
    else:
        return "Thank you again for dealing with that goblin threat!"
```

## Technical Specifications:

- Quest Manager should be completely generic - no hardcoded game logic
- Use the existing event bus system for quest state change notifications
- Replace the logic in events.py as it is all just placeholder.
- Ensure quest state persists across game sessions
- Level event handlers are responsible for all game-specific behavior
- Clean separation: Quest Manager = state tracking, Events.py = game logic

## API Design:

**Quest Manager Methods:**
```python
def register_quest(quest_definition: Dict) -> bool
def start_quest(quest_id: str) -> bool
def complete_objective(quest_id: str, objective_id: str) -> bool
def get_quest_state(quest_id: str) -> QuestState
def get_active_quests() -> List[Quest]
def is_objective_completed(quest_id: str, objective_id: str) -> bool
```

**Events Emitted:**
- `quest_started` - Quest became active
- `objective_completed` - Individual objective completed
- `quest_completed` - All objectives completed

## Files to Create/Modify:

1. `src/game_core/quest_manager.py` - Generic quest state manager
2. Update `src/levels/town_caledon/events.py` - Level-specific quest logic and NPC behavior
3. `src/levels/town_caledon/quest_definitions.py` - Quest data definitions for this level

## Implementation Flow:

1. **Quest Registration:** Level loads and registers its quests with the quest manager
2. **Quest Initiation:** Player interacts with guard → Level event handler starts quest
3. **Dynamic Behavior:** Level listens for quest state changes and updates NPC dialogue/spawns entities
4. **Objective Completion:** Level translates game events (enemy defeated) into quest progress
5. **Rewards:** Level handles giving rewards when quest completes

## Expected Behavior:
- Generic quest system handles only state tracking and persistence
- Level-specific logic in events.py handles all gameplay mechanics
- Guard dialogue changes based on quest state (handled by level events)
- Goblin spawning and reward giving handled by level events
- System is extensible for any quest type across any level

## Environment:
Use the existing game architecture with event bus, interfaces, and the UV/Python environment. All tests should run with `./run.sh test`.
 