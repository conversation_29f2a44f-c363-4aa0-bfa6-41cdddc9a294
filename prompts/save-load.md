Create a prompt prompts/save-load-ai.md I can give to an AI to build the following:

I would like to add a save and load system to the game.

The game should have 10 save slots.

There should be a new settings UI, which has options for new game, load game, and save game (Save should save to the current slot, and there should be no option to overwrite games), and there should be an exit game option.

When the game starts, this UI should be presented to the player

Pressing escape should be context aware, if you're in a UI, it should exit that ui. If you're in the game itself, it should pause the game, and show the settings UI.