Write a prompt I can give to an AI to build the following:

Look at the README.md to understand the concept of this game.

I would like to drastically update the player of this game. Currently the player asset is just a dot on the screen.

I would like to have a new detailed generated asset for the player. This should be a human character, and what the character is wearing should be reflected on the asset.

The player can wear a head peice, chest piece, legs, and boots. The player can also have a melee weapon and a shield, or a ranged weapon.

It would be nice if there was some sort of walking animation, but since we are using renerated assets, the combination of swappable armour, and animation might be too difficult.


------------------------