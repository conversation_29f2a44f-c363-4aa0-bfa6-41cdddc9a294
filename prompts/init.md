### **Final Architectural Blueprint & Style Guide for the RPG Rebuild**

**Project Mission:** This document outlines the complete architectural blueprint for the ground-up rebuild of our old-school Nintendo-style RPG. The original codebase became unmaintainable due to a monolithic design, tight coupling, and a lack of modern development practices. The primary goal of this rebuild is to create a robust, maintainable, testable, and extensible foundation that will support the game's growth for years to come. Adherence to this guide is mandatory for all development.

---

### **Section 1: The Core Philosophy — Clean Architecture**

The entire game will be built upon the principles of **Clean Architecture**. This is a layered approach that isolates the core business logic of the game from external concerns like frameworks, databases, and user interfaces. This separation is the most critical aspect of the new design.

**The Golden Rule:** Dependencies must **only** point inwards, from outer layers to inner layers. The `Core` must be completely independent and ignorant of all other layers.

#### **Layer 1: `src/game_core/` (The Core/Domain)**
*   **Purpose:** This is the heart of the game. It contains the pure, abstract rules and data structures that define the game world, independent of how it is presented or run.
*   **Contents:**
    *   **Entities:** Abstract definitions for `Player`, `Monster`, `Item`, etc., as pure data classes.
    *   **Value Objects:** Immutable data structures like `Position(x, y)`, `Stats(hp, mp, str)`, etc.
    *   **Game Logic:** Pure Python functions for core mechanics like combat calculations (`calculate_damage`), status effect logic (`apply_poison`), and rule validation (`is_move_valid`).
*   **Strict Rules:**
    *   **Absolutely no imports from Pygame, file I/O libraries (e.g., `os`, `pathlib`), or any UI toolkit.**
    *   Contains no knowledge of rendering, sound, or specific input devices.
    *   This layer must be fully testable with a standard test runner like `pytest` without needing to initialize any external systems.

#### **Layer 2: `src/application/` (The Application Layer)**
*   **Purpose:** This layer orchestrates the flow of the game. It contains the specific "use cases" that represent user actions or game events, connecting the outer world to the core logic.
*   **Contents:**
    *   **Use Cases:** Classes like `MovePlayerUseCase`, `PlayerAttackUseCase`, `PurchaseItemUseCase`. These classes implement the step-by-step logic for a specific action.
    *   **Interfaces (Abstract Base Classes):** Definitions for services that the outer layers must provide. Examples include `IEventBus`, `ILevelRepository`, `IAssetManager`. This allows the application to depend on an abstraction, not a concrete implementation.
    *   **Data Transfer Objects (DTOs):** Pydantic models used to pass structured data between layers, such as `LevelLayoutData`.
*   **Strict Rules:**
    *   Depends only on the `Core` layer.
    *   Does not contain business logic itself but directs the `Core` to execute it.
    *   Has no knowledge of Pygame or specific UI implementations.

#### **Layer 3: `src/infrastructure/` (The Infrastructure Layer)**
*   **Purpose:** This layer is the bridge to the outside world. It contains the concrete implementations of the interfaces defined in the `Application` layer. It is where all external libraries and platform-specific details live.
*   **Contents:**
    *   **Framework Implementations:** `PygameEventBus`, `PygameRenderer`, `PygameSoundPlayer`.
    *   **Data Persistence:** `JsonSaveGameRepository`, `SqliteSaveGameRepository`.
    *   **Asset Management:** The `AssetManager`, `AssetRegistry`, and all procedural asset generation functions using Pygame (see Section 4).
    *   **Parsers:** The `MapParser` for reading `.map` files (see Section 7).
*   **Strict Rules:**
    *   Implements the interfaces from the `Application` layer.
    *   This is the *only* layer where libraries like `pygame` should be major dependencies.
    *   This layer should be completely replaceable. We could switch from Pygame to Godot by rewriting this layer while leaving the `Core` and `Application` layers untouched.

#### **Layer 4: `src/presentation/` (The Presentation Layer)**
*   **Purpose:** This layer is responsible for presenting the game to the user and capturing their input.
*   **Contents:**
    *   The main game loop (`while running:`).
    *   Input handling code that translates raw Pygame events (e.g., `pygame.KEYDOWN`) into commands for the `Application` layer.
    *   UI rendering logic that displays menus, HUDs, and dialogue boxes.
*   **Strict Rules:**
    *   Drives the entire application by calling use cases in the `Application` layer.
    *   Renders the game state provided to it; does not manage the state itself.

---

### **Section 2: Development Methodology, Quality, & DX**

A strong foundation requires rigorous development practices.

*   **Test-Driven Development (TDD):** Development will follow a TDD cycle (Red-Green-Refactor). This is essential for ensuring the `Core` logic is correct and for enabling safe refactoring.
    *   **Unit Tests:** Target the `game_core` and `application` logic. They must be fast, run in memory, and have zero dependencies on Pygame or I/O.
    *   **Integration Tests:** Verify the contracts between layers, for example, ensuring the `PygameRenderer` correctly implements the `IRenderer` interface.
    *   **Test Coverage:** We will target a minimum of **80% test coverage** for the `game_core` and `application` layers.
*   **Modern Python Standards:**
    *   **Full Type Hinting:** Every function and variable must be type-hinted. The codebase must pass `mypy --strict` checks.
    *   **Data Classes & Pydantic:** All structured data must be defined using `dataclasses` or, preferably, `Pydantic` models for automatic validation and clear schema definition.
    *   **Dependency Management:** `uv` will be used for its speed and reliability in managing virtual environments and dependencies.
*   **Developer Experience (DX):** The project must be easy for any developer to join.
    *   A `./run.sh setup` script will create the `uv` virtual environment and install all necessary dependencies.
    *   A `./run.sh test` script will execute the complete test suite.
    *   New systems and complex mechanics must be accompanied by documentation.

---

### **Section 3: Communication Patterns — Event-Driven Architecture**

To prevent the tight coupling that plagued the previous version, we will use an **event-driven architecture**. Instead of components calling each other's methods directly, they will emit events to a central bus.

*   **Problem (The Old Way):** `player.attack(monster)` -> `monster.take_damage(amount)` -> `ui.show_damage_number(monster, amount)` -> `audio.play_sound("hit.wav")`. This creates a tangled web of dependencies.
*   **Solution (The New Way):**
    1.  The `PlayerAttackUseCase` validates an attack and instructs the `Core` to calculate the outcome.
    2.  The `Core`'s combat logic determines a hit and emits an `EntityDamagedEvent(target_id="monster_1", damage=10)`.
    3.  This event is published to the `EventBus`.
    4.  Multiple, independent systems subscribe to this event:
        *   The `MonsterSystem` listens and updates the monster's HP in the game state.
        *   The `RenderingSystem` listens and triggers a "flash red" animation on the monster's sprite.
        *   The `UISystem` listens and displays a "10" damage number over the monster.
        *   The `AudioSystem` listens and plays the "hit.wav" sound.

This approach ensures that the combat logic, rendering, UI, and audio systems are completely decoupled from one another.

---

### **Section 4: Shared Game Data & Procedural Assets**

This section details how to manage game data (like monster stats) and their visual assets while respecting the architectural layers. The guiding principle is the **strict separation of data definition from asset implementation.**

#### **A. Shared Data Definitions (`src/game_data/`)**
This directory acts as our framework-agnostic "game database" in pure Python.

*   **Contents:** A series of Python files containing `Pydantic` models for all shared entities. For example, `src/game_data/monsters.py`, `src/game_data/items.py`.
*   **The Link:** Each definition includes a unique string identifier, `asset_id`, which is the sole link between the data model and its visual representation.

**Example: `src/game_data/monsters.py`**
```python
# This file has NO Pygame imports. It is pure data.
from pydantic import BaseModel

class MonsterDefinition(BaseModel):
    id: str         # e.g., "green_drake"
    name: str       # "Green Drake"
    asset_id: str   # The crucial link, e.g., "monster.drake.green.side"
    max_hp: int
    attack_power: int
    # ... other game stats

MONSTERS = {
    "green_drake": MonsterDefinition(...)
}
```

#### **B. The Asset Pipeline (`src/infrastructure/assets/`)**
This is where all Pygame code for generating or loading visual assets resides.

*   **Asset Generators:** Python files containing functions that use Pygame to procedurally draw a sprite. Each function returns a `pygame.Surface`.
    *   **Example:** `get_green_drake_sprite()` from the user prompt lives here.
*   **Asset Registry:** A simple dictionary that maps the string `asset_id` from the data definitions to the actual generation function.
    ```python
    # In src/infrastructure/assets/registry.py
    ASSET_GENERATORS = {
        "monster.drake.green.side": procedural_monsters.get_green_drake_sprite,
        # ... other asset IDs
    }
    ```

    **Example Asset Complexity**: for green_drake
    ```
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    body_color = (34, 139, 34) # Forest Green
    wing_color_far = (0, 80, 0)   # Darker Green for far wing
    wing_color_near = (0, 100, 0)  # Slightly lighter for near wing
    underbelly_color = (152, 251, 152) # Pale Green
    eye_color = (255, 255, 0) # Yellow
    claw_color = (60,60,60)

    # Body (main ellipse, side view)
    body_rect = pygame.Rect(s_w*0.2, s_h*0.35, s_w*0.6, s_h*0.35)
    pygame.draw.ellipse(sprite_surface, body_color, body_rect)
    pygame.draw.ellipse(sprite_surface, underbelly_color, (body_rect.left + s_w*0.05, body_rect.centery + s_h*0.05, body_rect.width*0.9, body_rect.height*0.4))

    # Tail (extending from left of body)
    tail_points = [
        (body_rect.left + s_w*0.05, body_rect.centery),
        (body_rect.left - s_w*0.2, body_rect.centery - s_h*0.05),
        (body_rect.left - s_w*0.3, body_rect.centery + s_h*0.05),
        (body_rect.left - s_w*0.2, body_rect.centery + s_h*0.15),
    ]
    pygame.draw.polygon(sprite_surface, body_color, tail_points)

    # Legs (two visible on the near side)
    leg_y_start = body_rect.bottom - s_h*0.05
    # Near front leg
    pygame.draw.rect(sprite_surface, body_color, (body_rect.centerx + s_w*0.1, leg_y_start, s_w*0.08, s_h*0.2))
    pygame.draw.polygon(sprite_surface, claw_color, [(body_rect.centerx + s_w*0.1, leg_y_start + s_h*0.2), (body_rect.centerx + s_w*0.18, leg_y_start + s_h*0.2), (body_rect.centerx + s_w*0.14, leg_y_start + s_h*0.25)])
    # Near back leg
    pygame.draw.rect(sprite_surface, body_color, (body_rect.centerx - s_w*0.15, leg_y_start, s_w*0.08, s_h*0.2))
    pygame.draw.polygon(sprite_surface, claw_color, [(body_rect.centerx - s_w*0.15, leg_y_start + s_h*0.2), (body_rect.centerx - s_w*0.07, leg_y_start + s_h*0.2), (body_rect.centerx - s_w*0.11, leg_y_start + s_h*0.25)])

    # Far Wing (drawn first, partially obscured)
    fw_p1 = (body_rect.centerx - s_w*0.05, body_rect.top + s_h*0.05) # Shoulder
    fw_p2 = (fw_p1[0] - s_w*0.2, fw_p1[1] - s_h*0.25) # Elbow/Mid
    fw_p3 = (fw_p2[0] + s_w*0.1, fw_p2[1] - s_h*0.15) # Tip
    fw_p4 = (fw_p1[0] + s_w*0.15, body_rect.centery - s_h*0.05) # Lower connection
    pygame.draw.polygon(sprite_surface, wing_color_far, [fw_p1, fw_p2, fw_p3, fw_p4])

    # Head and Neck (extending from right of body)
    neck_base_x, neck_base_y = body_rect.right - s_w*0.05, body_rect.centery - s_h*0.05
    neck_mid_x, neck_mid_y = neck_base_x + s_w*0.1, neck_base_y - s_h*0.1
    head_x, head_y = neck_mid_x + s_w*0.1, neck_mid_y - s_h*0.05
    
    pygame.draw.line(sprite_surface, body_color, (neck_base_x, neck_base_y), (neck_mid_x, neck_mid_y), int(s_w*0.1)) # Neck
    pygame.draw.circle(sprite_surface, body_color, (head_x, head_y), int(s_w*0.12)) # Head main
    pygame.draw.polygon(sprite_surface, body_color, [(head_x, head_y - s_h*0.05), (head_x + s_w*0.15, head_y), (head_x, head_y + s_h*0.05)]) # Snout
    pygame.draw.circle(sprite_surface, eye_color, (int(head_x + s_w*0.03), int(head_y - s_h*0.02)), int(s_w*0.03)) # Eye

    # Near Wing (drawn last, on top)
    nw_p1 = (body_rect.centerx + s_w*0.05, body_rect.top) # Shoulder
    nw_p2 = (nw_p1[0] - s_w*0.15, nw_p1[1] - s_h*0.3)  # Elbow/Mid (higher)
    nw_p3 = (nw_p2[0] + s_w*0.2, nw_p2[1] - s_h*0.1)   # Tip (sweeping back)
    nw_p4 = (nw_p1[0] + s_w*0.2, body_rect.centery)    # Lower connection
    pygame.draw.polygon(sprite_surface, wing_color_near, [nw_p1, nw_p2, nw_p3, nw_p4])
    ```
*   **Asset Manager:** A class that implements the `IAssetManager` interface. It uses the registry to retrieve assets, and it caches the generated surfaces to avoid re-drawing them every frame.

---

### **Section 5: Game Content Structure — The Level Plugin System**

To eliminate the monolithic config file, each game area will be a self-contained, discoverable Python package.

**Canonical Level Structure:**
```
src/levels/town_caledon/
├── __init__.py          # Makes this a loadable Python package.
├── level_config.py      # Level-specific metadata (e.g., music track, weather).
├── caledon_docks.map    # The map layout file for this area (see Section 7).
├── events.py            # Level-specific event handlers and quest logic.
```

---

### **Section 6: Map Definition and Loading**

Level layouts will be defined in human-readable `.map` files, empowering level designers and decoupling map data from game code. This uses a "base-plus-override" model.

#### **A. The Data Hierarchy**
1.  **Base Legend (`src/game_data/base_legend.yaml`):** A global YAML file defining all common tiles and entities. This is the game's default palette, ensuring consistency.
    ```yaml
    '#': { category: tile, type: wall, solid: true, asset_id: "tile.wall.stone" }
    '.': { category: tile, type: floor, solid: false, asset_id: "tile.floor.dirt" }
    'g': { category: entity, data_id: "goblin_grunt" }
    ```
2.  **Level-Specific `.map` File:** A text file for each map that can override or extend the base legend.

#### **B. The `.map` File Format**
The file has two parts, separated by `---`.

1.  **YAML Header:**
    *   `legend`: Defines level-specific symbols or overrides base ones.
    *   `placements`: A list for placing unique, named entities with special properties that cannot be defined in the general legend.
2.  **ASCII Body:** The visual grid layout.

**Example: `src/levels/desert_oasis/oasis.map`**
```yaml
# METADATA: This level's legend overrides and additions.
legend:
  # OVERRIDE: '.' is now sand instead of dirt.
  '.':
    category: tile
    type: floor_sand
    solid: false
    movement_modifier: 0.8
    asset_id: "tile.floor.sand"
  # APPEND: 'S' is a new entity type for this level.
  'S':
    category: entity
    data_id: "sand_scorpion"

# PLACEMENTS: Defines a unique boss version of a Sand Scorpion.
placements:
  - char: 'S'
    position: { x: 8, y: 5 }
    overrides:
      name: "Skarth, the Dune-Stalker"
      max_hp: 150

---
# MAP LAYOUT
~~~~~~~~~~~
~.........~
~..C.S.C..~
~....g....~  # 'g' is a standard goblin from base_legend.
~~~~~~~~~~~
```

#### **C. Architectural Implementation**
1.  **Parsing & Merging (`Infrastructure`):** A `MapParser` will first load the `base_legend.yaml`, then load the level's `.map` file, merge the two legends (with the level's taking precedence), and output a single, clean `LevelLayoutData` Pydantic object.
2.  **Level Construction (`Application`):** A `BuildLevelUseCase` will take the `LevelLayoutData`. It iterates through the grid, creating tiles and entities based on the final, merged legend. When it encounters a coordinate specified in the `placements` list, it applies the unique `overrides` to that specific entity instance.

---

### **Section 7: Player Control — Movement and Attacking**

Player controls will strictly adhere to the architectural layers, cleanly separating input devices from core game mechanics.

#### **A. Player Movement (WASD)**
1.  **Input (`Presentation`):** The main loop detects a `pygame.KEYDOWN` event for the `W` key.
2.  **Command (`Application`):** This raw input is translated into a semantic command, `MovePlayerCommand(direction=Direction.NORTH)`, and passed to the `MovePlayerUseCase`. Ensure this allows for full WASD movement including diagonals.
3.  **Logic (`Core`):** The use case calls a pure function in the core, `is_move_valid(player_state, direction)`, which checks the collision map. If valid, the player's logical position is updated. An event is emitted.
4.  **Feedback (`Infrastructure`):** The `RenderingSystem` listens for the `PlayerMovedEvent` and updates the sprite's on-screen position.

#### **B. Player Attacking (Mouse Aim)**
This allows for 360-degree aiming, independent of the player's facing direction.
1.  **Input (`Presentation`):** The main loop detects a `pygame.MOUSEBUTTONDOWN` event.
2.  **Translation & Command (`Application`):** This is the critical step.
    *   Get the mouse's screen coordinates (e.g., `(750, 200)`).
    *   Get the player sprite's center screen coordinates (e.g., `(400, 300)`).
    *   Calculate the raw direction vector: `(350, -100)`.
    *   **Normalize** this vector to get a unit vector representing pure direction: `Vector2(0.96, -0.28)`.
    *   Create the command: `PlayerAttackCommand(direction_vector=Vector2(0.96, -0.28))`.
3.  **Logic (`Core`):** The `PlayerAttackUseCase` passes this `direction_vector` to the core combat system. The core logic uses this vector to determine the path of a projectile or the arc of a sword swing. It performs hit detection and damage calculations. **The core has no idea this vector came from a mouse; it's just a direction.**
4.  **Feedback (`Infrastructure`):** The `RenderingSystem` listens for the `PlayerAttackedEvent` and uses the *same* `direction_vector` to rotate the attack animation sprite to point in the correct direction, ensuring visual and logical consistency. The `AudioSystem` plays the attack sound.