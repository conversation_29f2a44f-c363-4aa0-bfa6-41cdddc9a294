# NPC System Implementation Prompt

## Overview
Implement a comprehensive NPC (Non-Player Character) system for <PERSON>beard's Revenge that supports multiple NPC types with unique behaviors, interactions, and visual assets. The system should integrate seamlessly with existing game architecture and the map editor.

## Required NPC Types

### 1. Merchants
- **Behavior**: Open store interface when interacted with
- **Inventory**: General goods (potions, food, basic items, gems)
- **Dialog**: Greeting focused on trade and available wares
- **Visual**: Distinctive merchant appearance with trade-related accessories

### 2. Armourers  
- **Behavior**: Open store interface when interacted with
- **Inventory**: Armor pieces (helmets, chest armor, leg armor, boots, shields)
- **Dialog**: Greeting focused on protection and defense
- **Visual**: Wearing/displaying armor pieces, anvil or forge nearby

### 3. Weaponsmiths
- **Behavior**: Open store interface when interacted with  
- **Inventory**: Weapons (swords, axes, bows, daggers, staves)
- **Dialog**: Greeting focused on combat and weapon quality
- **Visual**: Holding/displaying weapons, smithing tools visible

### 4. Innkeepers
- **Behavior**: Open store interface when interacted with
- **Inventory**: Food items, lodging services, drinks
- **Dialog**: Welcoming, hospitality-focused
- **Visual**: Tavern keeper appearance with apron, keys, or serving items

### 5. Commoners
- **Behavior**: Display random dialog related to their town/location
- **Dialog**: Location-specific conversations about local events, gossip, daily life
- **Visual**: Simple civilian clothing, varied appearances
- **Randomization**: Multiple dialog options per location

### 6. Guards
- **Behavior**: Display random dialog related to their duties
- **Dialog**: Security-focused, mentions of patrols, law enforcement, threats
- **Visual**: Armor, weapons, official appearance
- **Randomization**: Multiple dialog options about guard duties

## Technical Requirements

### Integration Points
1. **Existing Systems**: Use current `AssetManager`, `MapParser`, `base_legend.yaml`, and level configuration
2. **Player Interaction**: Integrate with existing collision detection and interaction system
3. **Store Interface**: Create/extend store UI that integrates with inventory system
4. **Map Editor**: Add NPC placement tools with proper icons and categories

### Asset Generation
- Create procedural sprites for each NPC type using existing asset generation patterns
- Follow naming convention: `npc.{type}.{variant}` (e.g., `npc.merchant.general`, `npc.guard.town`)
- Generate appropriate icons for map editor: `icon.npc.{type}`
- Ensure consistent visual style with existing game assets

### Data Structure
```python
# Example NPC definition structure with defaults
NPC_TYPES = {
    "merchant": {
        "asset_id": "npc.merchant.general",
        "behavior": "store",
        "default_dialog": [
            "Welcome to my shop! I have the finest goods in the land.",
            "Looking for something special? I might have just what you need.",
            "Trade is the lifeblood of any town. What can I get for you?"
        ],
        "default_inventory": ["health_potion", "mana_potion", "bread", "gold_coin", "ruby_gem"]
    },
    "armourer": {
        "asset_id": "npc.armourer.town",
        "behavior": "store",
        "default_dialog": [
            "Quality armor saves lives. Let me outfit you properly.",
            "A warrior is only as good as their protection. What do you need?",
            "I've been crafting armor for decades. Trust my expertise."
        ],
        "default_inventory": ["iron_helmet", "iron_breastplate", "iron_greaves", "iron_boots", "leather_armor", "wooden_shield"]
    },
    "weaponsmith": {
        "asset_id": "npc.weaponsmith.forge",
        "behavior": "store",
        "default_dialog": [
            "Sharp steel and true aim - that's what wins battles.",
            "Every weapon I forge is tested personally. Quality guaranteed.",
            "Looking for a blade? I have the finest steel in the realm."
        ],
        "default_inventory": ["iron_sword", "steel_dagger", "wooden_bow", "battle_axe"]
    },
    "innkeeper": {
        "asset_id": "npc.innkeeper.tavern",
        "behavior": "store",
        "default_dialog": [
            "Welcome to my inn! Food, drink, and a warm bed await.",
            "Travelers need good food and rest. I provide both.",
            "Pull up a chair! What can I get you?"
        ],
        "default_inventory": ["bread", "ale", "cheese", "room_key", "hot_meal"]
    },
    "commoner": {
        "asset_id": "npc.commoner.citizen",
        "behavior": "dialog",
        "default_dialog": [
            "Good day to you, traveler.",
            "The weather's been quite pleasant lately.",
            "I hope you're enjoying your stay in our town.",
            "Safe travels on your journey."
        ]
    },
    "guard": {
        "asset_id": "npc.guard.town",
        "behavior": "dialog",
        "default_dialog": [
            "Keep the peace, citizen.",
            "Nothing suspicious to report today.",
            "The town is secure under our watch.",
            "Move along, nothing to see here."
        ]
    }
}
```

### Map Integration
- Add NPC symbols to `base_legend.yaml` (suggest: M=merchant, A=armourer, W=weaponsmith, I=innkeeper, c=commoner, G=guard)
- Support level-specific NPC configuration in `level_config.py`
- Enable NPC placement in map editor with proper categorization

### Store System
- Create unified store interface that adapts based on NPC type
- Integrate with existing inventory and item systems
- Use default inventories from NPC type definitions
- Support level-specific inventory overrides via `level_config.py`
- Handle buy/sell transactions with player gold system

### Dialog System
- Implement simple dialog display system
- Use default dialog from NPC type definitions
- Support randomized dialog selection from available options
- Support level-specific dialog overrides via `level_config.py`
- Location-aware dialog customization for commoners and guards

## Implementation Guidelines

### Architecture Compliance
- Follow existing Clean Architecture patterns
- Place core NPC logic in `src/game_core/`
- Use cases in `src/application/use_cases.py`
- UI components in `src/presentation/ui/`
- Asset generation in `src/infrastructure/assets/`

### Code Organization
```
src/game_core/npc.py              # Core NPC entities and logic
src/application/npc_use_cases.py  # NPC interaction use cases  
src/presentation/ui/store_ui.py   # Store interface
src/presentation/ui/dialog_ui.py  # Dialog display
src/infrastructure/assets/procedural_npcs.py  # NPC sprite generation
src/game_data/npcs.py            # NPC definitions and dialog
```

### Configuration Structure
```python
# In level_config.py - Override defaults per level
"npc_overrides": {
    # Override inventory for specific NPC types in this level
    "merchant_inventory": ["special_potion", "local_bread", "town_trinket"],
    "armourer_inventory": ["town_guard_helmet", "reinforced_leather", "ceremonial_shield"],
    "weaponsmith_inventory": ["masterwork_sword", "elven_bow", "enchanted_dagger"],
    "innkeeper_inventory": ["local_ale", "hearty_stew", "luxury_room_key"],

    # Override dialog for specific NPC types in this level
    "merchant_dialog": [
        "Welcome to Caledon! Best prices in the kingdom!",
        "I've got rare items from distant lands.",
        "The goblin attacks have been bad for business..."
    ],
    "commoner_dialog": [
        "The goblins have been getting bolder lately.",
        "Mayor says we're safe, but I'm not so sure.",
        "Have you seen the old oak tree? Something's not right about it."
    ],
    "guard_dialog": [
        "Stay alert, citizen. Goblins have been spotted nearby.",
        "We're doubling patrols until this goblin threat is dealt with.",
        "Report any suspicious activity immediately."
    ],

    # Override for specific NPC instances by position or ID
    "specific_npcs": {
        "merchant_at_5_10": {
            "dialog": ["I'm the only merchant brave enough to stay during these troubled times!"],
            "inventory": ["emergency_rations", "escape_rope", "signal_flare"]
        }
    }
}
```

## Deliverables

1. **Core NPC System**: Entity definitions with default behaviors, dialog, and inventories
2. **Store Interface**: Unified shopping UI that uses default or overridden inventories
3. **Dialog System**: Text display with randomization from default or overridden dialog pools
4. **Override System**: Level-specific customization of NPC dialog and inventories
5. **Asset Generation**: Procedural sprites for all NPC types + icons
6. **Map Integration**: Legend updates, editor support, level configuration
7. **Use Cases**: Player-NPC interaction, store transactions, dialog display with override support
8. **Documentation**: Usage examples, default configuration reference, override guide

## Testing Requirements
- Unit tests for NPC behaviors and interactions
- Integration tests with existing player interaction system
- Map editor functionality tests for NPC placement
- Store interface usability tests

## Success Criteria
- All 6 NPC types functional with unique behaviors and default content
- Store interface works for merchant-type NPCs using default or overridden inventories
- Dialog system displays appropriate text with randomization from default or custom pools
- Map editor can place and configure NPCs
- Level designers can easily override default NPC dialog and inventories per level
- Default configurations provide immediate functionality without requiring level-specific setup
- Override system allows for rich customization and location-specific content
- Visual assets are consistent with game's art style
- No breaking changes to existing systems
