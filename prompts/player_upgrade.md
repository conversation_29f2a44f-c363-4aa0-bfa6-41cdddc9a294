# Enhanced Player Asset System Prompt

## Project Overview

Examine the **Treebeard's Revenge** RPG game to understand its Clean Architecture design and procedural asset generation system. This is a ground-up rebuild following strict architectural principles with procedurally generated visual assets.

## Current Player System

The current player implementation:
- Uses asset ID `"player.hero"` with a basic procedural sprite in `get_player_sprite()`
- Has a simple `Player` entity in `src/game_core/entities.py` with basic stats
- Currently appears as a very basic dot on screen
- No equipment system or visual customization

## Task: Enhanced Player Asset System

Create a sophisticated player asset system that supports:

### 1. Equipment System Architecture

**Core Layer Enhancement (`src/game_core/entities.py`):**
- Add equipment slots to the `Player` entity:
  - `head_equipment: Optional[str] = None`
  - `chest_equipment: Optional[str] = None` 
  - `legs_equipment: Optional[str] = None`
  - `boots_equipment: Optional[str] = None`
  - `main_hand_weapon: Optional[str] = None`
  - `off_hand_equipment: Optional[str] = None` (shield or two-handed weapon)
- Add inventory system to the `Player` entity:
  - `inventory: Dict[str, int] = {}` (item_id -> quantity mapping)
  - `inventory_max_size: int = 20` (maximum number of unique item types)
- Keep this layer framework-agnostic (no Pygame imports)

**Game Data Enhancement (`src/game_data/items.py`):**
- Extend existing armor/weapon definitions with equipment slot information
- Add new basic clothing items with no defense bonuses for starting equipment
- Add new equipment pieces as needed
- Include visual properties that affect rendering (colors, styles, etc.)

### 2. Player Inventory System

**Core Layer Enhancement (`src/game_core/entities.py`):**
- Add inventory management methods to the `Player` entity:
```python
def add_item_to_inventory(self, item_id: str, quantity: int = 1) -> "Player":
    """Return new player with item added to inventory."""
    
def remove_item_from_inventory(self, item_id: str, quantity: int = 1) -> "Player":
    """Return new player with item removed from inventory."""
    
def has_item(self, item_id: str, quantity: int = 1) -> bool:
    """Check if player has specified item and quantity."""
    
def get_inventory_space_used(self) -> int:
    """Get number of unique item types in inventory."""
```

**Starting Equipment Setup:**
- Player begins with basic clothing items equipped (no defense bonus)
- Player starts with a rusty sword equipped in main hand
- Player starts with some basic items in inventory

### 3. Starting Equipment Data Definitions

**Add to `src/game_data/items.py`:**

Basic clothing items for starting equipment:
```python
# Basic starting clothing (no defense bonus)
"cloth_shirt": ItemDefinition(
    id="cloth_shirt",
    name="Cloth Shirt",
    asset_id="item.armor.cloth.chest",
    item_type=ItemType.ARMOR,
    value=1,
    description="A simple cloth shirt. Provides modesty but no protection.",
    properties={
        "defense_bonus": 0,
        "armor_type": "clothing",
        "slot": "chest",
        "visual_style": "simple_shirt",
        "color_primary": (240, 240, 240),  # Light gray/white
        "color_secondary": (200, 200, 200)
    }
),

"cloth_pants": ItemDefinition(
    id="cloth_pants",
    name="Cloth Pants", 
    asset_id="item.armor.cloth.legs",
    item_type=ItemType.ARMOR,
    value=1,
    description="Basic cloth trousers. Better than nothing.",
    properties={
        "defense_bonus": 0,
        "armor_type": "clothing",
        "slot": "legs",
        "visual_style": "simple_pants",
        "color_primary": (139, 69, 19),  # Brown
        "color_secondary": (101, 67, 33)
    }
),

"cloth_cap": ItemDefinition(
    id="cloth_cap",
    name="Cloth Cap",
    asset_id="item.armor.cloth.head", 
    item_type=ItemType.ARMOR,
    value=1,
    description="A simple cloth cap to keep the sun out of your eyes.",
    properties={
        "defense_bonus": 0,
        "armor_type": "clothing", 
        "slot": "head",
        "visual_style": "simple_cap",
        "color_primary": (139, 69, 19),  # Brown
        "color_secondary": (101, 67, 33)
    }
),

"simple_shoes": ItemDefinition(
    id="simple_shoes",
    name="Simple Shoes",
    asset_id="item.armor.cloth.boots",
    item_type=ItemType.ARMOR,
    value=2,
    description="Basic leather shoes. They'll protect your feet from rocks.",
    properties={
        "defense_bonus": 0,
        "armor_type": "clothing",
        "slot": "boots", 
        "visual_style": "simple_shoes",
        "color_primary": (101, 67, 33),  # Dark brown
        "color_secondary": (70, 50, 30)
    }
),
```

**Starting Inventory Definition:**
```python
# Default starting equipment for new players
DEFAULT_STARTING_EQUIPMENT = {
    "head_equipment": "cloth_cap",
    "chest_equipment": "cloth_shirt", 
    "legs_equipment": "cloth_pants",
    "boots_equipment": "simple_shoes",
    "main_hand_weapon": "rusty_sword",
    "off_hand_equipment": None
}

DEFAULT_STARTING_INVENTORY = {
    "bread": 3,
    "health_potion": 2,
    "gold_coin": 25
}
```

### 4. Dynamic Asset Generation System

**Infrastructure Enhancement (`src/infrastructure/assets/procedural_items.py`):**

Replace the current basic `get_player_sprite()` with a sophisticated system:

#### A. Base Player Generator
```python
def get_base_player_sprite(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate base human character sprite (naked/underwear)."""
    # Detailed human figure with:
    # - Proper proportions for a classic top down view RPG game
    # - Skin tone, basic hair
    # - Gender-neutral or configurable appearance
    # - Clear attachment points for equipment
```

#### B. Equipment Layer System
```python
def get_equipped_player_sprite(
    equipment_ids: Dict[str, Optional[str]], 
    size: Tuple[int, int] = (32, 32)
) -> pygame.Surface:
    """Generate player sprite with equipment layered on top."""
    # 1. Start with base player sprite
    # 2. Layer equipment in proper order:
    #    - Leg armor/pants
    #    - Boot equipment
    #    - Chest armor/shirt
    #    - Head equipment
    #    - Weapons/shields (drawn in hand positions)
    # 3. Handle weapon types (melee vs ranged positioning)
    # 4. Return composite sprite
```

#### C. Individual Equipment Renderers
Create equipment-specific rendering functions:
```python
def render_equipment_on_player(
    player_surface: pygame.Surface,
    equipment_id: str,
    slot: str,
    size: Tuple[int, int]
) -> pygame.Surface:
    """Render specific equipment piece onto player base."""
    # Handle different equipment types with proper layering
    # Use equipment data from game_data/items.py for colors/style
    # Ensure equipment fits the character properly
```

### 5. Asset ID Strategy

**Registry Enhancement (`src/infrastructure/assets/registry.py`):**
- Replace single `"player.hero"` with dynamic system
- Use composite asset IDs like: `"player.equipped.{hash_of_equipment}"` 
- Or implement runtime asset generation that bypasses the static registry

**Asset Manager Enhancement (`src/infrastructure/assets/asset_manager.py`):**
- Add method to generate composite player assets
- Cache equipment combinations for performance
- Handle equipment changes efficiently

### 6. Equipment Data Definitions

**Add to `src/game_data/items.py`:**

Extend existing items and add new ones:
```python
# Head equipment
"iron_helmet": ItemDefinition(
    # ... existing definition ...
    properties={
        "defense_bonus": 5,
        "armor_type": "heavy", 
        "slot": "head",
        "visual_style": "full_helmet",  # affects rendering
        "color_primary": (120, 120, 120),  # for procedural generation
        "color_secondary": (80, 80, 80)
    }
),

"leather_cap": ItemDefinition(
    id="leather_cap",
    name="Leather Cap",
    asset_id="item.armor.leather.head",
    item_type=ItemType.ARMOR,
    properties={
        "slot": "head",
        "visual_style": "light_cap",
        "color_primary": (139, 69, 19)
    }
),

# Leg equipment
"iron_greaves": ItemDefinition(
    id="iron_greaves", 
    name="Iron Greaves",
    asset_id="item.armor.iron.legs",
    properties={
        "slot": "legs",
        "visual_style": "plate_legs",
        "color_primary": (120, 120, 120)
    }
),

# Boot equipment  
"leather_boots": ItemDefinition(
    id="leather_boots",
    name="Leather Boots", 
    asset_id="item.armor.leather.boots",
    properties={
        "slot": "boots",
        "visual_style": "simple_boots",
        "color_primary": (101, 67, 33)
    }
),

# Shield equipment
"wooden_shield": ItemDefinition(
    id="wooden_shield",
    name="Wooden Shield",
    asset_id="item.armor.shield.wooden", 
    properties={
        "slot": "off_hand",
        "visual_style": "round_shield",
        "color_primary": (139, 69, 19),
        "shield_type": "small"
    }
)
```

### 7. Animation Considerations

***ONLY CHOSE ONE OF THESE***
**Simple Animation Approach:**
- Create 2-frame walking animation (left foot up, then right foot up, etc...)
- Generate equipment combinations for each frame
- Use frame timing in the renderer
- Consider this a stretch goal - static equipment visualization is the priority

**Alternative - Static with Facing:**
- Generate player sprites facing different directions (up, down, left, right)
- Equipment renders appropriately for each facing
- No frame-based animation but better directional representation

### 8. Integration Requirements

**Application Layer (`src/application/use_cases.py`):**
- Add equipment management use cases:
  - `EquipItemUseCase`
  - `UnequipItemUseCase` 
  - `GetPlayerVisualDataUseCase` (returns equipment IDs for rendering)
- Add inventory management use cases:
  - `AddItemToInventoryUseCase`
  - `RemoveItemFromInventoryUseCase`
  - `GetInventoryUseCase`
  - `CanAddItemToInventoryUseCase` (check space limits)

**Player Creation (`src/application/use_cases.py`):**
- Add `CreateNewPlayerUseCase` that sets up starting equipment and inventory:
```python
def create_new_player(name: str, starting_position: Position) -> Player:
    """Create a new player with default starting equipment and inventory."""
    # Set up player with starting equipment equipped
    # Add starting items to inventory
    # Return fully configured player
```

**Rendering Integration (`src/infrastructure/rendering/pygame_renderer.py`):**
- Modify player rendering to use equipment-aware asset generation
- Get player's current equipment and generate appropriate sprite
- Cache equipment combinations to avoid regenerating every frame

### 9. Architecture Compliance

**Ensure the solution maintains Clean Architecture:**
- Core layer remains Pygame-free (equipment slots as simple strings)
- Game data layer contains pure data definitions
- Infrastructure layer handles all visual generation
- Application layer orchestrates equipment changes
- Presentation layer triggers equipment changes through use cases

### 10. Expected Deliverables

1. **Enhanced Player Entity** with equipment slots and inventory system
2. **Starting Equipment Data** including basic clothing items with no defense bonus
3. **Player Creation System** that sets up new players with starting gear and inventory
4. **Extended Equipment Data** with visual properties for all equipment types
5. **Layered Asset Generation System** for composite player sprites
6. **Equipment Rendering Functions** for each armor/weapon type including basic clothing
7. **Inventory Management System** with use cases for adding/removing items
8. **Application Layer Integration** for equipment and inventory management
9. **Updated Asset Registry/Manager** to handle dynamic player assets
10. **Visual Variety** - at least 15+ different equipment combinations should be clearly distinguishable

### 11. Technical Constraints

- **Maintain flexible sprite size** for player sprites should scale with a new config: base_entity_size: 128
- **Performance** - cache composite sprites to avoid regenerating every frame
- **Visual Clarity** - equipment should be clearly visible and distinguishable at small sizes
- **Color Coding** - use the color properties from item definitions for visual variety
- **Weapon Positioning** - melee weapons in hand, ranged weapons positioned appropriately
- **Shield Handling** - shields should appear in off-hand, two-handed weapons override shields
- **Inventory Limits** - enforce maximum inventory size and item stacking rules
- **Starting Balance** - basic clothing provides visual variety but no gameplay advantage

### 12. Success Criteria

- Player character visually changes when equipment is equipped/unequipped
- At least 4 armor slots (head, chest, legs, boots) are visually represented
- Basic starting clothing is visually distinct from higher-tier armor
- Weapons (both melee and ranged) are clearly visible and properly positioned
- Player starts with appropriate beginner equipment (rusty sword + basic clothes)
- Inventory system allows collecting and managing items with proper limits
- Equipment combinations create visually distinct character appearances
- System integrates seamlessly with existing Clean Architecture
- Performance remains smooth (cached asset generation)

This enhancement should transform the player from a basic dot into a detailed, customizable character with a full inventory system that reflects their equipment choices while maintaining the game's architectural principles and procedural asset generation approach. The starting equipment provides a good baseline for players to improve upon as they progress through the game.
