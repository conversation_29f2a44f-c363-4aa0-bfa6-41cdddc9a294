Write a prompt I can give to an AI to build the following:

prompts/npc-init-ai.md

We need to revamp the NPC system. There should be multiple types of NPCs including merchants, armourers, weaponsmiths, innkeepers, commoners and guards.

Each of these will require a unique map asset. 

When interacted with (Player collides with) Merchants, armourers, weaponsmiths should open a standard store interface. The level_config.py file for each level should define which items are available for purchase, but merchants should generally sell goods, armourers should sell armor, and weaponsmiths should sell weapons.

Innkeepers should sell food and lodging.

Commoners should have a random dialog that is related to the town they are in.

Guards should have random dialog that is related to their job.

the map editor will also need icons for each of these and allow placement on the map.