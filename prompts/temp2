Of course. This is an excellent refinement that makes the system more powerful and intuitive for level designers. It establishes a clear hierarchy for game data (base -> level-specific) and simplifies the map file's structure.

Here is the revised Section 9, incorporating your feedback.

---

### **9. (Revised) Map Definition and Loading**

Level layouts will be defined in human-readable text files. This system is designed around a "base-plus-override" model, allowing for both global consistency and level-specific uniqueness.

#### **A. The Data Hierarchy: Base Legend and Level Overrides**

1.  **Base Legend (`src/game_data/base_legend.yaml`):**
    A global YAML file that defines all common tiles and entities. This is the game's default "palette." It ensures consistency across all levels.

    **Example: `src/game_data/base_legend.yaml`**
    ```yaml
    # Defines the default meaning of symbols for the entire game.
    '#':
      category: tile
      type: wall_stone
      solid: true
      asset_id: "tile.wall.stone"
    '.':
      category: tile
      type: floor_dirt
      solid: false
      asset_id: "tile.floor.dirt"
    '~':
      category: tile
      type: water
      solid: false
      movement_modifier: 0.5 # Player moves at half speed
      asset_id: "tile.water.animated"
    'g':
      category: entity
      data_id: "goblin_grunt" # Links to src/game_data/monsters.py
    ```

2.  **Level-Specific `.map` File:**
    Each map file's YAML header can **override** base definitions or **append** new ones. Entities are defined in the legend and placed directly in the ASCII grid, making the map a true "what you see is what you get" (WYSIWYG) layout.

#### **B. The `.map` File Format (v2)**

*   **YAML Header:** Contains a `legend` that merges with the base legend. It can also contain an optional `placements` list for defining unique, one-off entities that override the legend for a specific coordinate.
*   **ASCII Body:** The visual grid. Every character corresponds to an entry in the merged legend.

**Example: `src/levels/desert_oasis/oasis.map`**
```yaml
# METADATA: This level's legend overrides and additions.
legend:
  # --- OVERRIDE ---
  # In this desert level, '.' is sand, not dirt.
  '.':
    category: tile
    type: floor_sand
    solid: false
    movement_modifier: 0.8 # Slightly slower
    asset_id: "tile.floor.sand"

  # --- APPEND ---
  # 'C' is a new symbol, unique to desert levels.
  'C':
    category: tile
    type: cactus
    solid: true
    effect_on_touch: # Damage if player bumps into it
      type: damage
      amount: 3
    asset_id: "tile.cactus"

  # --- APPEND (Unique Monster) ---
  'S':
    category: entity
    data_id: "sand_scorpion"

# PLACEMENTS: For unique entities that need special properties.
placements:
  - char: 'S'
    position: { x: 8, y: 5 }
    overrides:
      name: "Skarth, the Dune-Stalker"
      max_hp: 150

---
# MAP LAYOUT: Uses symbols from the merged legend.

~~~~~~~~~~~
~.........~
~..C.S.C..~
~.........~
~....g....~  # This 'g' is a standard goblin from base_legend.
~.........~
~~~~~~~~~~~
```

#### **C. Architectural Implementation**

**1. Parsing & Merging (`Infrastructure` Layer):**
The `MapParser` is now responsible for a multi-step loading process:
1.  **Load Base:** It first reads and parses `src/game_data/base_legend.yaml` into a dictionary.
2.  **Load Level:** It then reads the `.map` file's YAML header.
3.  **Merge Legends:** It merges the level's legend into the base legend (`level_legend` overwrites `base_legend` on any key conflicts). The result is the final, definitive legend for this specific map.
4.  **Parse Grid:** It processes the ASCII grid and reads the optional `placements` list.
5.  **Output:** It outputs a single, clean `LevelLayoutData` object containing the final merged legend, the placements list, and the grid.

**2. Data Modeling (`Application` Layer):**
The data models will be updated to reflect this structure.

```python
# In src/application/data_models.py
# ... (Position model is the same) ...

class LegendEntry(BaseModel):
    category: str # "tile" or "entity"
    # ... other common fields

class TileLegendEntry(LegendEntry):
    type: str
    solid: bool
    asset_id: str
    movement_modifier: float = 1.0
    effect_on_enter: Dict[str, Any] | None = None
    effect_on_touch: Dict[str, Any] | None = None

class EntityLegendEntry(LegendEntry):
    data_id: str

class UniquePlacement(BaseModel):
    char: str
    position: Position
    overrides: Dict[str, Any] = {}

class LevelLayoutData(BaseModel):
    # The final, merged legend
    legend: Dict[str, TileLegendEntry | EntityLegendEntry]
    placements: List[UniquePlacement]
    grid: List[str]
```

**3. Level Construction (`Application` Layer):**
The `BuildLevelUseCase` uses the `LevelLayoutData` to build the game world. Its logic is now:
1.  Create a lookup for unique placements, e.g., a dictionary mapping `(x, y)` to a `UniquePlacement` object.
2.  Iterate through the `grid` row by row (`y`) and character by character (`x`). For each `(x, y, char)`:
    *   **Check for Unique Placement:** Is the current `(x, y)` in the unique placement lookup and does the character match?
        *   If **YES**: Spawn an entity using the `data_id` from the legend's entry for `char`, but apply the `overrides` from the `UniquePlacement` object. This creates our named boss, "Skarth".
    *   **Use Legend Definition:**
        *   If **NO**: Look up `char` in the final, merged `legend`.
        *   If the `category` is `tile`, create a tile entity at `(x, y)` with the specified properties (collision, asset, effects).
        *   If the `category` is `entity`, spawn a standard entity using its `data_id` at `(x, y)`. This creates the generic goblin.
3.  After iterating, emit the `LevelReadyEvent`.

This refined approach provides a powerful, layered system for level design that is both easy for designers to use and robustly integrated into the game's clean architecture.
