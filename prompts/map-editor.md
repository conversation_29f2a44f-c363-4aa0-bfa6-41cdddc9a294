Write a prompt prompts/map-editor-ai.md I can give to an AI to build the following:
I want a graphical map editor that uses the same assets, legend and displays the same config as the game.

This should read  a map file as defined by this game. Look at init.md to understand the format of the map file.

The editor should be able to scaled up or down by zooming the mouse.

There should be a panel in the left side showing all available tiles / entities. The user can drag and drop these into the map, or select them and paint them with a brush tool.

The panel should allow you to filter by category (tile, entity, etc.)

there should be a search bar to filter the tiles / entities.

Holding the mouse button down and moving it should paint a line of the selected tile / entity.

Create this prompt an prompts/map-editor-ai.md

At the end of the prompt, list questions you might have, or clarification that might be needed.

We should be able to run the editor `run.sh editor`

The editor should allow the user to create/update .map files in the src/levels/ directory.