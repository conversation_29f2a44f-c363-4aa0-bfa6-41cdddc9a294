# Implementing Player Attack Animations in <PERSON><PERSON>ard's Revenge

## Overview
Implement a visual attack animation system for the player character that triggers when the left mouse button is clicked. The animation should vary based on the equipped weapon type (melee vs ranged) and provide visual feedback for the attack action.

## Current System Analysis
The game already has:
- **Mouse-based attacking**: Left click triggers `PlayerAttackUseCase.execute()` with direction vector
- **Direction calculation**: `screen_to_direction_vector()` converts mouse position to normalized attack direction
- **Equipment system**: Player has `main_hand_weapon` slot with weapon definitions in `src/game_data/items.py`
- **Weapon types**: Items have `weapon_type` property (sword, axe, bow, dagger)
- **Player rendering**: Dynamic sprite generation with equipment layering in `src/infrastructure/assets/procedural_items.py`
- **Event system**: `PlayerAttackedEvent` is published when attacks occur

## Requirements

### 1. Animation State Management
Create a player animation state system that tracks:
- Current animation type (idle, attacking, moving)
- Animation progress/frame
- Animation duration
- Weapon-specific animation parameters

### 2. Weapon-Specific Animations

#### Melee Weapons (sword, axe, dagger)
- **Swing animation**: Player sprite rotates or tilts to simulate weapon swing
- **Duration**: 200-400ms based on weapon `attack_speed` property
- **Visual effect**: Slight rotation of entire player sprite (10-15 degrees) in attack direction
- **Recovery**: Return to idle position smoothly

#### Ranged Weapons (bow)
- **Vibration effect**: Small rapid oscillation of player sprite
- **Duration**: 150-250ms
- **Visual effect**: 2-3 pixel horizontal/vertical shake
- **Recovery**: Return to stable position

### 3. Implementation Approach

#### Step 1: Animation State System
Create `src/game_core/animation.py`:
```python
@dataclass
class PlayerAnimationState:
    animation_type: str = "idle"  # idle, attacking, moving
    start_time: float = 0.0
    duration: float = 0.0
    weapon_type: Optional[str] = None
    attack_direction: Optional[Vector2] = None
```

#### Step 2: Animation Manager
Create `src/application/animation_manager.py`:
- `start_attack_animation(weapon_type, direction, attack_speed)`
- `update_animation(dt)` - called each frame
- `get_current_animation_transform()` - returns rotation/offset for rendering
- `is_animation_active()` - check if animation is playing

#### Step 3: Integration Points

**Game Engine Integration** (`src/presentation/game_engine.py`):
- Add animation manager to game engine
- Start attack animation when `PlayerAttackedEvent` is published
- Update animation state each frame in `_update()`

**Renderer Integration** (`src/infrastructure/rendering/pygame_renderer.py`):
- Modify player rendering to apply animation transforms
- Apply rotation/offset to player sprite before blitting
- Ensure animation doesn't interfere with equipment rendering

**Event Handling**:
- Subscribe to `PlayerAttackedEvent` to trigger animations
- Extract weapon type from player's equipped `main_hand_weapon`
- Use weapon's `attack_speed` property to calculate animation duration

### 4. Technical Details

#### Animation Calculations
```python
# Melee swing calculation
def calculate_swing_rotation(progress: float, direction: Vector2) -> float:
    # progress: 0.0 to 1.0
    # Use sine wave for smooth swing motion
    swing_angle = math.sin(progress * math.pi) * 15  # 15 degree max swing
    # Apply direction to determine swing direction
    return swing_angle * direction.x  # Swing left/right based on attack direction

# Ranged vibration calculation  
def calculate_vibration_offset(progress: float) -> Tuple[float, float]:
    # High frequency oscillation
    frequency = 20  # Hz
    amplitude = 2   # pixels
    offset_x = math.sin(progress * frequency * 2 * math.pi) * amplitude
    offset_y = math.cos(progress * frequency * 2 * math.pi) * amplitude * 0.5
    return (offset_x, offset_y)
```

#### Weapon Type Detection
```python
def get_weapon_animation_type(player: Player) -> str:
    if not player.main_hand_weapon:
        return "unarmed"
    
    weapon_def = get_item_definition(player.main_hand_weapon)
    if not weapon_def:
        return "unarmed"
    
    weapon_type = weapon_def.properties.get("weapon_type", "sword")
    
    # Map weapon types to animation categories
    melee_weapons = ["sword", "axe", "dagger"]
    ranged_weapons = ["bow"]
    
    if weapon_type in melee_weapons:
        return "melee"
    elif weapon_type in ranged_weapons:
        return "ranged"
    else:
        return "melee"  # Default fallback
```

### 5. Configuration
Add animation settings to `game_config.yaml`:
```yaml
animations:
  attack_base_duration: 300  # milliseconds
  melee_swing_angle: 15      # degrees
  ranged_vibration_amplitude: 2  # pixels
  ranged_vibration_frequency: 20 # Hz
```

### 6. Testing Strategy
- Create unit tests for animation calculations
- Test with different weapon types (sword, bow, axe)
- Verify animations don't interfere with movement
- Test animation timing with different `attack_speed` values
- Ensure animations work with equipment rendering system

### 7. Future Enhancements
- Add weapon trail effects for melee weapons
- Implement directional attack animations (up/down/left/right swings)
- Add impact effects when attacks hit targets
- Consider adding weapon-specific sound effects timing

## Key Files to Modify
1. `src/game_core/animation.py` (new)
2. `src/application/animation_manager.py` (new) 
3. `src/presentation/game_engine.py` (integrate animation manager)
4. `src/infrastructure/rendering/pygame_renderer.py` (apply transforms)
5. `game_config.yaml` (add animation settings)
6. Tests for animation system

## Success Criteria
- Left click triggers appropriate weapon animation
- Melee weapons show swing motion approriate to weapon type
- Ranged weapons show vibration effect
- Animations respect weapon attack speed
- No interference with existing systems (movement, equipment, UI)
- Smooth animation transitions and timing
