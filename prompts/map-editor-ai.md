# Map Editor Development Prompt

## Overview
Build a graphical map editor for <PERSON><PERSON><PERSON>'s Revenge that integrates seamlessly with the existing game architecture. The editor should use the same asset system, legend definitions, and visual styling as the main game while providing an intuitive interface for creating and editing .map files.

## Technical Requirements

### Integration with Existing Systems
- **Asset System**: Use the existing `AssetManager` and procedural asset generation from `src/infrastructure/assets/`
- **Map Format**: Read/write .map files using the existing `MapParser` from `src/infrastructure/repositories/map_parser.py`
- **Base Legend**: Load symbols from `src/game_data/base_legend.yaml` 
- **Configuration**: Use settings from `game_config.yaml` for tile sizes, rendering, etc.
- **Launch Method**: Integrate with `run.sh` so users can run `./run.sh editor`

### Map File Format Support
The editor must handle the complete .map file format:

```yaml
# YAML Header
legend:
  # Level-specific overrides and additions
  'H': { category: entity, type: building, data_id: "house" }
  
placements:
  # Unique entities with special properties
  - char: 'g'
    position: { x: 15, y: 8 }
    overrides: { name: "Boss Goblin", max_hp: 100 }

metadata:
  name: "Level Name"
  description: "Level description"
  
---
# ASCII Body (visual grid)
######################
#..................H.#
#..@...............#.#
######################
```

### Core Features

#### 1. Main Editor Interface
- **Canvas Area**: Scrollable/pannable map view with zoom support (mouse wheel)
- **Grid Display**: Show tile boundaries clearly
- **Coordinate Display**: Show current mouse position in tile coordinates
- **Status Bar**: Display current tool, selected tile/entity, map dimensions

#### 2. Left Panel - Asset Browser
- **Category Filtering**: Tabs or dropdown for "Tiles", "Entities", "All"
- **Search Bar**: Real-time filtering of available symbols
- **Asset Grid**: Visual grid showing all available tiles/entities with their symbols
- **Asset Info**: Show asset_id, category, and properties when hovering/selecting

#### 3. Editing Tools
- **Selection Tool**: Click to select tiles/entities
- **Paint Brush**: Click or drag to paint selected asset
- **Line Tool**: Hold mouse and drag to paint straight lines
- **Fill Tool**: Flood fill areas with selected asset
- **Eraser**: Remove tiles/entities (replace with default floor)

#### 4. Map Management
- **New Map**: Create new .map file with configurable dimensions
- **Open Map**: Load existing .map files from `src/levels/` directory
- **Save Map**: Save changes to .map file
- **Save As**: Save to new filename/location

#### 5. Advanced Features
- **Layer Support**: Toggle between tile layer and entity layer
- **Undo/Redo**: Track editing history
- **Copy/Paste**: Select and duplicate map sections
- **Map Properties**: Edit metadata (name, description, etc.)

### Technical Implementation

#### Architecture Integration
- Extend the existing dependency injection pattern
- Use the same event bus system for editor actions
- Leverage existing `LevelRepository` for file operations
- Reuse `PygameRenderer` components where possible

#### Asset Loading
```python
# Use existing asset system
asset_manager = AssetManager()
base_legend = load_base_legend("src/game_data/base_legend.yaml")

# Get all available assets
available_assets = {}
for symbol, definition in base_legend.items():
    asset_id = definition.get('asset_id')
    if asset_id:
        surface = asset_manager.get_asset(asset_id)
        available_assets[symbol] = {
            'surface': surface,
            'definition': definition
        }
```

#### Map File Operations
```python
# Use existing MapParser
map_parser = MapParser("src/game_data/base_legend.yaml")

# Load map
layout_data = map_parser.parse_map_file("src/levels/town/map.map")

# Save map (implement save functionality in MapParser)
map_parser.save_map_file("src/levels/new_level/map.map", layout_data)
```

### User Interface Design

#### Layout
```
┌─────────────────────────────────────────────────────────────┐
│ File  Edit  View  Tools                              Help   │
├─────────────────────────────────────────────────────────────┤
│ [New] [Open] [Save] │ [Select] [Paint] [Line] [Fill] [Erase] │
├─────────────┬───────────────────────────────────────────────┤
│ Asset Panel │                                               │
│ ┌─────────┐ │                                               │
│ │[Tiles]  │ │                                               │
│ │Entities │ │                                               │
│ │  All    │ │                                               │
│ └─────────┘ │                                               │
│ [Search___] │              Map Canvas                       │
│             │         (Scrollable/Zoomable)                 │
│ ┌─────────┐ │                                               │
│ │ # Wall  │ │                                               │
│ │ . Floor │ │                                               │
│ │ @ Player│ │                                               │
│ │ g Goblin│ │                                               │
│ └─────────┘ │                                               │
├─────────────┴───────────────────────────────────────────────┤
│ Tool: Paint │ Selected: # (Wall) │ Pos: (12,8) │ 32x24 tiles│
└─────────────────────────────────────────────────────────────┘
```

#### Controls
- **Mouse Wheel**: Zoom in/out
- **Middle Mouse + Drag**: Pan the map
- **Left Click**: Use current tool
- **Right Click**: Context menu or quick erase
- **Ctrl+Z/Y**: Undo/Redo
- **Ctrl+S**: Save
- **Ctrl+O**: Open
- **Ctrl+N**: New map

### File Structure
```
src/
├── editor/
│   ├── __init__.py
│   ├── main.py              # Editor entry point
│   ├── editor_engine.py     # Main editor class
│   ├── ui/
│   │   ├── asset_panel.py   # Left panel with assets
│   │   ├── canvas.py        # Map editing canvas
│   │   ├── toolbar.py       # Tool selection
│   │   └── dialogs.py       # New/Open/Save dialogs
│   ├── tools/
│   │   ├── base_tool.py     # Abstract tool class
│   │   ├── paint_tool.py    # Painting functionality
│   │   ├── line_tool.py     # Line drawing
│   │   └── fill_tool.py     # Flood fill
│   └── map_operations.py    # Map file I/O operations
```

### Integration with run.sh
Add to the existing `run.sh` script:

```bash
editor)
    echo "Starting map editor..."
    ensure_venv
    cd src && uv run python -m editor.main
    ;;
```

## Implementation Guidelines

### Map File Operations
- Extend the existing `MapParser` class to include save functionality (simpler and more future-proof)
- Each level directory will contain exactly one .map file
- Use only existing asset categories ("tile" and "entity") - no custom asset definitions needed

### Simplified Scope
Focus on core functionality for the initial version:
- Basic paint brush tool for placing tiles/entities
- Asset browser with existing symbols from base legend
- New/Open/Save operations for .map files
- Simple zoom and pan controls

Advanced features like undo/redo, copy/paste, layers, validation, and performance optimizations can be added later as needed.
