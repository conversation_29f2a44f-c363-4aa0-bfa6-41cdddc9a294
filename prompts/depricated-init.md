
### **Prompt: Style Guideline and Architectural Blueprint for the RPG Rebuild**

**Project Goal:** To build an old-school Nintendo-style RPG framework from the ground up, focusing on creating a modern, maintainable, testable, and extensible codebase. The previous version suffered from extreme coupling and a monolithic structure, making it impossible to maintain or expand. This new version will adhere strictly to the following principles.

### **1. Core Architectural Philosophy: Clean Architecture & Dependency Injection**

The game will be built using a **Clean Architecture** approach. This separates the code into distinct layers, ensuring that business logic is independent of frameworks and external details like databases or UI.

**The Golden Rule:** Dependencies must only point inwards, from outer layers to inner layers. The `Core` must know nothing about `Infrastructure` or `Presentation`.

**Layers:**

*   **`src/game_core/` (The Core/Domain):**
    *   **Contents:** Pure Python business logic. This includes game rules, entity definitions (Player, Monster, Item), core mechanics (combat calculations, status effects), and value objects (Position, Stats).
    *   **Rules:**
        *   **NO** external dependencies like Pygame, file I/O libraries, or UI toolkits.
        *   Contains the essential data structures and logic that define the game itself.
        *   Fully testable without any graphics or windowing system.
    *   **Base Game config:**
        *   global config values that are likely to exist in multiple or all levels. The levels should have a way of overriding this though.

*   **`src/application/` (The Application Layer):**
    *   **Contents:** Use cases and orchestration. This layer coordinates the flow of data between the Core and the outer layers. Example: a `MovePlayerUseCase` that takes input coordinates, uses the `game_core` to validate the move, updates the player's state, and returns the result.
    *   **Rules:**
        *   Depends on the `Core` layer.
        *   Does not implement logic, but directs it.
        *   Defines interfaces (Abstract Base Classes) that the `Infrastructure` layer will implement (e.g., `ILevelRepository`, `IEventBus`).

*   **`src/infrastructure/` (The Infrastructure Layer):**
    *   **Contents:** Implementations of external concerns. This is where Pygame, file saving/loading, audio playback, and other third-party libraries live.
    *   **Rules:**
        *   Implements the interfaces defined in the `Application` layer.
        *   Handles all interactions with the outside world (rendering, input, persistence).
        *   Is completely replaceable. We could swap Pygame for Godot by changing only this layer.

*   **`src/presentation/` (The Presentation/UI Layer):**
    *   **Contents:** The main game loop, UI rendering, and input handling.
    *   **Rules:**
        *   Drives the application by calling use cases in the `Application` layer in response to user input or game events.
        *   Renders the game state provided by the `Application` layer.

---

### **2. Game Content Structure: The Level Plugin System**

To eliminate the massive, centralized `config.py` and hardcoded level logic, all level specific content will be organized into self-contained **Level Plugins**.

**Principle:** Each game area (a town, a dungeon, a world map) is a self-contained Python package that the main game engine can discover and load dynamically.

**Canonical Level Structure:**

A new level, such as "Caledon Town," will be created with the following directory structure:

```
src/levels/town_caledon/
├── __init__.py        # Makes it a package
├── level_config.py    # Metadata: level name, music track, connections to other levels.
├── entities.py        # Definitions for NPCs, monsters, and items specific to this level.
├── events.py          # Event handlers for level-specific quests and interactions.
├── map_data.py        # Tilemap data, collision maps, and entity spawn points.
└── tests/             # Unit tests for this level's specific logic and events.
```

---

### **3. Communication and Logic Flow: Event-Driven Architecture**

To decouple components, the game will use an **event-driven architecture**. Instead of objects calling each other's methods directly, they will emit events. Other systems will listen for and react to these events.

**Example Flow:**

1.  **Old Way (Forbidden):** The `Player` object directly calls `game.ui.show_dialogue("Hello!")`. This creates a hard dependency on the UI.
2.  **New Way (Required):**
    *   An `InteractionSystem` detects the player is near an NPC.
    *   It emits an `NpcInteractionEvent(npc_id="villager_1", dialogue_key="greeting")`.
    *   A `DialogueSystem` (part of the Application/Infrastructure layer) listens for `NpcInteractionEvent`.
    *   Upon receiving the event, the `DialogueSystem` looks up the dialogue and instructs the UI to display it.

This ensures the core game logic (detecting an interaction) is completely separate from the implementation (displaying a dialogue box).

---

### **4. Development Methodology: Test-Driven Development (TDD)**

The project will be developed using a **TDD workflow**. This is non-negotiable for ensuring the stability and correctness of the game's logic. Test should be build using pytest, and mocking should be done with pytest-mock.

*   **Unit Tests:**
    *   Target: The `game_core` and `levels/*/` logic.
    *   Characteristics: Fast, run in memory, have zero Pygame or I/O dependencies.
    *   Goal: Verify that game rules and mechanics work as expected in isolation.

*   **Integration Tests:**
    *   Target: The interaction between layers (e.g., does the `Application` layer correctly use the `Infrastructure` implementation to save a game?).

*   **Test Coverage:**
    *   A minimum of **80% test coverage** is required for the `game_core` and `application` layers.

---

### **5. Code Quality and Standards: Modern Python**

*   **Type Hinting:** All new code **must** be fully type-hinted and pass static analysis with `mypy`.
*   **Data Structures:** Use `dataclasses` or `Pydantic` models for all structured data (e.g., `PlayerStats`, `ItemData`). This provides clarity, type safety, and auto-validation.
*   **Dependency Management:** The project will use `uv` for fast and reliable dependency management and virtual environment creation.
*   **Package Structure:** Adhere to the standard `src/` layout for clean separation between source code and project root files.

---

### **6. Developer Experience (DX) and Workflow**

The project must be easy to set up and contribute to.

*   **Setup Script:** A simple `./setup.sh` script will create the virtual environment with `uv` and install all dependencies.
*   **Game runner Script:** A simple `./run.sh` script will run setup.sh if needed, then execute the game in the uv environment.
*   **Test Runner:** A command like `./run_tests.sh` will execute the entire test suite, a specific test, a group of tests. Tests should be build using pytest.
*   **Documentation:** New plugins, systems, and complex mechanics must be documented clearly.

---

### **7. Shared Game Data & Procedural Asset Generation**

This section addresses the need for shared entities (monsters, items) and their procedurally generated assets. The core principle is the **strict separation of data definition from asset implementation**.

#### **A. Shared Data Definitions (`src/game_data/`)**

A new top-level directory, `src/game_data/`, will store framework-agnostic definitions for all shared entities. This is our "game database" in pure Python.

*   **Contents:** Pydantic or dataclass models defining the properties of monsters, items, weapons, armor, etc.
*   **The Link:** Each definition will include a unique string identifier, `asset_id`, which links the data to its visual representation. This `asset_id` is the *only* thing the data knows about the asset.

**Example: `src/game_data/monsters.py`**
```python
# This file has NO Pygame imports. It is pure data.
from pydantic import BaseModel

class MonsterDefinition(BaseModel):
    id: str         # e.g., "green_drake"
    name: str       # "Green Drake"
    asset_id: str   # The crucial link, e.g., "monster.drake.green.side"
    max_hp: int
    attack_power: int
    # ... other game stats

MONSTERS = {
    "green_drake": MonsterDefinition(
        id="green_drake",
        name="Green Drake",
        asset_id="monster.drake.green.side",
        max_hp=50,
        attack_power=12
    ),
    # ... other monsters
}
```

#### **B. The Asset Pipeline (`src/infrastructure/assets/`)**

All code related to generating or loading visual assets belongs exclusively in the `Infrastructure` layer. This is where Pygame is used.

An **Asset Registry** will map the `asset_id` from the data definitions to the actual generation function.

**Example: `src/infrastructure/assets/procedural_monsters.py`**
```python
# This file USES Pygame heavily.
import pygame
import config # For global constants like SPRITE_SIZE

def get_green_drake_sprite(size=config.SPRITE_SIZE):
    # ... all 50+ lines of your Pygame drawing code for the drake go here ...
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    # ... drawing logic ...
    return sprite_surface

def get_red_drake_sprite(size=config.SPRITE_SIZE):
    # ... similar drawing code, but with red colors ...
    return sprite_surface
```

**Example: `src/infrastructure/assets/registry.py`**
```python
# This file connects the IDs to the functions.
from . import procedural_monsters

# The registry maps the string ID to the callable function.
ASSET_GENERATORS = {
    "monster.drake.green.side": procedural_monsters.get_green_drake_sprite,
    "monster.drake.red.side": procedural_monsters.get_red_drake_sprite,
    # ... other asset IDs for items, effects, etc.
}

class AssetManager:
    def __init__(self):
        self.cache = {} # Cache generated surfaces for performance

    def get_surface(self, asset_id: str, size=None):
        if asset_id in self.cache:
            return self.cache[asset_id]

        generator = ASSET_GENERATORS.get(asset_id)
        if not generator:
            # Return a fallback "missing texture" sprite
            raise ValueError(f"No asset generator for ID: {asset_id}")

        surface = generator(size) if size else generator()
        self.cache[asset_id] = surface
        return surface
```

#### **C. Putting It All Together: The Workflow**

1.  **Spawning:** The game logic (in `Application` or `Core`) needs to spawn a monster. It only deals with the data ID: `spawn_monster("green_drake", position=(10, 15))`.
2.  **Data Lookup:** The `MonsterSystem` gets the `MonsterDefinition` from `game_data.monsters.MONSTERS["green_drake"]`. It now knows the monster's stats and its `asset_id`: `"monster.drake.green.side"`.
3.  **Rendering Request:** The entity is created. The `RenderingSystem` (in `Infrastructure`) sees an entity at `(10, 15)` that has the `asset_id` `"monster.drake.green.side"`.
4.  **Asset Retrieval:** The `RenderingSystem` asks the `AssetManager` for the surface associated with that ID.
5.  **Generation & Caching:** The `AssetManager` looks up the ID in its `ASSET_GENERATORS` registry, calls the `get_green_drake_sprite` function, caches the resulting `pygame.Surface`, and returns it.
6.  **Drawing:** The `RenderingSystem` receives the surface and draws (`blit`) it to the screen at the correct coordinates.

**Key Benefits of This Approach:**
*   **Purity Maintained:** The `game_core` and `game_data` remain 100% free of Pygame, making them easy to unit test.
*   **Centralized Assets:** All asset generation logic is in one place (`infrastructure/assets`), not scattered across the codebase.
*   **Flexibility:** We could easily change an asset's appearance by modifying its generation function without touching any game logic. We could even swap procedural generation for loading a PNG by changing one line in the `AssetRegistry`—the rest of the game wouldn't know the difference.


### **9. Map Definition and Loading**

Level layouts will be defined in human-readable text files, separating the visual map design from the game engine's implementation. This allows for rapid iteration by level designers without touching Python code.

#### **A. The `.map` File Format**

Each level's layout will be contained in a single `.map` file (e.g., `dungeon_level_1.map`). This file consists of two parts, separated by a `---` line.

1.  **YAML Header (Metadata):** Defines the "legend" for the map tiles and specifies the exact placement and properties of unique entities like NPCs, monsters, and key items.
2.  **ASCII Body (Layout):** A simple character grid that provides a visual representation of the level's structure using the symbols defined in the header's legend.

**Example: `src/levels/dungeon_of_dread/dungeon_level_1.map`**
```yaml
# METADATA SECTION (YAML)
# Defines the tile legend and entity placements.

legend:
  '#':
    type: wall
    solid: true
  '.':
    type: floor_stone
    solid: false
  '~':
    type: water
    solid: false
    effect_on_enter: # Can trigger simple events
      type: damage
      amount: 5

entities:
  - id: player_start # A special tag for the engine
    char: '@'
  - id: goblin_warrior_1
    char: 'g'
    data_id: "goblin_warrior" # Links to src/game_data/monsters.py
    overrides: # Optional: change stats for this specific instance
      name: "Griznak the Guard"
      position: { x: 8, y: 5 } # Override a specific instance of this entity, or create one at this point
  - id: chest_1
    char: 'C'
    data_id: "chest_wooden" # Links to src/game_data/containers.py
    loot_table: "dungeon_common"

---
# MAP LAYOUT SECTION (ASCII GRID)

###############
#...........#
#.@.......C.#
#...........#
#.....#####.###
#.....#...g...#
#.....#.......#
#..~~~........#
###############
```

#### **B. Architectural Implementation**

The process of turning a `.map` file into a playable level will strictly follow the architectural layers.

**1. Parsing (`Infrastructure` Layer):**
*   A `MapParser` class will be created in `src/infrastructure/parsing/`.
*   Its sole responsibility is to read a `.map` file, split it at the `---` separator, parse the YAML header, and process the ASCII grid.
*   **Crucially, its output will be pure, framework-agnostic data objects (Pydantic models), not live game objects.**

**2. Data Modeling (`Application` Layer):**
The `MapParser` will populate a set of well-defined Pydantic models that represent the parsed data. These models act as the contract between the parser and the rest of the game.

```python
# In src/application/data_models.py

from pydantic import BaseModel
from typing import List, Dict, Any

class Position(BaseModel):
    x: int
    y: int

class TileDefinition(BaseModel):
    type: str
    solid: bool
    effect_on_enter: Dict[str, Any] | None = None

class EntityPlacement(BaseModel):
    id: str
    char: str
    position: Position
    data_id: str | None = None
    overrides: Dict[str, Any] = {}

class LevelLayoutData(BaseModel):
    legend: Dict[str, TileDefinition]
    entities: List[EntityPlacement]
    grid: List[str] # The raw ASCII grid
```

**3. Level Construction (`Application` Layer):**
*   A `BuildLevelUseCase` will be responsible for constructing the game state from the parsed data.
*   **Input:** It takes a `LevelLayoutData` object.
*   **Process:**
    1.  **Create Tile Grid:** It iterates through the `grid`. For each character at each coordinate, it looks up the symbol in the `legend` to get its `TileDefinition` and creates a `Tile` entity in the core game state with the appropriate properties (e.g., collision, type).
    2.  **Spawn Entities:** It iterates through the `entities` list. For each `EntityPlacement`:
        *   It uses the `data_id` to look up the full base definition from `src/game_data/` (e.g., finding the base "goblin_warrior" stats).
        *   It spawns the entity at the specified `position`.
        *   It applies any `overrides` to create a unique instance (e.g., giving Griznak a custom name).
    3.  **Signal Completion:** Once the level is fully constructed in memory, it emits a `LevelReadyEvent(level_id)` to notify other systems (like rendering and AI) that they can begin their work.