"""
Application Layer Interfaces

This module defines the abstract interfaces that the outer layers must implement.
This allows the application layer to depend on abstractions, not concretions.
"""

from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any, Callable
from pydantic import BaseModel

from src.game_core import GameEvent, Player, Monster, Item, NPC, Position


class GameStateData(BaseModel):
    """Data transfer object for the current game state."""
    player: Optional[Player] = None
    monsters: Dict[str, Monster] = {}
    items: Dict[str, Item] = {}
    npcs: Dict[str, NPC] = {}
    current_level_id: Optional[str] = None
    collision_map: List[List[bool]] = []
    level_tiles: List[List[str]] = []  # Tile asset IDs for rendering
    tile_states: Dict[str, Dict[str, Any]] = {}  # Tile states by position "x,y" -> properties
    quest_data: Dict[str, Any] = {}  # Quest state data for persistence


class LevelLayoutData(BaseModel):
    """Data transfer object for level layout information."""
    width: int
    height: int
    tiles: List[List[str]]  # 2D array of tile asset_ids
    entities: List[Dict[str, Any]]  # List of entity definitions
    collision_map: List[List[bool]]
    metadata: Dict[str, Any] = {}


class IEventBus(ABC):
    """Interface for the event bus system."""
    
    @abstractmethod
    def publish(self, event: GameEvent) -> None:
        """Publish an event to all subscribers."""
        pass
    
    @abstractmethod
    def subscribe(self, event_type: str, handler: Callable[[GameEvent], None]) -> None:
        """Subscribe to events of a specific type."""
        pass
    
    @abstractmethod
    def unsubscribe(self, event_type: str, handler: Callable[[GameEvent], None]) -> None:
        """Unsubscribe from events of a specific type."""
        pass


class ILevelRepository(ABC):
    """Interface for loading and saving level data."""
    
    @abstractmethod
    def load_level(self, level_id: str) -> LevelLayoutData:
        """Load level layout data by ID."""
        pass
    
    @abstractmethod
    def get_available_levels(self) -> List[str]:
        """Get a list of all available level IDs."""
        pass


class ISaveGameRepository(ABC):
    """Interface for saving and loading game state."""
    
    @abstractmethod
    def save_game(self, game_state: GameStateData, save_slot: str = "default") -> None:
        """Save the current game state."""
        pass
    
    @abstractmethod
    def load_game(self, save_slot: str = "default") -> Optional[GameStateData]:
        """Load a saved game state."""
        pass
    
    @abstractmethod
    def delete_save(self, save_slot: str) -> None:
        """Delete a saved game."""
        pass
    
    @abstractmethod
    def list_saves(self) -> List[str]:
        """List all available save slots."""
        pass


class IAssetManager(ABC):
    """Interface for managing game assets."""
    
    @abstractmethod
    def get_asset(self, asset_id: str) -> Any:
        """Get an asset by its ID."""
        pass
    
    @abstractmethod
    def preload_assets(self, asset_ids: List[str]) -> None:
        """Preload a list of assets for performance."""
        pass
    
    @abstractmethod
    def clear_cache(self) -> None:
        """Clear the asset cache."""
        pass


class IRenderer(ABC):
    """Interface for rendering the game."""
    
    @abstractmethod
    def render_frame(self, game_state: GameStateData) -> None:
        """Render a complete frame of the game."""
        pass
    
    @abstractmethod
    def get_screen_size(self) -> tuple[int, int]:
        """Get the current screen size."""
        pass
    
    @abstractmethod
    def world_to_screen(self, world_pos: Position) -> tuple[int, int]:
        """Convert world coordinates to screen coordinates."""
        pass


class IAudioPlayer(ABC):
    """Interface for playing audio."""
    
    @abstractmethod
    def play_sound(self, sound_id: str, volume: float = 1.0) -> None:
        """Play a sound effect."""
        pass
    
    @abstractmethod
    def play_music(self, music_id: str, loop: bool = True, volume: float = 1.0) -> None:
        """Play background music."""
        pass
    
    @abstractmethod
    def stop_music(self) -> None:
        """Stop currently playing music."""
        pass
    
    @abstractmethod
    def set_master_volume(self, volume: float) -> None:
        """Set the master volume (0.0 to 1.0)."""
        pass


class IInputHandler(ABC):
    """Interface for handling user input."""
    
    @abstractmethod
    def get_pressed_keys(self) -> List[str]:
        """Get a list of currently pressed keys."""
        pass
    
    @abstractmethod
    def get_mouse_position(self) -> tuple[int, int]:
        """Get the current mouse position."""
        pass
    
    @abstractmethod
    def was_mouse_clicked(self) -> bool:
        """Check if the mouse was clicked this frame."""
        pass
