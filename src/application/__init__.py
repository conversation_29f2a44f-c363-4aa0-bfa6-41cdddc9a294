"""
Application Layer

This layer orchestrates the flow of the game and contains use cases.
It depends only on the Core layer and defines interfaces for outer layers.
"""

from .interfaces import (
    GameStateData,
    LevelLayoutData,
    IEventBus,
    ILevelRepository,
    ISaveGameRepository,
    IAssetManager,
    IRenderer,
    IAudioPlayer,
    IInputHandler,
)

from .use_cases import (
    MovePlayerUseCase,
    PlayerAttackUseCase,
    MonsterAttackUseCase,
    BuildLevelUseCase,
    EquipItemUseCase,
    UnequipItemUseCase,
    GetInventoryUseCase,
    GetPlayerVisualDataUseCase,
    AddItemToInventoryUseCase,
    RemoveItemFromInventoryUseCase,
    CreateNewPlayerUseCase,
    GetPlayerStatsUseCase,
    InteractWithNPCUseCase,
    BuyFromNPCUseCase,
    SellToNPCUseCase,
    CreateNPCUseCase,
    UpdateWanderingEntitiesUseCase,
    TileInteractionUseCase,
)

__all__ = [
    # Data Transfer Objects
    "GameStateData",
    "LevelLayoutData",
    # Interfaces
    "IEventBus",
    "ILevelRepository", 
    "ISaveGameRepository",
    "IAssetManager",
    "IRenderer",
    "IAudioPlayer",
    "IInputHandler",
    # Use Cases
    "MovePlayerUseCase",
    "PlayerAttackUseCase",
    "MonsterAttackUseCase",
    "BuildLevelUseCase",
    "EquipItemUseCase",
    "UnequipItemUseCase",
    "GetInventoryUseCase",
    "GetPlayerVisualDataUseCase",
    "AddItemToInventoryUseCase",
    "RemoveItemFromInventoryUseCase",
    "CreateNewPlayerUseCase",
    "GetPlayerStatsUseCase",
    "InteractWithNPCUseCase",
    "BuyFromNPCUseCase",
    "SellToNPCUseCase",
    "CreateNPCUseCase",
    "UpdateWanderingEntitiesUseCase",
    "TileInteractionUseCase",
]
