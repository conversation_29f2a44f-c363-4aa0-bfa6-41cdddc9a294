"""
Animation Manager for Treebeard's Revenge

This module provides the AnimationManager class that handles player attack animations,
integrating with the game's weapon system and event handling.
"""

import time
from typing import Optional

from src.game_core import (
    Player, Vector2, PlayerAnimationState, AnimationTransform,
    WeaponAnimationTransform, CombinedAnimationTransform,
    calculate_animation_transform, calculate_combined_animation_transform, is_animation_active
)
from src.game_data import get_item_definition
from .interfaces import IEventBus


class AnimationManager:
    """
    Manages player attack animations.
    
    Handles starting attack animations based on weapon type, updating animation state
    each frame, and calculating visual transforms for rendering.
    """
    
    def __init__(self, event_bus: IEventBus):
        """
        Initialize the animation manager.
        
        Args:
            event_bus: Event bus for subscribing to game events
        """
        self.event_bus = event_bus
        self.animation_state = PlayerAnimationState()
        
        # Animation configuration (will be overridden by game config)
        self.config_attack_base_duration = 300  # milliseconds
        self.config_melee_swing_angle = 15.0    # degrees
        self.config_ranged_vibration_amplitude = 2.0  # pixels
        self.config_ranged_vibration_frequency = 20.0  # Hz

        # Weapon-specific animation configuration
        self.config_weapon_swing_angle = 45.0   # degrees
        self.config_weapon_vibration_amplitude = 1.0  # pixels
    
    def update_config(self, config: dict) -> None:
        """
        Update animation configuration from game config.

        Args:
            config: Animation configuration dictionary
        """
        animations_config = config.get('animations', {})
        self.config_attack_base_duration = animations_config.get('attack_base_duration', 300)
        self.config_melee_swing_angle = animations_config.get('melee_swing_angle', 15.0)
        self.config_ranged_vibration_amplitude = animations_config.get('ranged_vibration_amplitude', 2.0)
        self.config_ranged_vibration_frequency = animations_config.get('ranged_vibration_frequency', 20.0)

        # Weapon-specific configuration
        self.config_weapon_swing_angle = animations_config.get('weapon_swing_angle', 45.0)
        self.config_weapon_vibration_amplitude = animations_config.get('weapon_vibration_amplitude', 1.0)
    
    def get_weapon_animation_type(self, player: Player) -> str:
        """
        Determine the animation type based on the player's equipped weapon.
        
        Args:
            player: Player entity with equipment
        
        Returns:
            Animation type: "melee", "ranged", or "unarmed"
        """
        if not player.main_hand_weapon:
            return "unarmed"
        
        weapon_def = get_item_definition(player.main_hand_weapon)
        if not weapon_def:
            return "unarmed"
        
        weapon_type = weapon_def.properties.get("weapon_type", "sword")
        
        # Map weapon types to animation categories
        melee_weapons = ["sword", "axe", "dagger"]
        ranged_weapons = ["bow"]
        
        if weapon_type in melee_weapons:
            return "melee"
        elif weapon_type in ranged_weapons:
            return "ranged"
        else:
            return "melee"  # Default fallback
    
    def get_weapon_attack_speed(self, player: Player) -> float:
        """
        Get the attack speed of the player's equipped weapon.
        
        Args:
            player: Player entity with equipment
        
        Returns:
            Attack speed multiplier (1.0 = normal speed)
        """
        if not player.main_hand_weapon:
            return 1.0
        
        weapon_def = get_item_definition(player.main_hand_weapon)
        if not weapon_def:
            return 1.0
        
        return weapon_def.properties.get("attack_speed", 1.0)
    
    def start_attack_animation(self, player: Player, direction: Vector2) -> None:
        """
        Start a new attack animation based on the player's weapon.
        
        Args:
            player: Player entity performing the attack
            direction: Normalized direction vector of the attack
        """
        current_time = time.time()
        
        # Determine weapon type and animation parameters
        weapon_type = self.get_weapon_animation_type(player)
        attack_speed = self.get_weapon_attack_speed(player)
        
        # Calculate animation duration based on weapon attack speed
        # Higher attack speed = shorter animation duration
        base_duration_seconds = self.config_attack_base_duration / 1000.0  # Convert to seconds
        animation_duration = base_duration_seconds / attack_speed
        
        # Update animation state
        self.animation_state = PlayerAnimationState(
            animation_type="attacking",
            start_time=current_time,
            duration=animation_duration,
            weapon_type=weapon_type,
            attack_direction=direction
        )
    
    def update_animation(self, dt: float) -> None:
        """
        Update the animation state each frame.
        
        Args:
            dt: Delta time in seconds since last update
        """
        current_time = time.time()
        
        # Check if current animation has finished
        if not is_animation_active(self.animation_state, current_time):
            # Reset to idle state
            self.animation_state = PlayerAnimationState(animation_type="idle")
    
    def get_current_animation_transform(self) -> AnimationTransform:
        """
        Get the current visual transform to apply to the player sprite.

        Returns:
            AnimationTransform with rotation and offset values
        """
        current_time = time.time()

        return calculate_animation_transform(
            self.animation_state,
            current_time,
            self.config_melee_swing_angle,
            self.config_ranged_vibration_amplitude,
            self.config_ranged_vibration_frequency
        )

    def get_current_combined_animation_transform(self) -> CombinedAnimationTransform:
        """
        Get the current combined animation transforms for both player and weapon.

        Returns:
            CombinedAnimationTransform with both player and weapon transforms
        """
        current_time = time.time()

        return calculate_combined_animation_transform(
            self.animation_state,
            current_time,
            self.config_melee_swing_angle,
            self.config_ranged_vibration_amplitude,
            self.config_ranged_vibration_frequency,
            self.config_weapon_swing_angle,
            self.config_weapon_vibration_amplitude
        )
    
    def is_animation_active(self) -> bool:
        """
        Check if an animation is currently playing.
        
        Returns:
            True if animation is active, False otherwise
        """
        current_time = time.time()
        return is_animation_active(self.animation_state, current_time)
    
    def get_current_animation_type(self) -> str:
        """
        Get the current animation type.
        
        Returns:
            Current animation type string
        """
        return self.animation_state.animation_type
