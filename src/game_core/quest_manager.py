"""
Generic Global Quest System

This module provides a completely generic quest management system that handles
quest states and objectives without any knowledge of game-specific entities,
NPCs, levels, or dialogue. All game-specific logic is handled by level-specific
event handlers.

NO PYGAME IMPORTS ALLOWED IN THIS MODULE.
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any
from enum import Enum
import time

from typing import TYPE_CHECKING
import logging

if TYPE_CHECKING:
    from src.application.interfaces import IEventBus

def get_logger(name):
    return logging.getLogger(name)


class QuestState(Enum):
    """Possible states for a quest."""
    NOT_STARTED = "not_started"
    ACTIVE = "active"
    COMPLETED = "completed"
    CLOSED = "closed"  # Quest completed and reward claimed
    FAILED = "failed"


@dataclass
class QuestObjective:
    """Represents a single objective within a quest."""
    id: str
    completed: bool = False
    completion_time: Optional[float] = None


@dataclass
class Quest:
    """Represents a quest with its metadata and objectives."""
    id: str
    name: str
    description: str
    objectives: List[QuestObjective] = field(default_factory=list)
    state: QuestState = QuestState.NOT_STARTED
    start_time: Optional[float] = None
    completion_time: Optional[float] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

    def is_completed(self) -> bool:
        """Check if all objectives are completed."""
        return all(obj.completed for obj in self.objectives)

    def get_objective(self, objective_id: str) -> Optional[QuestObjective]:
        """Get an objective by ID."""
        for obj in self.objectives:
            if obj.id == objective_id:
                return obj
        return None

    def get_completion_percentage(self) -> float:
        """Get the percentage of objectives completed."""
        if not self.objectives:
            return 0.0
        completed_count = sum(1 for obj in self.objectives if obj.completed)
        return (completed_count / len(self.objectives)) * 100.0


class GlobalQuestManager:
    """
    Generic global quest manager that handles quest state tracking and events.
    
    This class is completely agnostic about game entities, NPCs, levels, or dialogue.
    It only manages quest states and objectives, emitting events when state changes occur.
    All game-specific logic should be handled by level-specific event handlers.
    """

    def __init__(self, event_bus: "IEventBus"):
        """
        Initialize the quest manager.
        
        Args:
            event_bus: Event bus for publishing quest events
        """
        self.event_bus = event_bus
        self.logger = get_logger(__name__)
        
        # Quest storage
        self._quests: Dict[str, Quest] = {}
        self._registered_quest_definitions: Dict[str, Dict[str, Any]] = {}
        
        self.logger.info("GlobalQuestManager initialized")

    def register_quest(self, quest_definition: Dict[str, Any]) -> bool:
        """
        Register a quest definition.
        
        Args:
            quest_definition: Dictionary containing quest metadata and objectives
                Expected format:
                {
                    "id": "quest_id",
                    "name": "Quest Name",
                    "description": "Quest description",
                    "objectives": ["objective_1", "objective_2", ...],
                    "metadata": {...}  # Optional additional data
                }
        
        Returns:
            True if registration successful, False otherwise
        """
        try:
            quest_id = quest_definition.get("id")
            if not quest_id:
                self.logger.error("Quest definition missing required 'id' field")
                return False

            if quest_id in self._registered_quest_definitions:
                self.logger.warning(f"Quest '{quest_id}' already registered, overwriting")

            # Validate required fields
            required_fields = ["name", "description", "objectives"]
            for field in required_fields:
                if field not in quest_definition:
                    self.logger.error(f"Quest definition '{quest_id}' missing required field: {field}")
                    return False

            # Store the definition
            self._registered_quest_definitions[quest_id] = quest_definition.copy()
            
            # Create quest instance if not already exists
            if quest_id not in self._quests:
                objectives = [
                    QuestObjective(id=obj_id) 
                    for obj_id in quest_definition["objectives"]
                ]
                
                quest = Quest(
                    id=quest_id,
                    name=quest_definition["name"],
                    description=quest_definition["description"],
                    objectives=objectives,
                    metadata=quest_definition.get("metadata", {})
                )
                
                self._quests[quest_id] = quest

            self.logger.info(f"Quest '{quest_id}' registered successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to register quest: {e}")
            return False

    def start_quest(self, quest_id: str) -> bool:
        """
        Start a quest by changing its state to ACTIVE.
        
        Args:
            quest_id: ID of the quest to start
            
        Returns:
            True if quest started successfully, False otherwise
        """
        try:
            quest = self._quests.get(quest_id)
            if not quest:
                self.logger.error(f"Cannot start quest '{quest_id}': quest not found")
                return False

            if quest.state != QuestState.NOT_STARTED:
                self.logger.warning(f"Cannot start quest '{quest_id}': quest is in state {quest.state}")
                return False

            # Update quest state
            quest.state = QuestState.ACTIVE
            quest.start_time = time.time()

            # Emit quest started event
            from src.game_core.events import GameEvent
            event = GameEvent(
                event_type="quest_started",
                timestamp=time.time(),
                data={
                    "quest_id": quest_id,
                    "quest_name": quest.name,
                    "quest_description": quest.description
                }
            )
            self.event_bus.publish(event)

            self.logger.info(f"Quest '{quest_id}' started")
            return True

        except Exception as e:
            self.logger.error(f"Failed to start quest '{quest_id}': {e}")
            return False

    def complete_objective(self, quest_id: str, objective_id: str) -> bool:
        """
        Mark an objective as completed.
        
        Args:
            quest_id: ID of the quest containing the objective
            objective_id: ID of the objective to complete
            
        Returns:
            True if objective completed successfully, False otherwise
        """
        try:
            quest = self._quests.get(quest_id)
            if not quest:
                self.logger.error(f"Cannot complete objective: quest '{quest_id}' not found")
                return False

            if quest.state != QuestState.ACTIVE:
                self.logger.warning(f"Cannot complete objective: quest '{quest_id}' is not active")
                return False

            objective = quest.get_objective(objective_id)
            if not objective:
                self.logger.error(f"Objective '{objective_id}' not found in quest '{quest_id}'")
                return False

            if objective.completed:
                self.logger.warning(f"Objective '{objective_id}' in quest '{quest_id}' already completed")
                return True

            # Mark objective as completed
            objective.completed = True
            objective.completion_time = time.time()

            # Emit objective completed event
            from src.game_core.events import GameEvent
            event = GameEvent(
                event_type="objective_completed",
                timestamp=time.time(),
                data={
                    "quest_id": quest_id,
                    "objective_id": objective_id,
                    "quest_name": quest.name
                }
            )
            self.event_bus.publish(event)

            # Check if quest is now complete
            if quest.is_completed():
                quest.state = QuestState.COMPLETED
                quest.completion_time = time.time()

                # Emit quest completed event
                quest_completed_event = GameEvent(
                    event_type="quest_completed",
                    timestamp=time.time(),
                    data={
                        "quest_id": quest_id,
                        "quest_name": quest.name,
                        "completion_time": quest.completion_time
                    }
                )
                self.event_bus.publish(quest_completed_event)

                self.logger.info(f"Quest '{quest_id}' completed!")

            self.logger.info(f"Objective '{objective_id}' in quest '{quest_id}' completed")
            return True

        except Exception as e:
            self.logger.error(f"Failed to complete objective '{objective_id}' in quest '{quest_id}': {e}")
            return False

    def fail_quest(self, quest_id: str) -> bool:
        """
        Mark a quest as failed.
        
        Args:
            quest_id: ID of the quest to fail
            
        Returns:
            True if quest failed successfully, False otherwise
        """
        try:
            quest = self._quests.get(quest_id)
            if not quest:
                self.logger.error(f"Cannot fail quest '{quest_id}': quest not found")
                return False

            if quest.state not in [QuestState.ACTIVE, QuestState.NOT_STARTED]:
                self.logger.warning(f"Cannot fail quest '{quest_id}': quest is in state {quest.state}")
                return False

            quest.state = QuestState.FAILED

            # Emit quest failed event
            from src.game_core.events import GameEvent
            event = GameEvent(
                event_type="quest_failed",
                timestamp=time.time(),
                data={
                    "quest_id": quest_id,
                    "quest_name": quest.name
                }
            )
            self.event_bus.publish(event)

            self.logger.info(f"Quest '{quest_id}' failed")
            return True

        except Exception as e:
            self.logger.error(f"Failed to fail quest '{quest_id}': {e}")
            return False

    def close_quest(self, quest_id: str) -> bool:
        """
        Close a completed quest (transition from COMPLETED to CLOSED).
        This is typically called after rewards have been distributed.
        
        Args:
            quest_id: ID of the quest to close
            
        Returns:
            True if quest closed successfully, False otherwise
        """
        try:
            quest = self._quests.get(quest_id)
            if not quest:
                self.logger.error(f"Cannot close quest '{quest_id}': quest not found")
                return False

            if quest.state != QuestState.COMPLETED:
                self.logger.warning(f"Cannot close quest '{quest_id}': quest is not completed (current state: {quest.state})")
                return False

            quest.state = QuestState.CLOSED

            # Emit quest closed event
            from src.game_core.events import GameEvent
            event = GameEvent(
                event_type="quest_closed",
                timestamp=time.time(),
                data={
                    "quest_id": quest_id,
                    "quest_name": quest.name
                }
            )
            self.event_bus.publish(event)

            self.logger.info(f"Quest '{quest_id}' closed (rewards distributed)")
            return True

        except Exception as e:
            self.logger.error(f"Failed to close quest '{quest_id}': {e}")
            return False

    def get_quest_state(self, quest_id: str) -> Optional[QuestState]:
        """
        Get the current state of a quest.
        
        Args:
            quest_id: ID of the quest
            
        Returns:
            QuestState if quest exists, None otherwise
        """
        quest = self._quests.get(quest_id)
        return quest.state if quest else None

    def get_quest(self, quest_id: str) -> Optional[Quest]:
        """
        Get a quest by ID.
        
        Args:
            quest_id: ID of the quest
            
        Returns:
            Quest if found, None otherwise
        """
        return self._quests.get(quest_id)

    def get_active_quests(self) -> List[Quest]:
        """
        Get all currently active quests.
        
        Returns:
            List of active quests
        """
        return [quest for quest in self._quests.values() if quest.state == QuestState.ACTIVE]

    def get_completed_quests(self) -> List[Quest]:
        """
        Get all completed quests.
        
        Returns:
            List of completed quests
        """
        return [quest for quest in self._quests.values() if quest.state == QuestState.COMPLETED]

    def is_objective_completed(self, quest_id: str, objective_id: str) -> bool:
        """
        Check if a specific objective is completed.
        
        Args:
            quest_id: ID of the quest
            objective_id: ID of the objective
            
        Returns:
            True if objective is completed, False otherwise
        """
        quest = self._quests.get(quest_id)
        if not quest:
            return False

        objective = quest.get_objective(objective_id)
        return objective.completed if objective else False

    def get_quest_progress(self, quest_id: str) -> Dict[str, Any]:
        """
        Get detailed progress information for a quest.
        
        Args:
            quest_id: ID of the quest
            
        Returns:
            Dictionary containing quest progress information
        """
        quest = self._quests.get(quest_id)
        if not quest:
            return {}

        return {
            "quest_id": quest.id,
            "name": quest.name,
            "description": quest.description,
            "state": quest.state.value,
            "completion_percentage": quest.get_completion_percentage(),
            "objectives": [
                {
                    "id": obj.id,
                    "completed": obj.completed,
                    "completion_time": obj.completion_time
                }
                for obj in quest.objectives
            ],
            "start_time": quest.start_time,
            "completion_time": quest.completion_time,
            "metadata": quest.metadata
        }

    def get_all_quests_data(self) -> Dict[str, Any]:
        """
        Get serializable data for all quests (for save/load).
        
        Returns:
            Dictionary containing all quest data
        """
        return {
            quest_id: {
                "id": quest.id,
                "name": quest.name,
                "description": quest.description,
                "state": quest.state.value,
                "start_time": quest.start_time,
                "completion_time": quest.completion_time,
                "metadata": quest.metadata,
                "objectives": [
                    {
                        "id": obj.id,
                        "completed": obj.completed,
                        "completion_time": obj.completion_time
                    }
                    for obj in quest.objectives
                ]
            }
            for quest_id, quest in self._quests.items()
        }

    def load_quests_data(self, quests_data: Dict[str, Any]) -> None:
        """
        Load quest data from serialized format (for save/load).
        
        Args:
            quests_data: Dictionary containing serialized quest data
        """
        try:
            for quest_id, quest_data in quests_data.items():
                # Recreate objectives
                objectives = [
                    QuestObjective(
                        id=obj_data["id"],
                        completed=obj_data["completed"],
                        completion_time=obj_data.get("completion_time")
                    )
                    for obj_data in quest_data.get("objectives", [])
                ]

                # Recreate quest
                quest = Quest(
                    id=quest_data["id"],
                    name=quest_data["name"],
                    description=quest_data["description"],
                    objectives=objectives,
                    state=QuestState(quest_data["state"]),
                    start_time=quest_data.get("start_time"),
                    completion_time=quest_data.get("completion_time"),
                    metadata=quest_data.get("metadata", {})
                )

                self._quests[quest_id] = quest

            self.logger.info(f"Loaded {len(quests_data)} quests from save data")

        except Exception as e:
            self.logger.error(f"Failed to load quest data: {e}")
