"""
Game Configuration

This module contains all configurable game constants and settings.
Centralizing configuration here makes it easy to tune game balance and behavior.
"""

from dataclasses import dataclass
from typing import Dict, Any, Optional
import yaml
import json
import os

# Import logging for this module - but delay initialization to avoid circular imports
import logging


@dataclass(frozen=True)  # Immutable configuration
class RenderingConfig:
    """Rendering and visual configuration."""
    # Tile rendering
    tile_size: int = 64  # Size of each tile in pixels
    base_entity_size: int = 128  # Default size for entity sprites in pixels
    screen_width: int = 1200  # Default window width
    screen_height: int = 800  # Default window height
    target_fps: int = 60  # Target frames per second

    # Camera
    camera_lerp_factor: float = 0.15  # Smooth camera movement factor (0.0-1.0)
    camera_boundary_margin: int = 2  # Tiles of margin before camera boundaries


@dataclass(frozen=True)
class InventoryUIConfig:
    """Inventory UI configuration."""
    # Panel layout (percentages of screen width/height)
    character_panel_width_percent: int = 30
    inventory_panel_width_percent: int = 45
    stats_panel_width_percent: int = 25
    panel_height_percent: int = 90
    panel_margin: int = 40
    panel_spacing: int = 20

    # Inventory grid settings
    inventory_max_slots: int = 10
    inventory_slot_size: int = 160
    inventory_slot_padding: int = 20

    # Equipment slot settings
    equipment_slot_size: int = 120

    # Typography - now uses centralized ui_scaling configuration

    # Colors (RGBA tuples)
    background_color: tuple = (139, 69, 19, 200)
    border_color: tuple = (101, 67, 33, 255)
    text_color: tuple = (255, 248, 220, 255)
    highlight_color: tuple = (255, 215, 0, 100)

    # Visual effects
    tooltip_delay_ms: int = 500
    double_click_threshold_ms: int = 500

    # Tooltip configuration - now uses centralized ui_scaling configuration
    tooltip_max_width: int = 300  # Base maximum width (before scaling)
    tooltip_padding: int = 8      # Base padding (before scaling)
    tooltip_line_spacing: int = 2 # Base line spacing (before scaling)


@dataclass(frozen=True)
class MovementConfig:
    """Player and entity movement configuration."""
    # Player movement timing
    player_move_cooldown: float = 0.15  # Seconds between player moves
    player_move_speed: float = 6.67  # Moves per second (1 / cooldown)

    # Entity movement (for future use)
    monster_move_cooldown: float = 0.5  # Monsters move slower
    npc_move_cooldown: float = 1.0  # NPCs move even slower


@dataclass(frozen=True)
class CombatConfig:
    """Combat and gameplay balance configuration."""
    # Base combat values
    base_damage_variance: float = 0.2  # ±20% damage variance
    critical_hit_chance: float = 0.05  # 5% base crit chance
    critical_hit_multiplier: float = 2.0  # 2x damage on crit
    
    # Speed affects combat
    speed_hit_modifier: float = 0.02  # 2% hit chance per speed point difference
    
    # Experience and leveling
    base_experience_per_level: int = 100
    experience_scaling_factor: float = 1.5  # Each level requires 50% more XP


@dataclass(frozen=True)
class AudioConfig:
    """Audio and sound configuration."""
    # Volume levels (0.0 - 1.0)
    master_volume: float = 0.8
    sfx_volume: float = 0.7
    music_volume: float = 0.6
    ui_volume: float = 0.5

    # Audio generation parameters
    sample_rate: int = 22050
    audio_bit_depth: int = 16


@dataclass(frozen=True)
class FontSizes:
    """Standard font sizes."""
    tiny: int = 12
    small: int = 16
    normal: int = 20
    medium: int = 24
    large: int = 28
    xlarge: int = 32
    xxlarge: int = 36


@dataclass(frozen=True)
class ElementSizes:
    """Standard UI element sizes."""
    button_height: int = 30
    input_height: int = 25
    menu_height: int = 30
    toolbar_button: int = 40
    status_height: int = 25
    header_height: int = 60
    panel_width: int = 250
    asset_grid_size: int = 48


@dataclass(frozen=True)
class Spacing:
    """Standard spacing values."""
    tiny: int = 2
    small: int = 4
    normal: int = 8
    medium: int = 12
    large: int = 16
    xlarge: int = 20


@dataclass(frozen=True)
class UIScalingConfig:
    """UI scaling configuration."""
    # Global UI scale factor (1.0 = normal, 1.5 = 50% larger, 0.8 = 20% smaller)
    scale_factor: float = 1.0

    # Standard font sizes (before scaling)
    font_sizes: FontSizes = FontSizes()

    # Standard UI element sizes (before scaling)
    element_sizes: ElementSizes = ElementSizes()

    # Standard spacing (before scaling)
    spacing: Spacing = Spacing()


@dataclass(frozen=True)
class SettingsUIConfig:
    """Settings UI configuration."""
    # Show settings on startup
    show_on_startup: bool = True

    # Default save slot for new games
    default_save_slot: str = "slot_1"

    # Settings menu layout
    menu_width_percent: int = 25
    menu_height_percent: int = 60
    button_spacing: int = 15

    # Save slot grid layout
    slots_per_row: int = 2
    slot_grid_spacing: int = 20


@dataclass(frozen=True)
class GameSettings:
    """Game-specific settings."""
    starting_map: str = "town_caledon"  # Default starting map


@dataclass(frozen=True)
class GameConfig:
    """Main game configuration containing all sub-configurations."""
    game: GameSettings = GameSettings()
    rendering: RenderingConfig = RenderingConfig()
    movement: MovementConfig = MovementConfig()
    combat: CombatConfig = CombatConfig()
    audio: AudioConfig = AudioConfig()
    inventory_ui: InventoryUIConfig = InventoryUIConfig()
    ui_scaling: UIScalingConfig = UIScalingConfig()
    settings_ui: SettingsUIConfig = SettingsUIConfig()

    # Debug settings
    debug_mode: bool = False
    show_collision_boxes: bool = False
    show_interaction_areas: bool = False
    show_fps: bool = False
    verbose_logging: bool = False
    show_weapon_direction: bool = False
    show_attack_arc: bool = False
    show_entity_hitboxes: bool = False


# Global configuration instance
# This is the single source of truth for all game configuration
CONFIG = GameConfig()


def get_config() -> GameConfig:
    """Get the global game configuration."""
    return CONFIG


def load_config_file(file_path: str) -> Optional[GameConfig]:
    """
    Load configuration from a YAML or JSON file.
    
    Args:
        file_path: Path to the configuration file
        
    Returns:
        Loaded GameConfig instance, or None if file doesn't exist or is invalid
    """
    if not os.path.exists(file_path):
        return None
    
    try:
        with open(file_path, 'r') as f:
            if file_path.endswith(('.yaml', '.yml')):
                config_dict = yaml.safe_load(f)
            elif file_path.endswith('.json'):
                config_dict = json.load(f)
            else:
                logging.warning(f"Unsupported config file format: {file_path}")
                return None
        
        return load_config_from_dict(config_dict)
    
    except Exception as e:
        logging.error(f"Error loading config file {file_path}: {e}")
        # Re-raise the exception with more context instead of returning None
        raise Exception(f"Failed to load config file {file_path}: {e}") from e


def save_config_file(config: GameConfig, file_path: str) -> bool:
    """
    Save configuration to a YAML or JSON file.
    
    Args:
        config: GameConfig instance to save
        file_path: Path where to save the configuration file
        
    Returns:
        True if saved successfully, False otherwise
    """
    try:
        config_dict = create_config_template()
        
        # Update the dictionary to reflect the actual config values
        config_dict.update({
            'rendering': {
                'tile_size': config.rendering.tile_size,
                'base_entity_size': config.rendering.base_entity_size,
                'screen_width': config.rendering.screen_width,
                'screen_height': config.rendering.screen_height,
                'target_fps': config.rendering.target_fps,
                'camera_lerp_factor': config.rendering.camera_lerp_factor,
                'camera_boundary_margin': config.rendering.camera_boundary_margin
            },
            'movement': {
                'player_move_cooldown': config.movement.player_move_cooldown,
                'player_move_speed': config.movement.player_move_speed,
                'monster_move_cooldown': config.movement.monster_move_cooldown,
                'npc_move_cooldown': config.movement.npc_move_cooldown
            },
            'combat': {
                'base_damage_variance': config.combat.base_damage_variance,
                'critical_hit_chance': config.combat.critical_hit_chance,
                'critical_hit_multiplier': config.combat.critical_hit_multiplier,
                'speed_hit_modifier': config.combat.speed_hit_modifier,
                'base_experience_per_level': config.combat.base_experience_per_level,
                'experience_scaling_factor': config.combat.experience_scaling_factor
            },
            'audio': {
                'master_volume': config.audio.master_volume,
                'sfx_volume': config.audio.sfx_volume,
                'music_volume': config.audio.music_volume,
                'ui_volume': config.audio.ui_volume,
                'sample_rate': config.audio.sample_rate,
                'audio_bit_depth': config.audio.audio_bit_depth
            },
            'debug_mode': config.debug_mode,
            'show_collision_boxes': config.show_collision_boxes,
            'show_interaction_areas': config.show_interaction_areas,
            'show_fps': config.show_fps,
            'verbose_logging': config.verbose_logging,
            'show_weapon_direction': config.show_weapon_direction,
            'show_attack_arc': config.show_attack_arc,
            'show_entity_hitboxes': config.show_entity_hitboxes
        })
        
        with open(file_path, 'w') as f:
            if file_path.endswith(('.yaml', '.yml')):
                yaml.safe_dump(config_dict, f, default_flow_style=False, indent=2)
            elif file_path.endswith('.json'):
                json.dump(config_dict, f, indent=2)
            else:
                logging.warning(f"Unsupported config file format: {file_path}")
                return False
        
        return True
    
    except Exception as e:
        logging.error(f"Error saving config file {file_path}: {e}")
        return False


def initialize_config(config_file_path: str = "game_config.yaml") -> GameConfig:
    """
    Initialize configuration, loading from file if available.

    Args:
        config_file_path: Path to the configuration file

    Returns:
        Initialized GameConfig instance

    Raises:
        FileNotFoundError: If config file doesn't exist
        Exception: If config file exists but fails to parse
    """
    global CONFIG

    # Check if config file exists
    if not os.path.exists(config_file_path):
        # Create a default config file for users to modify
        save_config_file(CONFIG, config_file_path)
        logging.info(f"Created default config file at {config_file_path}")
        return CONFIG

    # Try to load from file
    loaded_config = load_config_file(config_file_path)
    if loaded_config:
        CONFIG = loaded_config
        logging.info(f"Loaded configuration from {config_file_path}")
        return CONFIG
    else:
        # Config file exists but failed to parse - this is an error
        raise Exception(f"Failed to parse configuration file {config_file_path}. Check the file format and syntax.")

    return CONFIG


def load_config_from_dict(config_dict: Dict[str, Any]) -> GameConfig:
    """
    Load configuration from a dictionary (e.g., from JSON/YAML file).
    
    Args:
        config_dict: Dictionary containing configuration values
        
    Returns:
        New GameConfig instance with loaded values
    """
    # Extract sub-configs
    rendering_dict = config_dict.get('rendering', {})
    movement_dict = config_dict.get('movement', {})
    combat_dict = config_dict.get('combat', {})
    audio_dict = config_dict.get('audio', {})
    inventory_ui_dict = config_dict.get('inventory_ui', {})
    ui_scaling_dict = config_dict.get('ui_scaling', {})

    # Handle color tuples for inventory UI
    if 'background_color' in inventory_ui_dict:
        inventory_ui_dict['background_color'] = tuple(inventory_ui_dict['background_color'])
    if 'border_color' in inventory_ui_dict:
        inventory_ui_dict['border_color'] = tuple(inventory_ui_dict['border_color'])
    if 'text_color' in inventory_ui_dict:
        inventory_ui_dict['text_color'] = tuple(inventory_ui_dict['text_color'])
    if 'highlight_color' in inventory_ui_dict:
        inventory_ui_dict['highlight_color'] = tuple(inventory_ui_dict['highlight_color'])

    # Create config objects
    rendering_config = RenderingConfig(**rendering_dict)
    movement_config = MovementConfig(**movement_dict)
    combat_config = CombatConfig(**combat_dict)
    audio_config = AudioConfig(**audio_dict)
    inventory_ui_config = InventoryUIConfig(**inventory_ui_dict)

    # Handle UI scaling config with nested structures
    ui_scaling_config = UIScalingConfig()
    if ui_scaling_dict:
        # Extract nested dictionaries
        font_sizes_dict = ui_scaling_dict.get('font_sizes', {})
        element_sizes_dict = ui_scaling_dict.get('element_sizes', {})
        spacing_dict = ui_scaling_dict.get('spacing', {})

        # Create nested config objects
        font_sizes = FontSizes(**font_sizes_dict) if font_sizes_dict else FontSizes()
        element_sizes = ElementSizes(**element_sizes_dict) if element_sizes_dict else ElementSizes()
        spacing = Spacing(**spacing_dict) if spacing_dict else Spacing()

        # Create UI scaling config
        ui_scaling_config = UIScalingConfig(
            scale_factor=ui_scaling_dict.get('scale_factor', 1.0),
            font_sizes=font_sizes,
            element_sizes=element_sizes,
            spacing=spacing
        )

    # Handle settings UI config
    settings_ui_dict = config_dict.get('settings_ui', {})
    settings_ui_config = SettingsUIConfig(**settings_ui_dict) if settings_ui_dict else SettingsUIConfig()
    
    # Create game settings config
    game_settings_dict = config_dict.get('game', {})
    game_settings_config = GameSettings(
        starting_map=game_settings_dict.get('starting_map', 'town_caledon')
    )

    # Create main config
    game_config = GameConfig(
        game=game_settings_config,
        rendering=rendering_config,
        movement=movement_config,
        combat=combat_config,
        audio=audio_config,
        inventory_ui=inventory_ui_config,
        ui_scaling=ui_scaling_config,
        settings_ui=settings_ui_config,
        debug_mode=config_dict.get('debug_mode', False),
        show_collision_boxes=config_dict.get('show_collision_boxes', False),
        show_interaction_areas=config_dict.get('show_interaction_areas', False),
        show_fps=config_dict.get('show_fps', False),
        verbose_logging=config_dict.get('verbose_logging', False),
        show_weapon_direction=config_dict.get('show_weapon_direction', False),
        show_attack_arc=config_dict.get('show_attack_arc', False),
        show_entity_hitboxes=config_dict.get('show_entity_hitboxes', False)
    )
    
    return game_config


def create_config_template() -> Dict[str, Any]:
    """
    Create a configuration template dictionary for saving to file.
    
    Returns:
        Dictionary representation of default configuration
    """
    config = get_config()
    
    return {
        'game': {
            'starting_map': config.game.starting_map
        },
        'rendering': {
            'tile_size': config.rendering.tile_size,
            'base_entity_size': config.rendering.base_entity_size,
            'screen_width': config.rendering.screen_width,
            'screen_height': config.rendering.screen_height,
            'target_fps': config.rendering.target_fps,
            'camera_lerp_factor': config.rendering.camera_lerp_factor,
            'camera_boundary_margin': config.rendering.camera_boundary_margin
        },
        'movement': {
            'player_move_cooldown': config.movement.player_move_cooldown,
            'player_move_speed': config.movement.player_move_speed,
            'monster_move_cooldown': config.movement.monster_move_cooldown,
            'npc_move_cooldown': config.movement.npc_move_cooldown
        },
        'combat': {
            'base_damage_variance': config.combat.base_damage_variance,
            'critical_hit_chance': config.combat.critical_hit_chance,
            'critical_hit_multiplier': config.combat.critical_hit_multiplier,
            'speed_hit_modifier': config.combat.speed_hit_modifier,
            'base_experience_per_level': config.combat.base_experience_per_level,
            'experience_scaling_factor': config.combat.experience_scaling_factor
        },
        'audio': {
            'master_volume': config.audio.master_volume,
            'sfx_volume': config.audio.sfx_volume,
            'music_volume': config.audio.music_volume,
            'ui_volume': config.audio.ui_volume,
            'sample_rate': config.audio.sample_rate,
            'audio_bit_depth': config.audio.audio_bit_depth
        },
        'debug_mode': config.debug_mode,
        'show_collision_boxes': config.show_collision_boxes,
        'show_interaction_areas': config.show_interaction_areas,
        'show_fps': config.show_fps,
        'verbose_logging': config.verbose_logging,
        'show_weapon_direction': config.show_weapon_direction,
        'show_attack_arc': config.show_attack_arc,
        'show_entity_hitboxes': config.show_entity_hitboxes
    }
