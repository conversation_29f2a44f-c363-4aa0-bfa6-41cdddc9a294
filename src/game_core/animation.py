"""
Animation System for Treebeard's Revenge

This module provides the core animation data structures and calculation functions
for player attack animations, including weapon-specific effects.
"""

import math
from dataclasses import dataclass
from typing import Optional, Tuple
from .entities import Vector2


@dataclass
class PlayerAnimationState:
    """
    Represents the current animation state of the player.
    
    Attributes:
        animation_type: Type of animation (idle, attacking, moving)
        start_time: When the animation started (in seconds)
        duration: Total duration of the animation (in seconds)
        weapon_type: Type of weapon being used (melee, ranged, unarmed)
        attack_direction: Direction vector of the attack
    """
    animation_type: str = "idle"  # idle, attacking, moving
    start_time: float = 0.0
    duration: float = 0.0
    weapon_type: Optional[str] = None
    attack_direction: Optional[Vector2] = None


@dataclass
class AnimationTransform:
    """
    Represents a visual transformation to apply to a sprite.

    Attributes:
        rotation: Rotation angle in degrees
        offset_x: Horizontal offset in pixels
        offset_y: Vertical offset in pixels
    """
    rotation: float = 0.0
    offset_x: float = 0.0
    offset_y: float = 0.0


@dataclass
class WeaponAnimationTransform:
    """
    Represents a visual transformation to apply specifically to a weapon sprite.

    Attributes:
        rotation: Weapon rotation angle in degrees
        offset_x: Horizontal offset in pixels
        offset_y: Vertical offset in pixels
        pivot_x: X coordinate of rotation pivot point (0.0-1.0, relative to weapon sprite)
        pivot_y: Y coordinate of rotation pivot point (0.0-1.0, relative to weapon sprite)
    """
    rotation: float = 0.0
    offset_x: float = 0.0
    offset_y: float = 0.0
    pivot_x: float = 0.5  # Default to center
    pivot_y: float = 0.8  # Default to handle/grip area


@dataclass
class CombinedAnimationTransform:
    """
    Combined animation transforms for player and weapon.

    Attributes:
        player_transform: Transform to apply to the player body
        weapon_transform: Transform to apply to the weapon
    """
    player_transform: AnimationTransform = None
    weapon_transform: WeaponAnimationTransform = None

    def __post_init__(self):
        if self.player_transform is None:
            self.player_transform = AnimationTransform()
        if self.weapon_transform is None:
            self.weapon_transform = WeaponAnimationTransform()


def calculate_animation_progress(current_time: float, start_time: float, duration: float) -> float:
    """
    Calculate the progress of an animation as a value between 0.0 and 1.0.
    
    Args:
        current_time: Current time in seconds
        start_time: When the animation started in seconds
        duration: Total animation duration in seconds
    
    Returns:
        Progress value between 0.0 (start) and 1.0 (end), clamped to this range
    """
    if duration <= 0:
        return 1.0
    
    elapsed = current_time - start_time
    progress = elapsed / duration
    return max(0.0, min(1.0, progress))


def calculate_swing_rotation(progress: float, direction: Vector2, max_angle: float = 15.0) -> float:
    """
    Calculate the rotation angle for a melee weapon swing animation.
    
    Uses a sine wave to create smooth swing motion that starts and ends at 0 degrees.
    
    Args:
        progress: Animation progress from 0.0 to 1.0
        direction: Normalized direction vector of the attack
        max_angle: Maximum swing angle in degrees
    
    Returns:
        Rotation angle in degrees
    """
    # Use sine wave for smooth swing motion (0 -> max -> 0)
    swing_intensity = math.sin(progress * math.pi)
    
    # Apply direction to determine swing direction
    # Use the x component to swing left/right based on attack direction
    swing_angle = swing_intensity * max_angle * direction.x
    
    return swing_angle


def calculate_vibration_offset(progress: float, amplitude: float = 2.0, frequency: float = 20.0) -> Tuple[float, float]:
    """
    Calculate the offset for a ranged weapon vibration animation.
    
    Creates a high-frequency oscillation effect to simulate weapon vibration.
    
    Args:
        progress: Animation progress from 0.0 to 1.0
        amplitude: Maximum vibration amplitude in pixels
        frequency: Vibration frequency in Hz
    
    Returns:
        Tuple of (offset_x, offset_y) in pixels
    """
    # High frequency oscillation with decreasing amplitude over time
    time_factor = progress * frequency * 2 * math.pi
    
    # Decrease amplitude over time for natural feel
    current_amplitude = amplitude * (1.0 - progress * 0.5)
    
    offset_x = math.sin(time_factor) * current_amplitude
    offset_y = math.cos(time_factor * 1.3) * current_amplitude * 0.5  # Different frequency for Y
    
    return (offset_x, offset_y)


def calculate_weapon_swing_rotation(progress: float, direction: Vector2, max_angle: float = 45.0) -> float:
    """
    Calculate the rotation angle for a weapon swing animation.

    Creates a natural sword swing where the weapon rotates from its resting position
    in the direction of the attack. For vertical attacks (within 20 degrees of straight
    up or down), uses a stabbing motion instead of a swing.

    Args:
        progress: Animation progress from 0.0 to 1.0
        direction: Normalized direction vector of the attack
        max_angle: Maximum swing angle in degrees

    Returns:
        Rotation angle in degrees
    """
    import math

    # Calculate the angle of attack in degrees
    attack_angle_rad = math.atan2(direction.y, direction.x)
    attack_angle_deg = attack_angle_rad * 180 / math.pi

    # Normalize angle to -180 to 180 range
    while attack_angle_deg > 180:
        attack_angle_deg -= 360
    while attack_angle_deg < -180:
        attack_angle_deg += 360

    # Define vertical attack zones (45 degrees each)
    up_zone_center = -90  # Straight up
    down_zone_center = 90  # Straight down
    vertical_zone_width = 45  # degrees

    # Check if attacking in vertical zones
    attacking_up = abs(attack_angle_deg - up_zone_center) <= vertical_zone_width / 2
    attacking_down = abs(attack_angle_deg - down_zone_center) <= vertical_zone_width / 2

    if attacking_up or attacking_down:
        # Stabbing motion
        if attacking_up:
            # Stabbing up: keep weapon in natural position, no rotation
            return 0
        else:
            # Stabbing down: instantly rotate sword 180° to point down, then back at end
            if progress < 0.05:
                # Very beginning: normal position (before flip)
                return 0
            elif progress > 0.95:
                # Very end: back to normal position (after flip back)
                return 0
            else:
                # During attack: sword is flipped 180° to point down
                return 180
    else:
        # Normal swing motion for horizontal and diagonal attacks
        # Use sine wave for smooth swing motion (0 -> max -> 0)
        swing_intensity = math.sin(progress * math.pi)

        # Determine swing direction based on attack direction
        # For natural sword movement:
        # - Right attacks: swing clockwise (positive rotation)
        # - Left attacks: swing counter-clockwise (negative rotation)

        # Use the x component primarily, with some influence from y component
        swing_direction = direction.x

        # Add some influence from vertical direction for diagonal attacks
        if direction.y < -0.3:  # Attacking somewhat up
            swing_direction = direction.x * 0.8  # Reduce swing intensity
        elif direction.y > 0.3:  # Attacking somewhat down
            swing_direction = direction.x * 1.2  # Increase swing intensity

        # Calculate final swing angle
        swing_angle = swing_intensity * max_angle * swing_direction

        return swing_angle


def calculate_stabbing_offset(progress: float, direction: Vector2, max_offset: float = 15.0) -> Tuple[float, float]:
    """
    Calculate the offset for a stabbing motion (up/down movement).

    Args:
        progress: Animation progress from 0.0 to 1.0
        direction: Normalized direction vector of the attack
        max_offset: Maximum stabbing offset in pixels

    Returns:
        Tuple of (offset_x, offset_y) in pixels
    """
    import math

    # Calculate the angle of attack in degrees
    attack_angle_rad = math.atan2(direction.y, direction.x)
    attack_angle_deg = attack_angle_rad * 180 / math.pi

    # Normalize angle to -180 to 180 range
    while attack_angle_deg > 180:
        attack_angle_deg -= 360
    while attack_angle_deg < -180:
        attack_angle_deg += 360

    # Define vertical attack zones (45 degrees each)
    up_zone_center = -90  # Straight up
    down_zone_center = 90  # Straight down
    vertical_zone_width = 45  # degrees

    # Check if attacking in vertical zones
    attacking_up = abs(attack_angle_deg - up_zone_center) <= vertical_zone_width / 2
    attacking_down = abs(attack_angle_deg - down_zone_center) <= vertical_zone_width / 2

    if attacking_up or attacking_down:
        # Stabbing motion: move weapon forward then back
        # Use sine wave for smooth motion (0 -> max -> 0)
        stab_intensity = math.sin(progress * math.pi)

        if attacking_up:
            # Stabbing up: move weapon upward
            offset_x = 0.0
            offset_y = -stab_intensity * max_offset  # Negative Y is up
        else:
            # Stabbing down: move weapon downward
            offset_x = 0.0
            offset_y = stab_intensity * max_offset  # Positive Y is down

        return (offset_x, offset_y)
    else:
        # No stabbing offset for non-vertical attacks
        return (0.0, 0.0)


def calculate_weapon_animation_transform(animation_state: PlayerAnimationState, current_time: float,
                                       config_weapon_swing_angle: float = 45.0,
                                       config_weapon_vibration_amplitude: float = 1.0) -> WeaponAnimationTransform:
    """
    Calculate the weapon-specific animation transform.

    Args:
        animation_state: Current animation state
        current_time: Current time in seconds
        config_weapon_swing_angle: Maximum weapon swing angle from configuration
        config_weapon_vibration_amplitude: Weapon vibration amplitude from configuration

    Returns:
        WeaponAnimationTransform with weapon-specific rotation and offset values
    """
    # Default to no transformation
    transform = WeaponAnimationTransform()

    # Only apply transforms for attacking animations
    if animation_state.animation_type != "attacking" or not animation_state.attack_direction:
        return transform

    # Calculate animation progress
    progress = calculate_animation_progress(current_time, animation_state.start_time, animation_state.duration)

    # Apply weapon-specific animations
    if animation_state.weapon_type == "melee":
        # Melee weapons rotate in attack direction
        transform.rotation = calculate_weapon_swing_rotation(
            progress, animation_state.attack_direction, config_weapon_swing_angle
        )

        # Add stabbing offset for vertical attacks
        stab_offset_x, stab_offset_y = calculate_stabbing_offset(
            progress, animation_state.attack_direction, 15.0  # 15 pixel max stab distance
        )
        transform.offset_x += stab_offset_x
        transform.offset_y += stab_offset_y

        # Set pivot point for melee weapons (at handle/hilt area)
        # For swords: handle is in player's right hand position
        transform.pivot_x = 0.72  # Right hand position (70% right + half handle width)
        transform.pivot_y = 0.71  # Hand height (around 70% down)

    elif animation_state.weapon_type == "ranged":
        # Ranged weapons have minimal rotation but some vibration
        offset_x, offset_y = calculate_vibration_offset(
            progress, config_weapon_vibration_amplitude, 25.0  # Higher frequency for weapons
        )
        transform.offset_x = offset_x
        transform.offset_y = offset_y
        # Bow pivot point (at grip area in left hand)
        transform.pivot_x = 0.24  # Left hand position (around 24% from left)
        transform.pivot_y = 0.59  # Grip area (around 59% down)

    return transform


def calculate_animation_transform(animation_state: PlayerAnimationState, current_time: float,
                                config_swing_angle: float = 15.0,
                                config_vibration_amplitude: float = 2.0,
                                config_vibration_frequency: float = 20.0) -> AnimationTransform:
    """
    Calculate the visual transform to apply based on the current animation state.

    Args:
        animation_state: Current animation state
        current_time: Current time in seconds
        config_swing_angle: Maximum swing angle from configuration
        config_vibration_amplitude: Vibration amplitude from configuration
        config_vibration_frequency: Vibration frequency from configuration

    Returns:
        AnimationTransform with rotation and offset values
    """
    # Default to no transformation
    transform = AnimationTransform()

    # Only apply transforms for attacking animations
    if animation_state.animation_type != "attacking" or not animation_state.attack_direction:
        return transform

    # Calculate animation progress
    progress = calculate_animation_progress(current_time, animation_state.start_time, animation_state.duration)

    # Apply weapon-specific animations (reduced for player body)
    if animation_state.weapon_type == "melee":
        # Melee weapons use swing rotation (reduced for player body)
        transform.rotation = calculate_swing_rotation(
            progress, animation_state.attack_direction, config_swing_angle
        )
    elif animation_state.weapon_type == "ranged":
        # Ranged weapons use vibration offset
        offset_x, offset_y = calculate_vibration_offset(
            progress, config_vibration_amplitude, config_vibration_frequency
        )
        transform.offset_x = offset_x
        transform.offset_y = offset_y

    return transform


def calculate_combined_animation_transform(animation_state: PlayerAnimationState, current_time: float,
                                         config_player_swing_angle: float = 15.0,
                                         config_player_vibration_amplitude: float = 2.0,
                                         config_player_vibration_frequency: float = 20.0,
                                         config_weapon_swing_angle: float = 45.0,
                                         config_weapon_vibration_amplitude: float = 1.0) -> CombinedAnimationTransform:
    """
    Calculate combined animation transforms for both player and weapon.

    Args:
        animation_state: Current animation state
        current_time: Current time in seconds
        config_player_swing_angle: Maximum player swing angle from configuration
        config_player_vibration_amplitude: Player vibration amplitude from configuration
        config_player_vibration_frequency: Player vibration frequency from configuration
        config_weapon_swing_angle: Maximum weapon swing angle from configuration
        config_weapon_vibration_amplitude: Weapon vibration amplitude from configuration

    Returns:
        CombinedAnimationTransform with both player and weapon transforms
    """
    player_transform = calculate_animation_transform(
        animation_state, current_time,
        config_player_swing_angle, config_player_vibration_amplitude, config_player_vibration_frequency
    )

    weapon_transform = calculate_weapon_animation_transform(
        animation_state, current_time,
        config_weapon_swing_angle, config_weapon_vibration_amplitude
    )

    return CombinedAnimationTransform(
        player_transform=player_transform,
        weapon_transform=weapon_transform
    )


def is_animation_active(animation_state: PlayerAnimationState, current_time: float) -> bool:
    """
    Check if an animation is currently active.
    
    Args:
        animation_state: Current animation state
        current_time: Current time in seconds
    
    Returns:
        True if animation is active, False otherwise
    """
    if animation_state.animation_type == "idle":
        return False
    
    elapsed = current_time - animation_state.start_time
    return elapsed < animation_state.duration
