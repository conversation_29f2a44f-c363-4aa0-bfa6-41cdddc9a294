"""
Dialog State Manager

This module manages dialog state for NPCs to support sequential and random conversations.
"""

from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
import random


@dataclass
class DialogState:
    """State information for NPC dialog."""
    dialog_index: int = 0
    conversation_active: bool = False  # Whether a conversation is currently in progress
    current_conversation: List[str] = field(default_factory=list)  # Current conversation lines
    conversation_step: int = 0  # Current step within the active conversation
    custom_data: Dict[str, Any] = field(default_factory=dict)


class DialogManager:
    """Manages dialog state for NPCs across the game."""
    
    def __init__(self):
        """Initialize the dialog manager."""
        self._npc_dialog_states: Dict[str, DialogState] = {}
    
    def get_dialog_state(self, npc_id: str) -> DialogState:
        """
        Get the dialog state for an NPC.
        
        Args:
            npc_id: ID of the NPC
            
        Returns:
            DialogState for the NPC
        """
        if npc_id not in self._npc_dialog_states:
            self._npc_dialog_states[npc_id] = DialogState()
        
        return self._npc_dialog_states[npc_id]
    
    def set_dialog_index(self, npc_id: str, index: int) -> None:
        """
        Set the dialog index for an NPC.
        
        Args:
            npc_id: ID of the NPC
            index: New dialog index
        """
        state = self.get_dialog_state(npc_id)
        state.dialog_index = index
    
    def get_dialog_index(self, npc_id: str) -> int:
        """
        Get the current dialog index for an NPC.

        Args:
            npc_id: ID of the NPC

        Returns:
            Current dialog index
        """
        return self.get_dialog_state(npc_id).dialog_index

    def start_conversation(self, npc_id: str, dialog_lines: List[str], dialog_mode: str = "sequential") -> str:
        """
        Start a new conversation with an NPC.

        Args:
            npc_id: ID of the NPC
            dialog_lines: List of dialog lines for this conversation
            dialog_mode: Mode for dialog selection ("sequential" or "random")

        Returns:
            First line of dialog
        """
        state = self.get_dialog_state(npc_id)

        if not dialog_lines:
            # No dialog available - don't start a conversation
            state.conversation_active = False
            state.current_conversation = []
            state.conversation_step = 0
            return "..."

        state.conversation_active = True

        if dialog_mode == "random":
            # For random mode, select a single random line as the conversation
            selected_dialog = [random.choice(dialog_lines)]
            state.current_conversation = selected_dialog
        else:
            # For sequential mode, use all dialog lines
            state.current_conversation = dialog_lines.copy()

        state.conversation_step = 0
        return state.current_conversation[0]

    def continue_conversation(self, npc_id: str) -> tuple[str, bool]:
        """
        Continue an active conversation.

        Args:
            npc_id: ID of the NPC

        Returns:
            Tuple of (dialog_text, has_more_lines)
        """
        state = self.get_dialog_state(npc_id)

        if not state.conversation_active or not state.current_conversation:
            return "...", False

        state.conversation_step += 1

        if state.conversation_step < len(state.current_conversation):
            return state.current_conversation[state.conversation_step], True
        else:
            # Conversation finished
            self.end_conversation(npc_id)
            return "", False

    def end_conversation(self, npc_id: str) -> None:
        """
        End the current conversation with an NPC.

        Args:
            npc_id: ID of the NPC
        """
        state = self.get_dialog_state(npc_id)
        state.conversation_active = False
        state.current_conversation = []
        state.conversation_step = 0

        # Advance to next dialog set for future interactions
        state.dialog_index = (state.dialog_index + 1)

    def invalidate_conversation(self, npc_id: str) -> None:
        """
        Invalidate the current conversation for an NPC (e.g., when quest state changes).
        This will force dialog resolution on the next interaction.

        Args:
            npc_id: ID of the NPC
        """
        state = self.get_dialog_state(npc_id)
        if state.conversation_active:
            # End current conversation to force re-resolution
            self.end_conversation(npc_id)
            # Reset dialog index so quest dialog can take precedence
            state.dialog_index = 0

    def is_conversation_active(self, npc_id: str) -> bool:
        """
        Check if a conversation is currently active with an NPC.

        Args:
            npc_id: ID of the NPC

        Returns:
            True if conversation is active
        """
        return self.get_dialog_state(npc_id).conversation_active
    
    def reset_dialog(self, npc_id: str) -> None:
        """
        Reset dialog state for an NPC.
        
        Args:
            npc_id: ID of the NPC
        """
        if npc_id in self._npc_dialog_states:
            self._npc_dialog_states[npc_id].dialog_index = 0
    
    def set_custom_data(self, npc_id: str, key: str, value: Any) -> None:
        """
        Set custom data for an NPC's dialog state.
        
        Args:
            npc_id: ID of the NPC
            key: Data key
            value: Data value
        """
        state = self.get_dialog_state(npc_id)
        state.custom_data[key] = value
    
    def get_custom_data(self, npc_id: str, key: str, default: Any = None) -> Any:
        """
        Get custom data from an NPC's dialog state.
        
        Args:
            npc_id: ID of the NPC
            key: Data key
            default: Default value if key not found
            
        Returns:
            Custom data value or default
        """
        state = self.get_dialog_state(npc_id)
        return state.custom_data.get(key, default)
    
    def clear_all_states(self) -> None:
        """Clear all dialog states."""
        self._npc_dialog_states.clear()
    
    def get_state_data(self) -> Dict[str, Dict[str, Any]]:
        """
        Get all dialog state data for saving.

        Returns:
            Dictionary of NPC dialog states
        """
        return {
            npc_id: {
                "dialog_index": state.dialog_index,
                "conversation_active": state.conversation_active,
                "current_conversation": state.current_conversation.copy(),
                "conversation_step": state.conversation_step,
                "custom_data": state.custom_data.copy()
            }
            for npc_id, state in self._npc_dialog_states.items()
        }
    
    def load_state_data(self, state_data: Dict[str, Dict[str, Any]]) -> None:
        """
        Load dialog state data from save.

        Args:
            state_data: Dictionary of NPC dialog states
        """
        self._npc_dialog_states.clear()

        for npc_id, data in state_data.items():
            state = DialogState(
                dialog_index=data.get("dialog_index", 0),
                conversation_active=data.get("conversation_active", False),
                current_conversation=data.get("current_conversation", []).copy(),
                conversation_step=data.get("conversation_step", 0),
                custom_data=data.get("custom_data", {}).copy()
            )
            self._npc_dialog_states[npc_id] = state


# Global dialog manager instance
_dialog_manager: Optional[DialogManager] = None


def get_dialog_manager() -> DialogManager:
    """Get the global dialog manager instance."""
    global _dialog_manager
    if _dialog_manager is None:
        _dialog_manager = DialogManager()
    return _dialog_manager


def initialize_dialog_manager() -> DialogManager:
    """Initialize a new dialog manager instance."""
    global _dialog_manager
    _dialog_manager = DialogManager()
    return _dialog_manager
