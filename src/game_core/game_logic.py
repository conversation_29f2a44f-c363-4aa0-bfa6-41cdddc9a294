"""
Core Game Logic Functions

This module contains pure functions that implement the core game mechanics.
These functions have no side effects and depend only on their inputs.

NO PYGAME IMPORTS ALLOWED IN THIS MODULE.
"""

from typing import List, Optional, Tuple
from .entities import Player, Monster, Position, Stats, Vector2, Direction
from .events import EntityDamagedEvent, EntityDefeatedEvent, PlayerLevelUpEvent
import random
import math


def is_move_valid(current_position: Position, direction: Direction, collision_map: List[List[bool]]) -> bool:
    """
    Check if a move in the given direction is valid.
    
    Args:
        current_position: Current position of the entity
        direction: Direction to move
        collision_map: 2D list where True means blocked, False means passable
    
    Returns:
        True if the move is valid, False otherwise
    """
    new_position = current_position.move(direction)
    
    # Check bounds
    if new_position.y < 0 or new_position.y >= len(collision_map):
        return False
    if new_position.x < 0 or new_position.x >= len(collision_map[0]):
        return False
    
    # Check collision
    return not collision_map[new_position.y][new_position.x]


def calculate_damage(attacker_stats: Stats, defender_stats: Stats, base_damage: int = 0) -> int:
    """
    Calculate damage dealt from attacker to defender.

    Args:
        attacker_stats: Stats of the attacking entity
        defender_stats: Stats of the defending entity
        base_damage: Base damage to add (for weapons, spells, etc.)

    Returns:
        Final damage amount after all calculations
    """
    # Base damage calculation
    raw_damage = attacker_stats.strength + base_damage

    # Apply defender's defense
    final_damage = max(1, raw_damage - defender_stats.defense)

    return final_damage  # Minimum 1 damage


def apply_damage(target_stats: Stats, damage: int) -> Stats:
    """
    Apply damage to an entity's stats.
    
    Args:
        target_stats: Current stats of the target
        damage: Damage amount to apply
    
    Returns:
        New stats with damage applied
    """
    return target_stats.take_damage(damage)


def check_level_up(current_experience: int, current_level: int) -> bool:
    """
    Check if a player should level up based on experience.
    
    Args:
        current_experience: Current experience points
        current_level: Current character level
    
    Returns:
        True if the player should level up
    """
    # Simple formula: next level requires level * 100 experience
    required_experience = current_level * 100
    return current_experience >= required_experience


def calculate_hit_chance(attacker_stats: Stats, defender_stats: Stats) -> float:
    """
    Calculate the chance of an attack hitting.
    
    Args:
        attacker_stats: Stats of the attacking entity
        defender_stats: Stats of the defending entity
    
    Returns:
        Hit chance as a float between 0.0 and 1.0
    """
    # Base hit chance of 75%
    base_chance = 0.75
    
    # Modify based on speed difference
    speed_diff = attacker_stats.speed - defender_stats.speed
    speed_modifier = speed_diff * 0.02  # 2% per speed point difference
    
    final_chance = base_chance + speed_modifier
    return max(0.1, min(0.95, final_chance))  # Clamp between 10% and 95%


def check_hit(attacker_stats: Stats, defender_stats: Stats) -> bool:
    """
    Determine if an attack hits based on stats.

    Args:
        attacker_stats: Stats of the attacking entity
        defender_stats: Stats of the defending entity

    Returns:
        True if the attack hits (always hits for now)
    """
    # For now, attacks always hit to make combat more predictable
    # Could add hit chance logic back later if desired
    return True


def calculate_projectile_path(start_pos: Position, direction_vector: Vector2, max_range: int = 10) -> List[Position]:
    """
    Calculate the path of a projectile given a starting position and direction.
    
    Args:
        start_pos: Starting position of the projectile
        direction_vector: Normalized direction vector
        max_range: Maximum range in tiles
    
    Returns:
        List of positions the projectile travels through
    """
    path = []
    current_x = float(start_pos.x)
    current_y = float(start_pos.y)
    
    for _ in range(max_range):
        current_x += direction_vector.x
        current_y += direction_vector.y
        
        # Round to nearest tile
        tile_x = round(current_x)
        tile_y = round(current_y)
        
        position = Position(tile_x, tile_y)
        if position not in path:  # Avoid duplicates
            path.append(position)
    
    return path


def find_entities_in_area(center: Position, radius: int, entity_positions: List[Tuple[str, Position]]) -> List[str]:
    """
    Find all entities within a circular area.

    Args:
        center: Center position of the area
        radius: Radius of the area in tiles
        entity_positions: List of (entity_id, position) tuples

    Returns:
        List of entity IDs within the area
    """
    entities_in_area = []

    for entity_id, pos in entity_positions:
        distance = math.sqrt((pos.x - center.x) ** 2 + (pos.y - center.y) ** 2)
        if distance <= radius:
            entities_in_area.append(entity_id)

    return entities_in_area


def find_entities_in_attack_arc(
    attacker_pos: Position,
    direction_vector: Vector2,
    weapon_range: float,
    damage_arc: float,
    entity_positions: List[Tuple[str, Position]]
) -> List[str]:
    """
    Find all entities within a weapon's attack arc.

    Args:
        attacker_pos: Position of the attacker
        direction_vector: Normalized direction vector of the attack
        weapon_range: Range of the weapon in tiles
        damage_arc: Total arc angle in degrees
        entity_positions: List of (entity_id, position) tuples

    Returns:
        List of entity IDs within the attack arc
    """
    entities_in_arc = []
    half_arc = damage_arc / 2.0

    # Calculate the attack direction angle in degrees
    attack_angle = math.degrees(math.atan2(direction_vector.y, direction_vector.x))

    for entity_id, pos in entity_positions:
        # Calculate distance to entity
        dx = pos.x - attacker_pos.x
        dy = pos.y - attacker_pos.y
        distance = math.sqrt(dx * dx + dy * dy)

        # Check if entity is within range
        if distance <= weapon_range and distance > 0:  # Don't hit self
            # Calculate angle to entity
            entity_angle = math.degrees(math.atan2(dy, dx))

            # Calculate angle difference, handling wrap-around
            angle_diff = abs(attack_angle - entity_angle)
            if angle_diff > 180:
                angle_diff = 360 - angle_diff

            # Check if entity is within the damage arc
            if angle_diff <= half_arc:
                entities_in_arc.append(entity_id)

    return entities_in_arc


def get_weapon_stats(weapon_properties: dict) -> Tuple[int, float, float, float]:
    """
    Extract weapon combat stats from weapon properties.

    Args:
        weapon_properties: Dictionary of weapon properties

    Returns:
        Tuple of (damage, attack_speed, range, damage_arc)
    """
    damage = weapon_properties.get("damage", 1)
    attack_speed = weapon_properties.get("attack_speed", 1.0)
    weapon_range = weapon_properties.get("range", 1.0)
    damage_arc = weapon_properties.get("damage_arc", 60.0)

    return damage, attack_speed, weapon_range, damage_arc


def find_entities_in_attack_arc_with_hitboxes(
    attacker_pos: Position,
    direction_vector: Vector2,
    weapon_range: float,
    damage_arc: float,
    entity_data: List[Tuple[str, Position, Tuple[int, int]]]
) -> List[str]:
    """
    Find all entities within a weapon's attack arc, considering their hitboxes.

    Args:
        attacker_pos: Position of the attacker
        direction_vector: Normalized direction vector of the attack
        weapon_range: Range of the weapon in pixels
        damage_arc: Total arc angle in degrees
        entity_data: List of (entity_id, position, size) tuples

    Returns:
        List of entity IDs within the attack arc
    """
    entities_in_arc = []
    half_arc = damage_arc / 2.0

    # Calculate the attack direction angle in degrees
    attack_angle = math.degrees(math.atan2(direction_vector.y, direction_vector.x))

    for entity_id, pos, size in entity_data:
        width, height = size

        # Calculate entity's bounding box corners (centered on position)
        half_width = width / 2
        half_height = height / 2
        corners = [
            Position(pos.x - half_width, pos.y - half_height),  # Top-left
            Position(pos.x + half_width, pos.y - half_height),  # Top-right
            Position(pos.x - half_width, pos.y + half_height),  # Bottom-left
            Position(pos.x + half_width, pos.y + half_height),  # Bottom-right
            pos  # Center point
        ]

        # Check if any corner or center of the entity is within the attack arc
        entity_hit = False
        for corner in corners:
            # Calculate distance to corner
            dx = corner.x - attacker_pos.x
            dy = corner.y - attacker_pos.y
            distance = math.sqrt(dx * dx + dy * dy)

            # Check if corner is within range
            if distance <= weapon_range and distance > 0:  # Don't hit self
                # Calculate angle to corner
                corner_angle = math.degrees(math.atan2(dy, dx))

                # Calculate angle difference, handling wrap-around
                angle_diff = abs(attack_angle - corner_angle)
                if angle_diff > 180:
                    angle_diff = 360 - angle_diff

                # Check if corner is within the damage arc
                if angle_diff <= half_arc:
                    entity_hit = True
                    break

        if entity_hit:
            entities_in_arc.append(entity_id)

    return entities_in_arc


def normalize_vector(x: float, y: float) -> Vector2:
    """
    Normalize a 2D vector to unit length.
    
    Args:
        x: X component of the vector
        y: Y component of the vector
    
    Returns:
        Normalized Vector2
    """
    return Vector2(x, y).normalize()


def screen_to_direction_vector(player_screen_pos: Tuple[int, int], mouse_screen_pos: Tuple[int, int]) -> Vector2:
    """
    Convert screen coordinates to a normalized direction vector.
    
    Args:
        player_screen_pos: Player's position on screen (x, y)
        mouse_screen_pos: Mouse position on screen (x, y)
    
    Returns:
        Normalized direction vector pointing from player to mouse
    """
    dx = mouse_screen_pos[0] - player_screen_pos[0]
    dy = mouse_screen_pos[1] - player_screen_pos[1]
    return normalize_vector(dx, dy)
