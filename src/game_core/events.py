"""
Core Game Events

This module defines all events that can occur in the game.
Events are immutable data structures that communicate state changes.

NO PYGAME IMPORTS ALLOWED IN THIS MODULE.
"""

from dataclasses import dataclass
from typing import Any, Dict, Optional
from .entities import Position, Vector2


@dataclass(frozen=True)
class GameEvent:
    """Base class for all game events."""
    event_type: str
    timestamp: float
    data: Dict[str, Any]


@dataclass(frozen=True)
class PlayerMovedEvent(GameEvent):
    """Emitted when the player moves."""
    player_id: str
    old_position: Position
    new_position: Position
    
    def __init__(self, player_id: str, old_position: Position, new_position: Position, timestamp: float):
        object.__setattr__(self, 'event_type', 'player_moved')
        object.__setattr__(self, 'timestamp', timestamp)
        object.__setattr__(self, 'player_id', player_id)
        object.__setattr__(self, 'old_position', old_position)
        object.__setattr__(self, 'new_position', new_position)
        object.__setattr__(self, 'data', {
            'player_id': self.player_id,
            'old_position': self.old_position,
            'new_position': self.new_position
        })


@dataclass(frozen=True)
class PlayerAttackedEvent(GameEvent):
    """Emitted when the player attacks."""
    player_id: str
    direction_vector: Vector2
    attack_power: int
    
    def __init__(self, player_id: str, direction_vector: Vector2, attack_power: int, timestamp: float):
        object.__setattr__(self, 'event_type', 'player_attacked')
        object.__setattr__(self, 'timestamp', timestamp)
        object.__setattr__(self, 'player_id', player_id)
        object.__setattr__(self, 'direction_vector', direction_vector)
        object.__setattr__(self, 'attack_power', attack_power)
        object.__setattr__(self, 'data', {
            'player_id': self.player_id,
            'direction_vector': self.direction_vector,
            'attack_power': self.attack_power
        })


@dataclass(frozen=True)
class EntityDamagedEvent(GameEvent):
    """Emitted when any entity takes damage."""
    target_id: str
    damage: int
    damage_type: str = "physical"
    source_id: Optional[str] = None
    
    def __init__(self, target_id: str, damage: int, timestamp: float, 
                 damage_type: str = "physical", source_id: Optional[str] = None):
        object.__setattr__(self, 'event_type', 'entity_damaged')
        object.__setattr__(self, 'timestamp', timestamp)
        object.__setattr__(self, 'target_id', target_id)
        object.__setattr__(self, 'damage', damage)
        object.__setattr__(self, 'damage_type', damage_type)
        object.__setattr__(self, 'source_id', source_id)
        object.__setattr__(self, 'data', {
            'target_id': self.target_id,
            'damage': self.damage,
            'damage_type': self.damage_type,
            'source_id': self.source_id
        })


@dataclass(frozen=True)
class EntityDefeatedEvent(GameEvent):
    """Emitted when an entity is defeated (HP reaches 0)."""
    entity_id: str
    entity_type: str
    killer_id: Optional[str] = None
    
    def __init__(self, entity_id: str, entity_type: str, timestamp: float, killer_id: Optional[str] = None):
        object.__setattr__(self, 'event_type', 'entity_defeated')
        object.__setattr__(self, 'timestamp', timestamp)
        object.__setattr__(self, 'entity_id', entity_id)
        object.__setattr__(self, 'entity_type', entity_type)
        object.__setattr__(self, 'killer_id', killer_id)
        object.__setattr__(self, 'data', {
            'entity_id': self.entity_id,
            'entity_type': self.entity_type,
            'killer_id': self.killer_id
        })


@dataclass(frozen=True)
class PlayerLevelUpEvent(GameEvent):
    """Emitted when the player levels up."""
    player_id: str
    old_level: int
    new_level: int
    
    def __init__(self, player_id: str, old_level: int, new_level: int, timestamp: float):
        object.__setattr__(self, 'event_type', 'player_level_up')
        object.__setattr__(self, 'timestamp', timestamp)
        object.__setattr__(self, 'player_id', player_id)
        object.__setattr__(self, 'old_level', old_level)
        object.__setattr__(self, 'new_level', new_level)
        object.__setattr__(self, 'data', {
            'player_id': self.player_id,
            'old_level': self.old_level,
            'new_level': self.new_level
        })


@dataclass(frozen=True)
class ItemPickedUpEvent(GameEvent):
    """Emitted when an item is picked up."""
    player_id: str
    item_id: str
    item_type: str
    
    def __init__(self, player_id: str, item_id: str, item_type: str, timestamp: float):
        object.__setattr__(self, 'event_type', 'item_picked_up')
        object.__setattr__(self, 'timestamp', timestamp)
        object.__setattr__(self, 'player_id', player_id)
        object.__setattr__(self, 'item_id', item_id)
        object.__setattr__(self, 'item_type', item_type)
        object.__setattr__(self, 'data', {
            'player_id': self.player_id,
            'item_id': self.item_id,
            'item_type': self.item_type
        })


@dataclass(frozen=True)
class LevelLoadedEvent(GameEvent):
    """Emitted when a new level is loaded."""
    level_id: str
    level_name: str
    
    def __init__(self, level_id: str, level_name: str, timestamp: float):
        object.__setattr__(self, 'event_type', 'level_loaded')
        object.__setattr__(self, 'timestamp', timestamp)
        object.__setattr__(self, 'level_id', level_id)
        object.__setattr__(self, 'level_name', level_name)
        object.__setattr__(self, 'data', {
            'level_id': self.level_id,
            'level_name': self.level_name
        })


@dataclass(frozen=True)
class LevelTransitionEvent(GameEvent):
    """Emitted when the player triggers a level transition."""
    player_id: str
    current_level_id: str
    target_level_id: str
    exit_number: str
    spawn_point: str

    def __init__(self, player_id: str, current_level_id: str, target_level_id: str,
                 exit_number: str, spawn_point: str, timestamp: float):
        object.__setattr__(self, 'event_type', 'level_transition')
        object.__setattr__(self, 'timestamp', timestamp)
        object.__setattr__(self, 'player_id', player_id)
        object.__setattr__(self, 'current_level_id', current_level_id)
        object.__setattr__(self, 'target_level_id', target_level_id)
        object.__setattr__(self, 'exit_number', exit_number)
        object.__setattr__(self, 'spawn_point', spawn_point)
        object.__setattr__(self, 'data', {
            'player_id': self.player_id,
            'current_level_id': self.current_level_id,
            'target_level_id': self.target_level_id,
            'exit_number': self.exit_number,
            'spawn_point': self.spawn_point
        })


@dataclass(frozen=True)
class TileInteractionEvent(GameEvent):
    """Emitted when a player interacts with a tile."""
    tile_x: int
    tile_y: int
    tile_type: str
    new_state: Dict[str, Any]

    def __init__(self, tile_x: int, tile_y: int, tile_type: str, new_state: Dict[str, Any], timestamp: float):
        object.__setattr__(self, 'event_type', 'tile_interaction')
        object.__setattr__(self, 'timestamp', timestamp)
        object.__setattr__(self, 'tile_x', tile_x)
        object.__setattr__(self, 'tile_y', tile_y)
        object.__setattr__(self, 'tile_type', tile_type)
        object.__setattr__(self, 'new_state', new_state)
        object.__setattr__(self, 'data', {
            'tile_x': self.tile_x,
            'tile_y': self.tile_y,
            'tile_type': self.tile_type,
            'new_state': self.new_state
        })


@dataclass(frozen=True)
class QuestStartedEvent(GameEvent):
    """Emitted when a quest is started."""
    quest_id: str
    quest_name: str
    quest_description: str

    def __init__(self, quest_id: str, quest_name: str, quest_description: str, timestamp: float):
        object.__setattr__(self, 'event_type', 'quest_started')
        object.__setattr__(self, 'timestamp', timestamp)
        object.__setattr__(self, 'quest_id', quest_id)
        object.__setattr__(self, 'quest_name', quest_name)
        object.__setattr__(self, 'quest_description', quest_description)
        object.__setattr__(self, 'data', {
            'quest_id': self.quest_id,
            'quest_name': self.quest_name,
            'quest_description': self.quest_description
        })


@dataclass(frozen=True)
class ObjectiveCompletedEvent(GameEvent):
    """Emitted when a quest objective is completed."""
    quest_id: str
    objective_id: str
    quest_name: str

    def __init__(self, quest_id: str, objective_id: str, quest_name: str, timestamp: float):
        object.__setattr__(self, 'event_type', 'objective_completed')
        object.__setattr__(self, 'timestamp', timestamp)
        object.__setattr__(self, 'quest_id', quest_id)
        object.__setattr__(self, 'objective_id', objective_id)
        object.__setattr__(self, 'quest_name', quest_name)
        object.__setattr__(self, 'data', {
            'quest_id': self.quest_id,
            'objective_id': self.objective_id,
            'quest_name': self.quest_name
        })


@dataclass(frozen=True)
class QuestCompletedEvent(GameEvent):
    """Emitted when a quest is completed."""
    quest_id: str
    quest_name: str
    completion_time: float

    def __init__(self, quest_id: str, quest_name: str, completion_time: float, timestamp: float):
        object.__setattr__(self, 'event_type', 'quest_completed')
        object.__setattr__(self, 'timestamp', timestamp)
        object.__setattr__(self, 'quest_id', quest_id)
        object.__setattr__(self, 'quest_name', quest_name)
        object.__setattr__(self, 'completion_time', completion_time)
        object.__setattr__(self, 'data', {
            'quest_id': self.quest_id,
            'quest_name': self.quest_name,
            'completion_time': self.completion_time
        })


@dataclass(frozen=True)
class QuestFailedEvent(GameEvent):
    """Emitted when a quest fails."""
    quest_id: str
    quest_name: str

    def __init__(self, quest_id: str, quest_name: str, timestamp: float):
        object.__setattr__(self, 'event_type', 'quest_failed')
        object.__setattr__(self, 'timestamp', timestamp)
        object.__setattr__(self, 'quest_id', quest_id)
        object.__setattr__(self, 'quest_name', quest_name)
        object.__setattr__(self, 'data', {
            'quest_id': self.quest_id,
            'quest_name': self.quest_name
        })


@dataclass(frozen=True)
class QuestClosedEvent(GameEvent):
    """Emitted when a quest is closed (after rewards are distributed)."""
    quest_id: str
    quest_name: str

    def __init__(self, quest_id: str, quest_name: str, timestamp: float):
        object.__setattr__(self, 'event_type', 'quest_closed')
        object.__setattr__(self, 'timestamp', timestamp)
        object.__setattr__(self, 'quest_id', quest_id)
        object.__setattr__(self, 'quest_name', quest_name)
        object.__setattr__(self, 'data', {
            'quest_id': self.quest_id,
            'quest_name': self.quest_name
        })
