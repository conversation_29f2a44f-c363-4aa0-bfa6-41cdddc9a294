"""
Game Core Module

This module contains the pure game logic, entities, and events.
It has no dependencies on external frameworks like Pygame.
"""

# Re-export all public APIs for easy importing
from .entities import (
    Direction,
    Position,
    Vector2,
    Stats,
    Player,
    Monster,
    Item,
    NPC,
    Tile,
)

from .wander_system import (
    WanderBehavior,
    WanderState,
    AIBehavior,
    AIState,
    update_wandering_entities,
    get_monsters_ready_to_attack,
)

from .events import (
    GameEvent,
    PlayerMovedEvent,
    PlayerAttackedEvent,
    EntityDamagedEvent,
    EntityDefeatedEvent,
    PlayerLevelUpEvent,
    ItemPickedUpEvent,
    LevelLoadedEvent,
    LevelTransitionEvent,
    QuestStartedEvent,
    ObjectiveCompletedEvent,
    QuestCompletedEvent,
    QuestFailedEvent,
)

# Quest manager imports moved to avoid circular imports
# Import directly: from src.game_core.quest_manager import GlobalQuestManager, QuestState

from .game_logic import (
    is_move_valid,
    calculate_damage,
    apply_damage,
    check_level_up,
    calculate_hit_chance,
    check_hit,
    calculate_projectile_path,
    find_entities_in_area,
    find_entities_in_attack_arc,
    find_entities_in_attack_arc_with_hitboxes,
    get_weapon_stats,
    normalize_vector,
    screen_to_direction_vector,
)

from .animation import (
    PlayerAnimationState,
    AnimationTransform,
    WeaponAnimationTransform,
    CombinedAnimationTransform,
    calculate_animation_progress,
    calculate_swing_rotation,
    calculate_vibration_offset,
    calculate_weapon_swing_rotation,
    calculate_stabbing_offset,
    calculate_animation_transform,
    calculate_weapon_animation_transform,
    calculate_combined_animation_transform,
    is_animation_active,
)

__all__ = [
    # Entities
    "Direction",
    "Position",
    "Vector2",
    "Stats",
    "Player",
    "Monster",
    "Item",
    "NPC",
    "Tile",
    # Events
    "GameEvent",
    "PlayerMovedEvent",
    "PlayerAttackedEvent",
    "EntityDamagedEvent",
    "EntityDefeatedEvent",
    "PlayerLevelUpEvent",
    "ItemPickedUpEvent",
    "LevelLoadedEvent",
    "LevelTransitionEvent",
    # Game Logic
    "is_move_valid",
    "calculate_damage",
    "apply_damage",
    "check_level_up",
    "calculate_hit_chance",
    "check_hit",
    "calculate_projectile_path",
    "find_entities_in_area",
    "find_entities_in_attack_arc",
    "find_entities_in_attack_arc_with_hitboxes",
    "get_weapon_stats",
    "normalize_vector",
    "screen_to_direction_vector",
    # Animation System
    "PlayerAnimationState",
    "AnimationTransform",
    "WeaponAnimationTransform",
    "CombinedAnimationTransform",
    "calculate_animation_progress",
    "calculate_swing_rotation",
    "calculate_vibration_offset",
    "calculate_weapon_swing_rotation",
    "calculate_stabbing_offset",
    "calculate_animation_transform",
    "calculate_weapon_animation_transform",
    "calculate_combined_animation_transform",
    "is_animation_active",
    # Wander System
    "WanderBehavior",
    "WanderState",
    "update_wandering_entities",
]
