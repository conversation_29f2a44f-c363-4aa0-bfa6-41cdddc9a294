"""
Game Data Module

This module contains all shared game data definitions.
These are pure data structures with no framework dependencies.
"""

from .monsters import (
    MonsterDefinition,
    MONSTERS,
    get_monster_definition,
    get_all_monster_ids,
)

from .items import (
    ItemType,
    ItemDefinition,
    ITEMS,
    get_item_definition,
    get_all_item_ids,
    get_items_by_type,
)

from .npcs import (
    NPCDefinition,
    NPC_TYPES,
    NPC_DATA,
    get_npc_definition,
    get_all_npc_types,
    get_store_npc_types,
    get_dialog_npc_types,
    get_npc_data_id_mapping,
    get_all_npc_data_ids,
)

__all__ = [
    # Monsters
    "MonsterDefinition",
    "MONSTERS",
    "get_monster_definition",
    "get_all_monster_ids",
    # Items
    "ItemType",
    "ItemDefinition",
    "ITEMS",
    "get_item_definition",
    "get_all_item_ids",
    "get_items_by_type",
    # NPCs
    "NPCDefinition",
    "NPC_TYPES",
    "NPC_DATA",
    "get_npc_definition",
    "get_all_npc_types",
    "get_store_npc_types",
    "get_dialog_npc_types",
    "get_npc_data_id_mapping",
    "get_all_npc_data_ids",
]
