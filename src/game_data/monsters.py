"""
Monster Definitions

This file contains all monster data definitions as pure data.
NO PYGAME IMPORTS - this is framework-agnostic game data.
"""

from pydantic import BaseModel
from typing import Dict, Optional, List


class LootEntry(BaseModel):
    """Definition for a single loot entry."""
    item_id: str
    min_quantity: int = 1
    max_quantity: int = 1
    drop_chance: float = 1.0  # 0.0 to 1.0


class MonsterDefinition(BaseModel):
    """Definition for a monster type."""
    id: str
    name: str
    asset_id: str  # Links to asset generation function
    max_hp: int
    max_mp: int
    strength: int
    defense: int
    speed: int
    ai_behavior: str = "aggressive"
    experience_reward: int = 10
    description: Optional[str] = None
    humanoid: bool = False  # Whether the entity is humanoid (affects AI, equipment, etc.)
    aggressive: bool = False  # Whether the entity is aggressive by default
    loot_table: List[LootEntry] = []  # Items this monster can drop when defeated


# Monster definitions - the game's bestiary
MONSTERS: Dict[str, MonsterDefinition] = {
    "goblin_grunt": MonsterDefinition(
        id="goblin_grunt",
        name="Goblin Grunt",
        asset_id="monster.goblin.grunt.side",
        max_hp=25,
        max_mp=0,
        strength=6,
        defense=2,
        speed=7,
        ai_behavior="aggressive",
        experience_reward=15,
        description="A small, green-skinned humanoid with crude weapons and a bad attitude.",
        humanoid=True,
        aggressive=True,
        loot_table=[
            LootEntry(item_id="gold_coin", min_quantity=2, max_quantity=8, drop_chance=1.0)
        ]
    ),
    
    "goblin_shaman": MonsterDefinition(
        id="goblin_shaman",
        name="Goblin Shaman",
        asset_id="monster.goblin.shaman.side",
        max_hp=35,
        max_mp=40,
        strength=4,
        defense=3,
        speed=5,
        ai_behavior="aggressive",
        experience_reward=25,
        description="A goblin wielding dark magic and wearing bone trinkets.",
        humanoid=True,
        aggressive=True,
        loot_table=[
            LootEntry(item_id="gold_coin", min_quantity=5, max_quantity=15, drop_chance=1.0)
        ]
    ),
    
    "green_drake": MonsterDefinition(
        id="green_drake",
        name="Green Drake",
        asset_id="monster.drake.green.side",
        max_hp=80,
        max_mp=30,
        strength=12,
        defense=8,
        speed=6,
        ai_behavior="aggressive",
        experience_reward=50,
        description="A fierce dragon-like creature with emerald scales and razor-sharp claws.",
        humanoid=False,
        aggressive=True,
        loot_table=[
            LootEntry(item_id="meat", min_quantity=2, max_quantity=4, drop_chance=0.8),
            LootEntry(item_id="leather", min_quantity=1, max_quantity=3, drop_chance=0.6)
        ]
    ),
    
    "sand_scorpion": MonsterDefinition(
        id="sand_scorpion",
        name="Sand Scorpion",
        asset_id="monster.scorpion.sand.side",
        max_hp=45,
        max_mp=0,
        strength=10,
        defense=6,
        speed=8,
        ai_behavior="aggressive",
        experience_reward=30,
        description="A large arachnid that burrows in desert sand, striking with venomous stinger.",
        humanoid=False,
        aggressive=True,
        loot_table=[
            LootEntry(item_id="meat", min_quantity=1, max_quantity=2, drop_chance=0.5)
        ]
    ),
    
    "ice_wolf": MonsterDefinition(
        id="ice_wolf",
        name="Ice Wolf",
        asset_id="monster.wolf.ice.side",
        max_hp=55,
        max_mp=20,
        strength=11,
        defense=4,
        speed=12,
        ai_behavior="aggressive",
        experience_reward=35,
        description="A wolf with frost-covered fur that can breathe freezing wind.",
        humanoid=False,
        aggressive=True,
        loot_table=[
            LootEntry(item_id="meat", min_quantity=1, max_quantity=3, drop_chance=0.7),
            LootEntry(item_id="leather", min_quantity=1, max_quantity=2, drop_chance=0.5)
        ]
    ),
    
    "forest_troll": MonsterDefinition(
        id="forest_troll",
        name="Forest Troll",
        asset_id="monster.troll.forest.side",
        max_hp=120,
        max_mp=0,
        strength=15,
        defense=10,
        speed=4,
        ai_behavior="aggressive",
        experience_reward=75,
        description="A massive, moss-covered humanoid that regenerates wounds over time.",
        humanoid=True,
        aggressive=True,
        loot_table=[
            LootEntry(item_id="gold_coin", min_quantity=10, max_quantity=25, drop_chance=1.0)
        ]
    ),
    
    # Wandering Animals - Peaceful creatures that roam the land
    "horse": MonsterDefinition(
        id="horse",
        name="Wild Horse",
        asset_id="animal.horse.brown.side",
        max_hp=12,
        max_mp=0,
        strength=8,
        defense=0,  # Animals have no armor
        speed=15,
        ai_behavior="peaceful",
        experience_reward=5,
        description="A majestic wild horse with flowing mane, roaming freely across the plains.",
        humanoid=False,
        aggressive=False,
        loot_table=[
            LootEntry(item_id="meat", min_quantity=2, max_quantity=4, drop_chance=0.9),
            LootEntry(item_id="leather", min_quantity=1, max_quantity=3, drop_chance=0.8)
        ]
    ),

    "cow": MonsterDefinition(
        id="cow",
        name="Cow",
        asset_id="animal.cow.spotted.side",
        max_hp=15,
        max_mp=0,
        strength=5,
        defense=0,  # Animals have no armor
        speed=6,
        ai_behavior="peaceful",
        experience_reward=3,
        description="A gentle dairy cow that wanders the pastures, occasionally mooing contentedly.",
        humanoid=False,
        aggressive=False,
        loot_table=[
            LootEntry(item_id="meat", min_quantity=2, max_quantity=5, drop_chance=0.95),
            LootEntry(item_id="leather", min_quantity=1, max_quantity=2, drop_chance=0.7)
        ]
    ),
    
    "chicken": MonsterDefinition(
        id="chicken",
        name="Chicken",
        asset_id="animal.chicken.white.side",
        max_hp=5,
        max_mp=0,
        strength=2,
        defense=0,  # Animals have no armor
        speed=12,
        ai_behavior="peaceful",
        experience_reward=1,
        description="A plump chicken that pecks at the ground and flaps about nervously when approached.",
        humanoid=False,
        aggressive=False,
        loot_table=[
            LootEntry(item_id="meat", min_quantity=1, max_quantity=2, drop_chance=0.8)
        ]
    ),

    "deer": MonsterDefinition(
        id="deer",
        name="Deer",
        asset_id="animal.deer.brown.side",
        max_hp=8,
        max_mp=0,
        strength=4,
        defense=0,  # Animals have no armor
        speed=18,
        ai_behavior="skittish",
        experience_reward=4,
        description="An elegant deer with graceful antlers, alert and ready to bound away at any disturbance.",
        humanoid=False,
        aggressive=False,
        loot_table=[
            LootEntry(item_id="meat", min_quantity=1, max_quantity=3, drop_chance=1.0),
            LootEntry(item_id="leather", min_quantity=1, max_quantity=2, drop_chance=1.0)
        ]
    ),

    "pig": MonsterDefinition(
        id="pig",
        name="Pig",
        asset_id="animal.pig.pink.side",
        max_hp=10,
        max_mp=0,
        strength=6,
        defense=0,  # Animals have no armor
        speed=8,
        ai_behavior="peaceful",
        experience_reward=2,
        description="A portly pig that snorts and roots around in the dirt looking for truffles and acorns.",
        humanoid=False,
        aggressive=False,
        loot_table=[
            LootEntry(item_id="meat", min_quantity=1, max_quantity=3, drop_chance=0.9),
            LootEntry(item_id="leather", min_quantity=1, max_quantity=1, drop_chance=0.4)
        ]
    ),

    "dog": MonsterDefinition(
        id="dog",
        name="Stray Dog",
        asset_id="animal.dog.brown.side",
        max_hp=7,
        max_mp=0,
        strength=7,
        defense=0,  # Animals have no armor
        speed=14,
        ai_behavior="peaceful",
        experience_reward=3,
        description="A friendly stray dog with a wagging tail, hoping for some food or companionship.",
        humanoid=False,
        aggressive=False,
        loot_table=[
            LootEntry(item_id="meat", min_quantity=1, max_quantity=2, drop_chance=0.7),
            LootEntry(item_id="leather", min_quantity=1, max_quantity=1, drop_chance=0.3)
        ]
    ),

    # New monsters - Wolf and Goblin
    "wolf": MonsterDefinition(
        id="wolf",
        name="Wolf",
        asset_id="animal.wolf.gray.side",
        max_hp=30,
        max_mp=0,
        strength=9,
        defense=2,
        speed=14,
        ai_behavior="aggressive",
        experience_reward=20,
        description="A fierce wild wolf with piercing yellow eyes and powerful jaws.",
        humanoid=False,
        aggressive=True,
        loot_table=[
            LootEntry(item_id="meat", min_quantity=1, max_quantity=3, drop_chance=0.8),
            LootEntry(item_id="leather", min_quantity=1, max_quantity=2, drop_chance=0.6)
        ]
    ),

    "goblin": MonsterDefinition(
        id="goblin",
        name="Goblin",
        asset_id="monster.goblin.basic.side",
        max_hp=20,
        max_mp=0,
        strength=5,
        defense=1,
        speed=8,
        ai_behavior="aggressive",
        experience_reward=12,
        description="A small, mischievous green humanoid with sharp teeth and beady eyes.",
        humanoid=True,
        aggressive=True,
        loot_table=[
            LootEntry(item_id="gold_coin", min_quantity=1, max_quantity=6, drop_chance=1.0)
        ]
    ),

    "spider": MonsterDefinition(
        id="spider",
        name="Forest Spider",
        asset_id="monster.spider.forest.side",
        max_hp=35,
        max_mp=0,
        strength=8,
        defense=3,
        speed=12,
        ai_behavior="aggressive",
        experience_reward=25,
        description="A large forest spider with venomous fangs and quick reflexes. It lurks in dark corners of the woods.",
        humanoid=False,
        aggressive=True,
        loot_table=[
            LootEntry(item_id="meat", min_quantity=1, max_quantity=2, drop_chance=0.4)
        ]
    ),
}


def get_monster_definition(monster_id: str) -> Optional[MonsterDefinition]:
    """Get a monster definition by ID."""
    return MONSTERS.get(monster_id)


def get_all_monster_ids() -> list[str]:
    """Get a list of all available monster IDs."""
    return list(MONSTERS.keys())


def generate_loot(monster_id: str) -> Dict[str, int]:
    """
    Generate loot from a monster's loot table.

    Args:
        monster_id: The ID of the monster that was defeated

    Returns:
        Dictionary mapping item_id to quantity for generated loot
    """
    import random

    monster_def = get_monster_definition(monster_id)
    if not monster_def or not monster_def.loot_table:
        return {}

    loot = {}

    for loot_entry in monster_def.loot_table:
        # Check if this item drops based on drop chance
        if random.random() <= loot_entry.drop_chance:
            # Generate quantity within the specified range
            quantity = random.randint(loot_entry.min_quantity, loot_entry.max_quantity)

            # Add to loot (stack if item already exists)
            if loot_entry.item_id in loot:
                loot[loot_entry.item_id] += quantity
            else:
                loot[loot_entry.item_id] = quantity

    return loot
