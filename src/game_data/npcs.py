"""
NPC Definitions

This file contains all NPC data definitions as pure data.
NO PYGAME IMPORTS - this is framework-agnostic game data.
"""

from pydantic import BaseModel
from typing import Dict, List, Optional
from enum import Enum


class DialogMode(str, Enum):
    """Dialog mode for NPCs."""
    SEQUENTIAL = "sequential"  # Progress through dialog in order (current behavior)
    RANDOM = "random"  # Select random dialog from available options


class NPCDefinition(BaseModel):
    """Definition for an NPC type."""
    id: str
    name: str
    asset_id: str  # Links to asset generation function
    npc_type: str  # merchant, armourer, weaponsmith, innkeeper, commoner, guard, mayor
    behavior: str  # store, dialog
    default_dialog: List[str]  # Dialog options
    dialog_mode: DialogMode = DialogMode.SEQUENTIAL  # How dialog is selected
    default_inventory: List[str] = []  # List of item IDs for store NPCs
    description: Optional[str] = None


# NPC type definitions with defaults
NPC_TYPES: Dict[str, NPCDefinition] = {
    "merchant": NPCDefinition(
        id="merchant",
        name="Merchant",
        asset_id="npc.merchant.general",
        npc_type="merchant",
        behavior="store",
        default_dialog=[
            "Welcome to my shop! I have the finest goods in the land.",
            "Looking for something special? I might have just what you need.",
            "Trade is the lifeblood of any town. What can I get for you?",
            "I've traveled far and wide to bring you these wares.",
            "Every item here has been carefully selected for quality.",
            "Business has been good lately, thanks to adventurers like you!"
        ],
        default_inventory=["health_potion", "mana_potion", "bread", "gold_coin", "ruby_gem"],
        description="A traveling merchant with a wide variety of goods."
    ),
    
    "armourer": NPCDefinition(
        id="armourer",
        name="Armourer",
        asset_id="npc.armourer.town",
        npc_type="armourer",
        behavior="store",
        default_dialog=[
            "Quality armor saves lives. Let me outfit you properly.",
            "A warrior is only as good as their protection. What do you need?",
            "I've been crafting armor for decades. Trust my expertise.",
            "Each piece is tested for durability and comfort.",
            "Don't skimp on protection - your life depends on it.",
            "I use only the finest materials in my work."
        ],
        default_inventory=["leather_boots", "leather_cap", "leather_boots", "leather_armor", "wooden_shield"],
        description="A skilled craftsman specializing in protective gear."
    ),
    
    "weaponsmith": NPCDefinition(
        id="weaponsmith",
        name="Weaponsmith",
        asset_id="npc.weaponsmith.forge",
        npc_type="weaponsmith",
        behavior="store",
        default_dialog=[
            "Sharp steel and true aim - that's what wins battles.",
            "Every weapon I forge is tested personally. Quality guaranteed.",
            "Looking for a blade? I have the finest steel in the realm.",
            "The forge burns hot today - perfect for crafting.",
            "A dull blade is worse than no blade at all.",
            "My weapons have served warriors for generations."
        ],
        default_inventory=["iron_sword", "steel_dagger", "wooden_bow", "battle_axe"],
        description="A master smith who forges the finest weapons."
    ),
    
    "innkeeper": NPCDefinition(
        id="innkeeper",
        name="Innkeeper",
        asset_id="npc.innkeeper.tavern",
        npc_type="innkeeper",
        behavior="store",
        default_dialog=[
            "Welcome to my inn! Food, drink, and a warm bed await.",
            "Travelers need good food and rest. I provide both.",
            "Pull up a chair! What can I get you?",
            "The stew is fresh and the ale is cold today.",
            "Many adventurers have rested here before continuing their journeys.",
            "A good night's sleep makes all the difference on the road."
        ],
        default_inventory=["bread", "ale", "cheese", "room_key", "hot_meal"],
        description="A friendly host who provides food and lodging."
    ),
    
    "commoner": NPCDefinition(
        id="commoner",
        name="Commoner",
        asset_id="npc.commoner.citizen",
        npc_type="commoner",
        behavior="dialog",
        default_dialog=[
            "Good day to you, traveler.",
            "The weather's been quite pleasant lately.",
            "I hope you're enjoying your stay in our town.",
            "Safe travels on your journey.",
            "Life in this town is peaceful and quiet.",
            "We don't get many visitors like yourself.",
            "The harvest has been good this year."
        ],
        dialog_mode=DialogMode.RANDOM,
        description="A regular citizen going about their daily life."
    ),

    "guard": NPCDefinition(
        id="guard",
        name="Guard",
        asset_id="npc.guard.town",
        npc_type="guard",
        behavior="dialog",
        default_dialog=[
            "Keep the peace, citizen.",
            "Nothing suspicious to report today.",
            "The town is secure under our watch.",
            "Move along, nothing to see here.",
            "We patrol these streets day and night.",
            "Any trouble, you come find me immediately.",
            "The safety of our citizens is our top priority."
        ],
        dialog_mode=DialogMode.RANDOM,
        description="A town guard maintaining law and order."
    ),

    "mayor": NPCDefinition(
        id="mayor",
        name="Mayor",
        asset_id="npc.mayor.formal",
        npc_type="mayor",
        behavior="dialog",
        default_dialog=[
            "Welcome to our town, traveler!",
            "I hope you find our community to your liking.",
            "If you need anything, please don't hesitate to ask.",
            "We pride ourselves on being a safe and welcoming place.",
            "Our town has a long and proud history.",
            "The people here work hard and look out for each other.",
            "May your stay with us be pleasant and peaceful."
        ],
        description="The town's mayor, a wise and respected leader."
    )
}


def get_npc_definition(npc_type: str) -> Optional[NPCDefinition]:
    """Get NPC definition by type."""
    return NPC_TYPES.get(npc_type)


def get_all_npc_types() -> List[str]:
    """Get list of all available NPC types."""
    return list(NPC_TYPES.keys())


def get_store_npc_types() -> List[str]:
    """Get list of NPC types that have store behavior."""
    return [npc_type for npc_type, definition in NPC_TYPES.items() 
            if definition.behavior == "store"]


def get_dialog_npc_types() -> List[str]:
    """Get list of NPC types that have dialog behavior."""
    return [npc_type for npc_type, definition in NPC_TYPES.items()
            if definition.behavior == "dialog"]


# NPC data definitions for map system (similar to monsters.py)
# These map data_id values from base_legend.yaml to NPC types
NPC_DATA: Dict[str, str] = {
    "merchant": "merchant",
    "armourer": "armourer",
    "weaponsmith": "weaponsmith",
    "innkeeper": "innkeeper",
    "commoner": "commoner",
    "guard": "guard",
    "mayor": "mayor"
}


def get_npc_data_id_mapping(data_id: str) -> Optional[str]:
    """Get NPC type from data_id used in maps."""
    return NPC_DATA.get(data_id)


def get_all_npc_data_ids() -> List[str]:
    """Get list of all available NPC data IDs for maps."""
    return list(NPC_DATA.keys())
