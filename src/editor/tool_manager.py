"""
Tool Manager

This module manages the editing tools and handles tool switching and events.
"""

import pygame
from typing import Optional, Dict, Tuple, List, Any

from .tools import BaseTool, ToolType, PaintTool, SelectionTool, TransitionTool
from .map_operations import EditorMapData
from src.infrastructure.logging import get_logger


class ToolManager:
    """
    Manages editing tools and handles tool switching and events.
    
    This class coordinates between different tools and provides a unified
    interface for tool operations.
    """
    
    def __init__(self):
        """Initialize the tool manager."""
        self.logger = get_logger(__name__)
        
        # Initialize tools
        self.tools: Dict[ToolType, BaseTool] = {
            ToolType.SELECTION: SelectionTool(),
            ToolType.PAINT: PaintTool(),
            ToolType.TRANSITION: TransitionTool(),
        }
        
        # Current active tool
        self.active_tool: Optional[BaseTool] = self.tools[ToolType.SELECTION]
        self.active_tool.activate()
        
        # Tool state
        self.current_asset_symbol: Optional[str] = None
        self.current_asset_definition: Optional[Dict[str, Any]] = None
    
    def set_active_tool(self, tool_type: ToolType) -> None:
        """
        Set the active tool.
        
        Args:
            tool_type: Type of tool to activate
        """
        if tool_type not in self.tools:
            self.logger.warning(f"Unknown tool type: {tool_type}")
            return
        
        # Deactivate current tool
        if self.active_tool:
            self.active_tool.deactivate()
        
        # Activate new tool
        self.active_tool = self.tools[tool_type]
        self.active_tool.activate()
        
        # If switching to paint tool, set current asset
        if tool_type == ToolType.PAINT and self.current_asset_symbol:
            paint_tool = self.active_tool
            if isinstance(paint_tool, PaintTool):
                paint_tool.set_current_asset(self.current_asset_symbol, self.current_asset_definition)
        
        self.logger.info(f"Switched to tool: {self.active_tool.name}")
    
    def get_active_tool(self) -> Optional[BaseTool]:
        """Get the currently active tool."""
        return self.active_tool
    
    def get_active_tool_type(self) -> Optional[ToolType]:
        """Get the type of the currently active tool."""
        return self.active_tool.tool_type if self.active_tool else None
    
    def set_current_asset(self, symbol: str, definition: Dict[str, Any]) -> None:
        """
        Set the current asset for tools that use it.
        
        Args:
            symbol: Asset symbol
            definition: Asset definition
        """
        self.current_asset_symbol = symbol
        self.current_asset_definition = definition
        
        # Update paint tool if it's active
        if isinstance(self.active_tool, PaintTool):
            self.active_tool.set_current_asset(symbol, definition)
    
    def handle_mouse_down(self, tile_pos: Tuple[int, int], button: int, 
                         map_data: Optional[EditorMapData]) -> bool:
        """
        Handle mouse button down event.
        
        Args:
            tile_pos: Mouse position in tile coordinates
            button: Mouse button (1=left, 2=middle, 3=right)
            map_data: Current map data
            
        Returns:
            True if the event was handled and map was modified
        """
        if not self.active_tool:
            return False
        
        return self.active_tool.handle_mouse_down(tile_pos, button, map_data)
    
    def handle_mouse_up(self, tile_pos: Tuple[int, int], button: int,
                       map_data: Optional[EditorMapData]) -> bool:
        """
        Handle mouse button up event.
        
        Args:
            tile_pos: Mouse position in tile coordinates
            button: Mouse button (1=left, 2=middle, 3=right)
            map_data: Current map data
            
        Returns:
            True if the event was handled and map was modified
        """
        if not self.active_tool:
            return False
        
        return self.active_tool.handle_mouse_up(tile_pos, button, map_data)
    
    def handle_mouse_move(self, tile_pos: Tuple[int, int], 
                         map_data: Optional[EditorMapData]) -> bool:
        """
        Handle mouse move event.
        
        Args:
            tile_pos: Mouse position in tile coordinates
            map_data: Current map data
            
        Returns:
            True if the event was handled and map was modified
        """
        if not self.active_tool:
            return False
        
        # Update cursor position
        self.active_tool.update_cursor(tile_pos)
        
        return self.active_tool.handle_mouse_move(tile_pos, map_data)
    
    def handle_key_down(self, key: int, map_data: Optional[EditorMapData]) -> bool:
        """
        Handle key down event.
        
        Args:
            key: Pygame key constant
            map_data: Current map data
            
        Returns:
            True if the event was handled and map was modified
        """
        # Handle tool switching shortcuts
        if key == pygame.K_s:
            self.set_active_tool(ToolType.SELECTION)
            return False
        elif key == pygame.K_p:
            self.set_active_tool(ToolType.PAINT)
            return False
        elif key == pygame.K_t:
            self.set_active_tool(ToolType.TRANSITION)
            return False
        
        # Pass to active tool
        if self.active_tool:
            return self.active_tool.handle_key_down(key, map_data)
        
        return False
    
    def render_overlay(self, screen: pygame.Surface, canvas_rect: pygame.Rect,
                      world_to_screen_func, tile_size: int) -> None:
        """
        Render tool-specific overlay on the canvas.
        
        Args:
            screen: Surface to render to
            canvas_rect: Canvas rectangle for clipping
            world_to_screen_func: Function to convert world to screen coordinates
            tile_size: Current tile size in pixels
        """
        if self.active_tool:
            self.active_tool.render_overlay(screen, canvas_rect, world_to_screen_func, tile_size)
    
    def get_cursor_type(self) -> int:
        """Get the pygame cursor type for the active tool."""
        if self.active_tool:
            return self.active_tool.get_cursor_type()
        return pygame.SYSTEM_CURSOR_ARROW
    
    def get_selection_info(self) -> Dict[str, Any]:
        """
        Get information about the current selection (if selection tool is active).
        
        Returns:
            Dictionary with selection information
        """
        if isinstance(self.active_tool, SelectionTool):
            return self.active_tool.get_selection_info()
        return {}
    
    def get_tool_status(self) -> str:
        """
        Get a status string describing the current tool state.
        
        Returns:
            Status string for display
        """
        if not self.active_tool:
            return "No tool active"
        
        status = f"Tool: {self.active_tool.name}"
        
        if isinstance(self.active_tool, PaintTool):
            if self.current_asset_symbol:
                asset_type = self.current_asset_definition.get('type', 'unknown') if self.current_asset_definition else 'unknown'
                status += f" | Asset: {self.current_asset_symbol} ({asset_type})"
            else:
                status += " | No asset selected"
        elif isinstance(self.active_tool, SelectionTool):
            selection_info = self.active_tool.get_selection_info()
            if selection_info.get('position'):
                pos = selection_info['position']
                status += f" | Selected: ({pos[0]}, {pos[1]})"
        
        return status
