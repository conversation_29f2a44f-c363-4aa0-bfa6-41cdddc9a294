"""
Map Editor Engine

This module contains the main map editor engine that manages the editor loop
and coordinates all editor systems.
"""

import pygame
from typing import Optional, Dict, Any, Tuple
from pathlib import Path

from src.application.interfaces import IAssetManager
from src.infrastructure.repositories import MapParser
from src.infrastructure.logging import get_logger
from src.game_core.config import get_config
from .ui import AssetBrowserPanel, MapCanvas, MenuBar, MenuAction, NewMapDialog, MapBrowserDialog, SaveAsDialog, TransitionDialog, DialogResult, Toolbar, StatusBar, InfoPanel
from .map_operations import MapOperations, EditorMapData
from .tool_manager import ToolManager
from .tools import ToolType
from .ui_config import get_ui_config, ELEMENT_MENU_HEIGHT, ELEMENT_STATUS_HEIGHT, ELEMENT_TOOLBAR_BUTTON, ELEMENT_PANEL_WIDTH


class MapEditorEngine:
    """
    Main map editor engine that manages the editor loop and coordinates all systems.
    
    This is the presentation layer that drives the entire map editor application.
    """
    
    def __init__(
        self,
        asset_manager: IAssetManager,
        map_parser: MapParser,
        levels_path: str,
        base_legend_path: str
    ):
        """
        Initialize the map editor engine.
        
        Args:
            asset_manager: Asset manager for loading sprites
            map_parser: Map parser for loading/saving maps
            levels_path: Path to levels directory
            base_legend_path: Path to base legend file
        """
        self.asset_manager = asset_manager
        self.map_parser = map_parser
        self.levels_path = Path(levels_path)
        self.base_legend_path = Path(base_legend_path)
        
        # Get configuration
        self.config = get_config()
        
        # Initialize logger
        self.logger = get_logger(__name__)
        
        # Initialize display
        self.screen_width = self.config.rendering.screen_width
        self.screen_height = self.config.rendering.screen_height
        self.screen = pygame.display.set_mode((self.screen_width, self.screen_height))
        pygame.display.set_caption("Treebeard's Revenge - Map Editor")
        
        # Editor state
        self.running = False
        self.clock = pygame.time.Clock()
        self.target_fps = self.config.rendering.target_fps
        
        # Current map data
        self.current_map_data: Optional[EditorMapData] = None
        
        # Load base legend for asset browser
        self.base_legend = self._load_base_legend()

        # Initialize map operations
        self.map_operations = MapOperations(self.map_parser, str(self.levels_path))

        # Initialize tool manager
        self.tool_manager = ToolManager()

        # Set up transition tool callback
        from .tools import ToolType, TransitionTool
        transition_tool = self.tool_manager.tools.get(ToolType.TRANSITION)
        if isinstance(transition_tool, TransitionTool):
            transition_tool.set_config_dialog_callback(self._open_transition_dialog)
            transition_tool.set_map_operations(self.map_operations)

        # Initialize UI configuration
        self.ui_config = get_ui_config()

        # Input state
        self.mouse_pos = (0, 0)
        self.mouse_clicked = False
        self.events = []

        # Initialize UI components
        self._init_ui_components()

        # Load a test map for demonstration
        self._load_test_map()
    
    def run(self) -> None:
        """Main editor loop."""
        self.logger.info("Starting map editor engine...")
        
        self.running = True
        self.logger.info("Entering main editor loop...")
        
        while self.running:
            dt = self.clock.tick(self.target_fps) / 1000.0  # Delta time in seconds
            
            # Handle events
            self._handle_events()
            
            # Update editor logic
            self._update(dt)
            
            # Render
            self._render()
            
            # Update display
            pygame.display.flip()
        
        self.logger.info("Map editor engine stopped.")
    
    def stop(self) -> None:
        """Stop the editor engine and clean up resources."""
        self.running = False
    
    def _load_base_legend(self) -> Dict[str, Any]:
        """Load the base legend for the asset browser."""
        try:
            import yaml
            with open(self.base_legend_path, 'r') as f:
                return yaml.safe_load(f) or {}
        except Exception as e:
            self.logger.error(f"Failed to load base legend: {e}")
            return {}
    
    def _init_ui_components(self) -> None:
        """Initialize UI components."""
        # Layout constants - using centralized UI configuration
        menu_height = self.ui_config.get_element_size(ELEMENT_MENU_HEIGHT)
        toolbar_width = self.ui_config.get_element_size(ELEMENT_TOOLBAR_BUTTON) + self.ui_config.get_spacing("normal") * 2
        status_height = self.ui_config.get_element_size(ELEMENT_STATUS_HEIGHT)
        panel_width = self.ui_config.get_element_size(ELEMENT_PANEL_WIDTH)

        # Menu bar (top)
        self.menu_bar = MenuBar(0, 0, self.screen_width, menu_height)
        self.menu_bar.set_action_callback(self._handle_menu_action)

        # Toolbar (left side, below menu)
        self.toolbar = Toolbar(0, menu_height, toolbar_width, self.screen_height - menu_height - status_height)
        self.toolbar.set_tool_change_callback(self._handle_tool_change)

        # Asset browser panel (left side, next to toolbar)
        self.asset_panel = AssetBrowserPanel(
            asset_manager=self.asset_manager,
            base_legend=self.base_legend,
            x=toolbar_width,
            y=menu_height,
            width=panel_width - toolbar_width,
            height=self.screen_height - menu_height - status_height
        )

        # Map canvas (right side, below menu)
        canvas_x = panel_width
        info_panel_width = 300  # Width for the info panel
        canvas_width = self.screen_width - panel_width - info_panel_width
        self.map_canvas = MapCanvas(
            asset_manager=self.asset_manager,
            x=canvas_x,
            y=menu_height,
            width=canvas_width,
            height=self.screen_height - menu_height - status_height
        )

        # Info panel (right side, next to canvas)
        info_x = canvas_x + canvas_width
        self.info_panel = InfoPanel(
            x=info_x,
            y=menu_height,
            width=info_panel_width,
            height=self.screen_height - menu_height - status_height
        )
        self.info_panel.set_legend(self.base_legend)
        self.info_panel.set_delete_callback(self._delete_entity)

        # Status bar (bottom)
        self.status_bar = StatusBar(0, self.screen_height - status_height, self.screen_width, status_height)

        # Dialogs
        self.new_map_dialog = NewMapDialog()
        self.map_browser_dialog = MapBrowserDialog()
        self.save_as_dialog = SaveAsDialog()
        self.transition_dialog = TransitionDialog()
    
    def _handle_events(self) -> None:
        """Handle pygame events."""
        self.events = pygame.event.get()
        self.mouse_pos = pygame.mouse.get_pos()
        self.mouse_clicked = False

        for event in self.events:
            if event.type == pygame.QUIT:
                self.stop()
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    self.stop()
                else:
                    # Pass key events to tool manager
                    self.tool_manager.handle_key_down(event.key, self.current_map_data)
            elif event.type == pygame.MOUSEBUTTONDOWN:

                if event.button == 1:  # Left click
                    self.mouse_clicked = True
            elif event.type == pygame.MOUSEMOTION:
                # Handle tool mouse movement
                self._handle_tool_mouse_movement()
    
    def _update(self, dt: float) -> None:
        """Update editor logic."""
        # Track if any UI component handled mouse events
        ui_handled_mouse = False

        # Update menu bar first (highest priority)
        self.menu_bar.update(self.mouse_pos, self.mouse_clicked, self.events)
        # Check if menu bar is active (dropdown open)
        if self.menu_bar.active_menu:
            ui_handled_mouse = True

        # Update toolbar
        self.toolbar.update(self.mouse_pos, self.mouse_clicked, self.events)

        # Update dialogs
        self.new_map_dialog.update(self.events)
        self.map_browser_dialog.update(self.events)
        self.save_as_dialog.update(self.events)
        self.transition_dialog.update(self.events)

        # Check if any dialog is open
        if (self.new_map_dialog.is_open or
            self.map_browser_dialog.is_open or
            self.save_as_dialog.is_open or
            self.transition_dialog.is_open):
            ui_handled_mouse = True

        # Update asset panel
        self.asset_panel.update(dt, self.mouse_pos, self.mouse_clicked, self.events)

        # Update info panel (handle delete button clicks)
        if self.info_panel.handle_mouse_click(self.mouse_pos, 1 if self.mouse_clicked else 0):
            ui_handled_mouse = True

        # Check if asset selection changed
        selected_asset = self.asset_panel.get_selected_asset()
        if selected_asset:
            symbol, definition = selected_asset
            self.tool_manager.set_current_asset(symbol, definition)

        # Only handle tool events if no UI component is active
        if not ui_handled_mouse:
            self._handle_tool_events()

        # Update map canvas
        self.map_canvas.update(dt, self.mouse_pos, self.events)

        # Update status bar
        self._update_status_bar()
    
    def _render(self) -> None:
        """Render the editor interface."""
        # Clear screen with a dark background
        self.screen.fill((40, 40, 40))

        # Render toolbar
        self.toolbar.render(self.screen)

        # Render asset panel
        self.asset_panel.render(self.screen)

        # Render map canvas
        self.map_canvas.render(self.screen)

        # Render info panel
        self.info_panel.render(self.screen)

        # Render tool overlay
        if self.current_map_data:
            canvas_rect = pygame.Rect(self.map_canvas.x, self.map_canvas.y,
                                    self.map_canvas.width, self.map_canvas.height)
            # Pass the base tile size for coordinate conversion - world_to_screen handles zoom scaling
            base_tile_size = self.map_canvas.base_tile_size
            self.tool_manager.render_overlay(self.screen, canvas_rect,
                                           self.map_canvas.world_to_screen, base_tile_size)

        # Render status bar
        self.status_bar.render(self.screen)

        # Render menu bar (after other UI elements so dropdowns appear on top)
        self.menu_bar.render(self.screen)

        # Render dialogs (on top of everything)
        self.new_map_dialog.render(self.screen)
        self.map_browser_dialog.render(self.screen)
        self.save_as_dialog.render(self.screen)
        self.transition_dialog.render(self.screen)

        # Render tooltips (on top of everything else)
        self._render_tooltips()

    def _render_tooltips(self) -> None:
        """Render tooltips from UI components."""
        # Render asset panel tooltip
        tooltip_data = self.asset_panel.get_tooltip_data()
        if tooltip_data:
            self.asset_panel.tooltip_renderer.render_tooltip(self.screen, tooltip_data, self.mouse_pos)

    def _handle_tool_events(self) -> None:
        """Handle tool events from the event queue."""
        for event in self.events:
            if event.type == pygame.MOUSEBUTTONDOWN:
                self._handle_tool_mouse_event(event, 'down')
            elif event.type == pygame.MOUSEBUTTONUP:
                self._handle_tool_mouse_event(event, 'up')

    def _handle_tool_mouse_event(self, event: pygame.event.Event, event_type: str) -> None:
        """Handle mouse events for tools."""
        # Get tile position from mouse position
        tile_pos = self.map_canvas.get_tile_at_mouse()
        if tile_pos is None:
            return

        # Handle the event
        if event_type == 'down':
            modified = self.tool_manager.handle_mouse_down(tile_pos, event.button, self.current_map_data)
        elif event_type == 'up':
            modified = self.tool_manager.handle_mouse_up(tile_pos, event.button, self.current_map_data)

        # If map was modified, mark it as such
        if modified and self.current_map_data:
            self.current_map_data.modified = True
        
        # Update info panel with current selection (for selection tool)
        self._update_info_panel()

    def _handle_tool_mouse_movement(self) -> None:
        """Handle mouse movement for tools."""
        tile_pos = self.map_canvas.get_tile_at_mouse()
        if tile_pos is None:
            return

        # Handle the movement
        modified = self.tool_manager.handle_mouse_move(tile_pos, self.current_map_data)

        # If map was modified, mark it as such
        if modified and self.current_map_data:
            self.current_map_data.modified = True

    def _load_test_map(self) -> None:
        """Load a test map for demonstration."""
        try:
            # Try to load the existing Caledon map
            test_map_path = self.levels_path / "town_caledon" / "caledon_main.map"
            if test_map_path.exists():
                self.current_map_data = self.map_operations.load_map(str(test_map_path))
                self.map_canvas.set_map_data(self.current_map_data)
                self.logger.info(f"Loaded test map: {self.current_map_data.metadata.name}")
            else:
                # Create a small test map
                self.current_map_data = self.map_operations.create_new_map(20, 15, "Test Map")
                self.map_canvas.set_map_data(self.current_map_data)
                self.logger.info("Created test map for demonstration")
        except Exception as e:
            self.logger.warning(f"Failed to load test map: {e}")
            # Create a minimal test map
            self.current_map_data = self.map_operations.create_new_map(10, 8, "Minimal Test")
            self.map_canvas.set_map_data(self.current_map_data)
            self.logger.info("Created minimal test map")

    def _handle_menu_action(self, action: MenuAction) -> None:
        """Handle menu actions."""
        if action == MenuAction.NEW_MAP:
            self.new_map_dialog.open(self._handle_new_map_dialog)
        elif action == MenuAction.OPEN_MAP:
            self._open_map()
        elif action == MenuAction.SAVE_MAP:
            self._save_map()
        elif action == MenuAction.SAVE_AS_MAP:
            self._save_map_as()
        elif action == MenuAction.EXIT:
            self.stop()

    def _handle_new_map_dialog(self, result: DialogResult) -> None:
        """Handle new map dialog result."""
        if result == DialogResult.OK:
            map_data = self.new_map_dialog.get_map_data()
            if map_data:
                self.current_map_data = self.map_operations.create_new_map(
                    map_data['width'], map_data['height'], map_data['name']
                )
                self.map_canvas.set_map_data(self.current_map_data)
                self.logger.info(f"Created new map: {map_data['name']} ({map_data['width']}x{map_data['height']})")

    def _open_map(self) -> None:
        """Open an existing map using the map browser dialog."""
        self.map_browser_dialog.open(self._handle_open_map_dialog, str(self.levels_path))

    def _save_map(self) -> None:
        """Save the current map."""
        if not self.current_map_data:
            self.logger.warning("No map to save")
            return

        try:
            if self.current_map_data.file_path:
                self.map_operations.save_map(self.current_map_data)
                self.logger.info(f"Saved map: {self.current_map_data.file_path}")
            else:
                self._save_map_as()
        except Exception as e:
            self.logger.error(f"Failed to save map: {e}")

    def _save_map_as(self) -> None:
        """Save the current map with a new name using the save as dialog."""
        if not self.current_map_data:
            self.logger.warning("No map to save")
            return

        current_name = self.current_map_data.metadata.name or "new_map"
        self.save_as_dialog.open(self._handle_save_as_dialog, str(self.levels_path), current_name)

    def _handle_open_map_dialog(self, result: DialogResult) -> None:
        """Handle open map dialog result."""
        if result == DialogResult.OK:
            selected_map = self.map_browser_dialog.get_selected_map()
            if selected_map:
                try:
                    self.current_map_data = self.map_operations.load_map(selected_map['path'])
                    self.map_canvas.set_map_data(self.current_map_data)
                    self.logger.info(f"Opened map: {selected_map['name']}")
                except Exception as e:
                    self.logger.error(f"Failed to open map {selected_map['path']}: {e}")

    def _handle_save_as_dialog(self, result: DialogResult) -> None:
        """Handle save as dialog result."""
        if result == DialogResult.OK:
            save_data = self.save_as_dialog.get_save_data()
            if save_data and self.current_map_data:
                try:
                    # Create the folder if it doesn't exist
                    save_path = save_data['full_path']
                    save_path.parent.mkdir(parents=True, exist_ok=True)

                    # Update the map metadata with the new name
                    self.current_map_data.metadata.name = save_data['filename']

                    # Save the map
                    self.map_operations.save_map(self.current_map_data, str(save_path))

                    # Update the current map's file path
                    self.current_map_data.file_path = save_path
                    self.current_map_data.modified = False

                    self.logger.info(f"Saved map as: {save_path}")
                except Exception as e:
                    self.logger.error(f"Failed to save map: {e}")

    def _open_transition_dialog(self, transition_number: str, position: Tuple[int, int]) -> None:
        """Open the transition configuration dialog."""
        if not self.current_map_data:
            self.logger.warning("No map loaded for transition configuration")
            return

        # Get current level ID from map file path
        level_id = self._get_level_id_from_map()
        if not level_id:
            self.logger.warning("Cannot determine level ID for transition configuration")
            return

        # Get existing transition configuration
        existing_transitions = self.map_operations.get_level_config_transitions(level_id)
        current_config = existing_transitions.get(transition_number, {})
        
        # Build used transitions map (excluding the current one being edited)
        used_transitions = {}
        for trans_num in existing_transitions.keys():
            used_transitions[trans_num] = True
        
        # Also check the current map data for placed transition tiles
        if self.current_map_data and self.current_map_data.transition_metadata:
            for pos, trans_num in self.current_map_data.transition_metadata.items():
                if str(trans_num) != transition_number:  # Exclude current transition
                    used_transitions[str(trans_num)] = True

        # Open dialog with current values and usage information
        self.transition_dialog.open(
            self._handle_transition_dialog,
            str(self.levels_path),
            transition_number,
            current_config.get('target_map', ''),
            current_config.get('spawn_point', '0'),
            current_config.get('tile_asset', 'exit_portal'),
            used_transitions,
            {}  # Target map transitions will be scanned by the dialog
        )

        # Store context for the dialog handler
        self._transition_context = {
            'level_id': level_id,
            'transition_number': transition_number,
            'position': position
        }

    def _handle_transition_dialog(self, result: DialogResult) -> None:
        """Handle transition configuration dialog result."""
        if result == DialogResult.OK and hasattr(self, '_transition_context'):
            transition_config = self.transition_dialog.get_transition_config()
            context = self._transition_context
            old_transition_number = context['transition_number']
            new_transition_number = transition_config['exit_number']

            # Update map metadata if transition number changed
            if old_transition_number != new_transition_number:
                position = context['position']
                # Remove old metadata entry
                self.map_operations.remove_transition_at_position(position, self.current_map_data)
                # Add new metadata entry with new number
                self.map_operations.set_transition_at_position(position, int(new_transition_number), self.current_map_data)
                self.logger.info(f"Updated transition metadata at {position}: {old_transition_number} -> {new_transition_number}")

            # Update the tile asset at the position if specified
            position = context['position']
            tile_asset = transition_config.get('tile_asset', 'exit_portal')
            if tile_asset and tile_asset != 'exit_portal':
                # Update the tile to use the custom asset
                tile_asset_id = f"tile.exit.{tile_asset}"

                # Check if the asset exists, if not use the missing asset placeholder
                try:
                    # Try to get the asset to verify it exists
                    from src.infrastructure.assets.registry import is_asset_registered
                    if not is_asset_registered(tile_asset_id):
                        self.logger.warning(f"Asset '{tile_asset_id}' not found, using missing asset placeholder")
                        tile_asset_id = "tile.missing_asset"
                except Exception as e:
                    self.logger.warning(f"Error checking asset '{tile_asset_id}': {e}, using missing asset placeholder")
                    tile_asset_id = "tile.missing_asset"

                if self.current_map_data:
                    x, y = position
                    if (0 <= y < len(self.current_map_data.layout.tiles) and
                        0 <= x < len(self.current_map_data.layout.tiles[y])):
                        self.current_map_data.layout.tiles[y][x] = tile_asset_id
                        self.current_map_data.modified = True
                        self.logger.info(f"Updated tile asset at {position} to {tile_asset_id}")

            if transition_config['target_map']:  # Only save if target map is specified
                # Get existing transitions
                existing_transitions = self.map_operations.get_level_config_transitions(context['level_id'])

                # Remove old transition config if number changed
                if old_transition_number != new_transition_number and old_transition_number in existing_transitions:
                    del existing_transitions[old_transition_number]

                # Update with new configuration using the new transition number
                existing_transitions[new_transition_number] = {
                    'target_map': transition_config['target_map'],
                    'spawn_point': transition_config['spawn_point'],
                    'tile_asset': transition_config['tile_asset']
                }

                # Save updated transitions
                try:
                    self.map_operations.update_level_config_transitions(
                        context['level_id'],
                        existing_transitions
                    )
                    self.logger.info(f"Updated transition {new_transition_number} -> {transition_config['target_map']}")
                except Exception as e:
                    self.logger.error(f"Failed to update transition configuration: {e}")

            # Clean up context
            delattr(self, '_transition_context')

    def _get_level_id_from_map(self) -> Optional[str]:
        """Get the level ID from the current map's file path."""
        if not self.current_map_data or not self.current_map_data.file_path:
            return None

        # Extract level ID from path (parent directory name)
        return self.current_map_data.file_path.parent.name

    def _handle_tool_change(self, tool_type: ToolType) -> None:
        """Handle tool change from toolbar."""
        self.tool_manager.set_active_tool(tool_type)
        # Update info panel when tool changes
        self._update_info_panel()

    def _update_info_panel(self) -> None:
        """Update the info panel with current selection information."""
        selection_info = self.tool_manager.get_selection_info()
        self.info_panel.update_selection(selection_info)
        
        # Update transition metadata and level config if we have a map loaded
        if self.current_map_data:
            # Set transition metadata from current map
            transition_metadata = getattr(self.current_map_data, 'transition_metadata', {})
            self.info_panel.set_transition_data(transition_metadata)
            
            # Get and set level config for transition information
            level_id = self._get_level_id_from_map()
            if level_id:
                try:
                    level_config = self.map_operations.get_level_config_transitions(level_id)
                    # Pass the full exits config to the info panel 
                    self.info_panel.set_level_config({'exits': level_config})
                except Exception as e:
                    self.logger.warning(f"Failed to load level config for info panel: {e}")
                    self.info_panel.set_level_config({})

    def _delete_entity(self, entity_index: int, position: Tuple[int, int], is_transition: bool) -> None:
        """
        Delete an entity at the specified position.
        
        Args:
            entity_index: Index of the entity in the entities list
            position: The tile position (x, y) where the entity is located
            is_transition: Whether this is a transition entity
        """
        if not self.current_map_data or not position:
            return
        
        try:
            # Get the current selection info to find the actual entity
            selection_info = self.tool_manager.get_selection_info()
            entities = selection_info.get('entities', [])
            
            if 0 <= entity_index < len(entities):
                entity_to_delete = entities[entity_index]
                
                # Find and remove the entity from the map data
                entities_list = self.current_map_data.layout.entities
                for i, entity in enumerate(entities_list):
                    # Match by position and type/data_id
                    entity_x = entity.get('x', 0)
                    entity_y = entity.get('y', 0)
                    
                    if (entity_x == position[0] and entity_y == position[1] and
                        entity.get('type') == entity_to_delete.get('type') and
                        entity.get('data_id') == entity_to_delete.get('data_id')):
                        
                        self.logger.info(f"Deleting entity at position {position}: {entity}")
                        entities_list.pop(i)
                        self.current_map_data.modified = True
                        break
                
                # If this is a transition entity, also remove the transition configuration
                if is_transition and position in self.current_map_data.transition_metadata:
                    transition_number = self.current_map_data.transition_metadata[position]
                    
                    # Remove from transition metadata
                    self.map_operations.remove_transition_at_position(position, self.current_map_data)
                    
                    # Remove from level config
                    level_id = self._get_level_id_from_map()
                    if level_id:
                        try:
                            # Get existing transitions
                            existing_transitions = self.map_operations.get_level_config_transitions(level_id)
                            
                            # Remove the transition configuration
                            if str(transition_number) in existing_transitions:
                                del existing_transitions[str(transition_number)]
                                
                                # Update the level config file
                                self.map_operations.update_level_config_transitions(level_id, existing_transitions)
                                self.logger.info(f"Removed transition {transition_number} configuration from level config")
                            
                        except Exception as e:
                            self.logger.warning(f"Failed to remove transition configuration: {e}")
                
                # Update the info panel with the new selection (entity removed)
                self._update_info_panel()
                
                self.logger.info(f"Successfully deleted entity at position {position}")
                
        except Exception as e:
            self.logger.error(f"Failed to delete entity: {e}")

    def _update_status_bar(self) -> None:
        """Update status bar information."""
        # Tool status
        tool_status = self.tool_manager.get_tool_status()
        self.status_bar.update_tool_status(tool_status)

        # Coordinate status
        tile_pos = self.map_canvas.get_tile_at_mouse()
        world_pos = self.map_canvas.mouse_world_pos if hasattr(self.map_canvas, 'mouse_world_pos') else None
        self.status_bar.update_coordinate_status(tile_pos, world_pos)

        # Map status
        if self.current_map_data:
            self.status_bar.update_map_status(
                self.current_map_data.metadata.name,
                self.current_map_data.layout.width,
                self.current_map_data.layout.height,
                self.current_map_data.modified
            )
        else:
            self.status_bar.update_map_status("No map loaded", 0, 0)

        # Zoom status
        zoom_level = getattr(self.map_canvas, 'zoom_level', 1.0)
        self.status_bar.update_zoom_status(zoom_level)
