"""
Map Editor Entry Point

This module provides the main entry point for the map editor.
It initializes all necessary systems and starts the editor.
"""

import pygame
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.infrastructure import (
    AssetManager, setup_logging, get_logger
)
from src.infrastructure.repositories import MapParser
from src.game_core.config import initialize_config, get_config
from .editor_engine import MapEditorEngine


def main():
    """Main entry point for the map editor."""
    # Initialize configuration - look in project root
    config_path = Path(__file__).parent.parent.parent / "game_config.yaml"
    initialize_config(str(config_path))
    
    # Set up logging based on config
    config = get_config()
    setup_logging(verbose=config.verbose_logging)
    
    # Get logger for this module
    logger = get_logger(__name__)
    logger.info("Starting Treebeard's Revenge Map Editor...")
    
    # Initialize Pygame
    pygame.init()
    
    try:
        # Setup paths
        base_legend_path = Path(__file__).parent.parent / "game_data" / "base_legend.yaml"
        levels_path = Path(__file__).parent.parent / "levels"
        
        # Initialize infrastructure components
        asset_manager = AssetManager()
        map_parser = MapParser(str(base_legend_path))
        
        # Initialize map editor engine
        editor_engine = MapEditorEngine(
            asset_manager=asset_manager,
            map_parser=map_parser,
            levels_path=str(levels_path),
            base_legend_path=str(base_legend_path)
        )
        
        # Start the editor
        editor_engine.run()
        
    except Exception as e:
        logger.error(f"Error starting map editor: {e}")
        import traceback
        traceback.print_exc()
    
    finally:    
        pygame.quit()
        sys.exit()


if __name__ == "__main__":
    main()
