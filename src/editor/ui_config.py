"""
UI Configuration Module

This module provides centralized UI configuration with scaling support.
All UI components should use this module for consistent sizing and fonts.
"""

import pygame
from typing import Dict, Any
from src.game_core.config import get_config
from src.infrastructure.logging import get_logger


class UIConfig:
    """
    Centralized UI configuration with scaling support.
    
    This class provides scaled font sizes, element dimensions, and spacing
    based on the global UI scale factor from the game configuration.
    """
    
    def __init__(self):
        """Initialize UI configuration."""
        self.logger = get_logger(__name__)

        # Initialize cache for scaled values
        self._font_cache: Dict[str, pygame.font.Font] = {}
        self._scaled_sizes_cache: Dict[str, int] = {}

        # Load configuration
        self._refresh_config()

        self.logger.info(f"UI scaling initialized with factor: {self._scale_factor}")

    def _refresh_config(self):
        """Refresh configuration from the game config."""
        self._config = get_config()
        self._ui_scaling = self._config.ui_scaling
        self._scale_factor = self._ui_scaling.scale_factor

        # Debug log to verify config loading
        if hasattr(self, 'logger'):
            self.logger.info(f"UI config refreshed - scale factor: {self._scale_factor}")

        # Clear caches when config changes (if they exist)
        if hasattr(self, '_font_cache'):
            self._font_cache.clear()
        if hasattr(self, '_scaled_sizes_cache'):
            self._scaled_sizes_cache.clear()
    
    def get_font(self, size_name: str) -> pygame.font.Font:
        """
        Get a font with the specified size name, scaled appropriately.

        Args:
            size_name: Font size name (tiny, small, normal, medium, large, xlarge, xxlarge)

        Returns:
            Pygame font object
        """
        cache_key = f"{size_name}_{self._scale_factor}"

        if cache_key not in self._font_cache:
            base_size = getattr(self._ui_scaling.font_sizes, size_name, 20)
            scaled_size = int(base_size * self._scale_factor)
            self._font_cache[cache_key] = pygame.font.Font(None, scaled_size)

        return self._font_cache[cache_key]
    
    def get_font_size(self, size_name: str) -> int:
        """
        Get a scaled font size.

        Args:
            size_name: Font size name

        Returns:
            Scaled font size in pixels
        """
        base_size = getattr(self._ui_scaling.font_sizes, size_name, 20)
        return int(base_size * self._scale_factor)

    def get_element_size(self, element_name: str) -> int:
        """
        Get a scaled element size.

        Args:
            element_name: Element size name (button_height, input_height, etc.)

        Returns:
            Scaled element size in pixels
        """
        cache_key = f"element_{element_name}_{self._scale_factor}"

        if cache_key not in self._scaled_sizes_cache:
            base_size = getattr(self._ui_scaling.element_sizes, element_name, 30)
            self._scaled_sizes_cache[cache_key] = int(base_size * self._scale_factor)

        return self._scaled_sizes_cache[cache_key]

    def get_spacing(self, spacing_name: str) -> int:
        """
        Get a scaled spacing value.

        Args:
            spacing_name: Spacing name (tiny, small, normal, medium, large, xlarge)

        Returns:
            Scaled spacing in pixels
        """
        cache_key = f"spacing_{spacing_name}_{self._scale_factor}"

        if cache_key not in self._scaled_sizes_cache:
            base_spacing = getattr(self._ui_scaling.spacing, spacing_name, 8)
            self._scaled_sizes_cache[cache_key] = int(base_spacing * self._scale_factor)

        return self._scaled_sizes_cache[cache_key]
    
    def scale_value(self, value: int) -> int:
        """
        Scale an arbitrary value by the UI scale factor.
        
        Args:
            value: Base value to scale
            
        Returns:
            Scaled value
        """
        return int(value * self._scale_factor)
    
    @property
    def scale_factor(self) -> float:
        """Get the current UI scale factor."""
        return self._scale_factor
    
    def get_standard_colors(self) -> Dict[str, tuple]:
        """
        Get standard UI colors with modern blue and grey theme.
        
        Returns:
            Dictionary of standard UI colors
        """
        return {
            # Backgrounds - Modern blue-grey palette
            'background_primary': (30, 35, 45),      # Dark blue-grey
            'background_secondary': (40, 45, 55),    # Medium blue-grey
            'background_panel': (45, 50, 60),        # Panel background
            'background_dark': (25, 30, 40),         # Darker blue-grey
            'background_medium': (35, 40, 50),       # Medium background
            'background_light': (50, 55, 65),        # Light background
            
            # Borders and separators - Blue-grey tones
            'border_primary': (70, 80, 95),          # Primary border
            'border_secondary': (60, 70, 85),        # Secondary border
            'border_accent': (100, 140, 180),        # Accent border (blue)
            'border': (60, 70, 85),                  # Legacy alias
            'border_light': (80, 90, 105),           # Light border
            'separator': (60, 70, 85),               # Separator lines
            
            # Text colors - High contrast on blue-grey
            'text_primary': (240, 245, 250),         # Almost white
            'text_secondary': (180, 190, 200),       # Light grey
            'text_disabled': (120, 130, 140),        # Muted grey
            'text_accent': (120, 180, 255),          # Light blue accent
            'text_muted': (140, 150, 160),           # Muted text
            
            # Modern button system with depth
            'button_primary': (60, 120, 180),        # Primary blue
            'button_primary_hover': (70, 130, 190),  # Lighter on hover
            'button_primary_active': (50, 110, 170), # Darker when pressed
            'button_primary_shadow': (40, 80, 120),  # Shadow color
            
            'button_secondary': (70, 80, 95),        # Secondary grey-blue
            'button_secondary_hover': (80, 90, 105), # Lighter on hover
            'button_secondary_active': (60, 70, 85), # Darker when pressed
            'button_secondary_shadow': (50, 60, 75), # Shadow color
            
            'button_success': (60, 150, 90),         # Green
            'button_success_hover': (70, 160, 100),  
            'button_success_active': (50, 140, 80),
            'button_success_shadow': (40, 100, 60),
            
            'button_danger': (180, 70, 70),          # Red
            'button_danger_hover': (190, 80, 80),
            'button_danger_active': (170, 60, 60),
            'button_danger_shadow': (120, 50, 50),
            
            # Legacy button aliases for backward compatibility
            'button_normal': (70, 80, 95),
            'button_hover': (80, 90, 105),
            'button_active': (100, 140, 180),
            'button_disabled': (50, 55, 65),
            
            # Input fields - Blue-grey theme
            'input_background': (40, 45, 55),
            'input_border': (70, 80, 95),
            'input_focus': (100, 140, 180),
            'input_text': (240, 245, 250),
            
            # Status and feedback colors
            'success': (90, 200, 120),
            'warning': (255, 190, 80),
            'error': (255, 100, 100),
            'info': (100, 160, 255),
            
            # Progress bars and meters
            'health': (220, 80, 80),
            'mana': (80, 120, 220),
            'experience': (200, 160, 40),
            'progress_bg': (35, 40, 50),
            'progress_border': (60, 70, 85),
            
            # Overlays and effects
            'overlay': (0, 0, 0, 128),
            'tooltip_bg': (35, 40, 50, 240),
            'selection': (100, 140, 180, 128),
            'hover': (255, 255, 255, 30),
            'shadow': (0, 0, 0, 80),
            'highlight': (100, 140, 180, 60),
            
            # Special UI elements
            'scrollbar': (60, 70, 85),
            'scrollbar_thumb': (80, 90, 105),
            'dropdown_bg': (40, 45, 55),
            'menu_separator': (60, 70, 85),
        }


# Global UI configuration instance
_ui_config_instance = None


def get_ui_config() -> UIConfig:
    """
    Get the global UI configuration instance.
    
    Returns:
        UIConfig instance
    """
    global _ui_config_instance
    if _ui_config_instance is None:
        _ui_config_instance = UIConfig()
    return _ui_config_instance


def reset_ui_config():
    """Reset the UI configuration (useful for testing or config changes)."""
    global _ui_config_instance
    if _ui_config_instance is not None:
        _ui_config_instance._refresh_config()
    else:
        _ui_config_instance = None


# Convenience functions for common operations
def get_font(size_name: str) -> pygame.font.Font:
    """Get a scaled font."""
    return get_ui_config().get_font(size_name)


def get_font_size(size_name: str) -> int:
    """Get a scaled font size."""
    return get_ui_config().get_font_size(size_name)


def get_element_size(element_name: str) -> int:
    """Get a scaled element size."""
    return get_ui_config().get_element_size(element_name)


def get_spacing(spacing_name: str) -> int:
    """Get a scaled spacing value."""
    return get_ui_config().get_spacing(spacing_name)


def scale_value(value: int) -> int:
    """Scale an arbitrary value."""
    return get_ui_config().scale_value(value)


def get_colors() -> Dict[str, tuple]:
    """Get standard UI colors."""
    return get_ui_config().get_standard_colors()


# Modern UI Utility Functions
def draw_modern_button(screen, rect, normal_color, hover_color, active_color, 
                      is_hovered=False, is_active=False, shadow_offset=3):
    """
    Draw a modern button with depth and shadow effects.
    
    Args:
        screen: Pygame surface to draw on
        rect: Button rectangle
        normal_color: Normal button color
        hover_color: Hover state color
        active_color: Active/pressed state color
        is_hovered: Whether button is being hovered
        is_active: Whether button is being pressed
        shadow_offset: Shadow depth in pixels
    """
    import pygame
    
    # Choose color based on state
    if is_active:
        color = active_color
        shadow_offset = shadow_offset // 2  # Reduce shadow when pressed
    elif is_hovered:
        color = hover_color
    else:
        color = normal_color
    
    # Draw shadow
    if shadow_offset > 0:
        shadow_rect = pygame.Rect(rect.x + shadow_offset, rect.y + shadow_offset, 
                                 rect.width, rect.height)
        shadow_color = (0, 0, 0, 40)  # Semi-transparent black
        shadow_surface = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)
        shadow_surface.fill(shadow_color)
        screen.blit(shadow_surface, shadow_rect)
    
    # Draw main button
    pygame.draw.rect(screen, color, rect)
    
    # Draw highlight on top edge for 3D effect
    if not is_active:
        highlight_color = tuple(min(255, c + 30) for c in color[:3])
        pygame.draw.line(screen, highlight_color, 
                        (rect.left, rect.top), (rect.right - 1, rect.top), 2)
        pygame.draw.line(screen, highlight_color,
                        (rect.left, rect.top), (rect.left, rect.bottom - 1), 1)
    
    # Draw subtle inner border
    border_color = tuple(max(0, c - 20) for c in color[:3])
    pygame.draw.rect(screen, border_color, rect, 1)


def draw_modern_panel(screen, rect, background_color, border_color, shadow=True, 
                     corner_radius=0, shadow_offset=2):
    """
    Draw a modern panel with optional shadow and rounded corners.
    
    Args:
        screen: Pygame surface to draw on
        rect: Panel rectangle
        background_color: Panel background color
        border_color: Panel border color
        shadow: Whether to draw shadow
        corner_radius: Corner radius for rounded corners
        shadow_offset: Shadow offset distance
    """
    import pygame
    
    # Draw shadow if enabled
    if shadow and shadow_offset > 0:
        shadow_rect = pygame.Rect(rect.x + shadow_offset, rect.y + shadow_offset,
                                 rect.width, rect.height)
        shadow_surface = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)
        shadow_surface.fill((0, 0, 0, 60))
        screen.blit(shadow_surface, shadow_rect)
    
    # Draw main panel
    if corner_radius > 0:
        # Use rounded rectangle (basic implementation)
        pygame.draw.rect(screen, background_color, rect, border_radius=corner_radius)
        pygame.draw.rect(screen, border_color, rect, 2, border_radius=corner_radius)
    else:
        pygame.draw.rect(screen, background_color, rect)
        pygame.draw.rect(screen, border_color, rect, 2)


def draw_progress_bar(screen, rect, progress, color, background_color=None, 
                     border_color=None, show_text=False, font=None, text_color=None):
    """
    Draw a modern progress bar with gradient and text.
    
    Args:
        screen: Pygame surface to draw on
        rect: Progress bar rectangle
        progress: Progress value (0.0 to 1.0)
        color: Fill color
        background_color: Background color (default: dark grey)
        border_color: Border color (default: medium grey)
        show_text: Whether to show percentage text
        font: Font for text
        text_color: Text color
    """
    import pygame
    
    # Default colors
    if background_color is None:
        background_color = (35, 40, 50)
    if border_color is None:
        border_color = (60, 70, 85)
    if text_color is None:
        text_color = (240, 245, 250)
    
    # Clamp progress
    progress = max(0.0, min(1.0, progress))
    
    # Draw background
    pygame.draw.rect(screen, background_color, rect)
    
    # Draw progress fill
    if progress > 0:
        fill_width = int(rect.width * progress)
        fill_rect = pygame.Rect(rect.x, rect.y, fill_width, rect.height)
        
        # Create gradient effect (simple two-tone)
        top_color = tuple(min(255, c + 20) for c in color[:3])
        bottom_color = tuple(max(0, c - 20) for c in color[:3])
        
        # Draw main fill
        pygame.draw.rect(screen, color, fill_rect)
        
        # Draw gradient highlight on top
        if rect.height > 4:
            highlight_rect = pygame.Rect(fill_rect.x, fill_rect.y, 
                                       fill_rect.width, rect.height // 3)
            highlight_surface = pygame.Surface((highlight_rect.width, highlight_rect.height), 
                                             pygame.SRCALPHA)
            highlight_surface.fill((*top_color, 80))
            screen.blit(highlight_surface, highlight_rect)
    
    # Draw border
    pygame.draw.rect(screen, border_color, rect, 1)
    
    # Draw text if requested
    if show_text and font:
        percentage_text = f"{int(progress * 100)}%"
        text_surface = font.render(percentage_text, True, text_color)
        text_rect = text_surface.get_rect(center=rect.center)
        screen.blit(text_surface, text_rect)


def render_text_with_shadow(screen, font, text, pos, color, shadow_color=(0, 0, 0, 100), 
                           shadow_offset=(2, 2)):
    """
    Render text with a shadow effect.
    
    Args:
        screen: Pygame surface to draw on
        font: Font to use
        text: Text to render
        pos: Position tuple (x, y)
        color: Text color
        shadow_color: Shadow color (with alpha)
        shadow_offset: Shadow offset (x, y)
    """
    import pygame
    
    # Render shadow
    shadow_surface = font.render(text, True, shadow_color[:3])
    if len(shadow_color) > 3:  # Has alpha
        shadow_surface.set_alpha(shadow_color[3])
    shadow_pos = (pos[0] + shadow_offset[0], pos[1] + shadow_offset[1])
    screen.blit(shadow_surface, shadow_pos)
    
    # Render main text
    text_surface = font.render(text, True, color)
    screen.blit(text_surface, pos)


def draw_pulse_effect(screen, center, radius, color, alpha=128):
    """
    Draw a pulsing circle effect.
    
    Args:
        screen: Pygame surface to draw on
        center: Center point (x, y)
        radius: Circle radius
        color: Circle color
        alpha: Transparency (0-255)
    """
    import pygame
    
    pulse_surface = pygame.Surface((radius * 2, radius * 2), pygame.SRCALPHA)
    pygame.draw.circle(pulse_surface, (*color[:3], alpha), (radius, radius), radius)
    screen.blit(pulse_surface, (center[0] - radius, center[1] - radius))


class UIAnimator:
    """Simple UI animation system."""
    
    def __init__(self):
        self.animations = []
    
    def add_animation(self, target, property_name, start_value, end_value, duration):
        """Add a new animation."""
        animation = {
            'target': target,
            'property': property_name,
            'start': start_value,
            'end': end_value,
            'duration': duration,
            'elapsed': 0.0,
            'active': True
        }
        self.animations.append(animation)
    
    def update(self, dt):
        """Update all animations."""
        for animation in self.animations[:]:  # Copy list to allow removal
            if not animation['active']:
                continue
                
            animation['elapsed'] += dt
            progress = min(1.0, animation['elapsed'] / animation['duration'])
            
            # Simple linear interpolation
            current_value = (animation['start'] + 
                           (animation['end'] - animation['start']) * progress)
            
            # Set the property (simplified - in practice you'd need more robust property setting)
            setattr(animation['target'], animation['property'], current_value)
            
            if progress >= 1.0:
                animation['active'] = False
                self.animations.remove(animation)


# Global animator instance
_ui_animator_instance = None


def get_ui_animator() -> UIAnimator:
    """Get the global UI animator instance."""
    global _ui_animator_instance
    if _ui_animator_instance is None:
        _ui_animator_instance = UIAnimator()
    return _ui_animator_instance


# Font size aliases for backward compatibility
FONT_TINY = "tiny"
FONT_SMALL = "small"
FONT_NORMAL = "normal"
FONT_MEDIUM = "medium"
FONT_LARGE = "large"
FONT_XLARGE = "xlarge"
FONT_XXLARGE = "xxlarge"

# Element size aliases
ELEMENT_BUTTON_HEIGHT = "button_height"
ELEMENT_INPUT_HEIGHT = "input_height"
ELEMENT_MENU_HEIGHT = "menu_height"
ELEMENT_TOOLBAR_BUTTON = "toolbar_button"
ELEMENT_STATUS_HEIGHT = "status_height"
ELEMENT_HEADER_HEIGHT = "header_height"
ELEMENT_PANEL_WIDTH = "panel_width"
ELEMENT_ASSET_GRID_SIZE = "asset_grid_size"

# Spacing aliases
SPACING_TINY = "tiny"
SPACING_SMALL = "small"
SPACING_NORMAL = "normal"
SPACING_MEDIUM = "medium"
SPACING_LARGE = "large"
SPACING_XLARGE = "xlarge"
