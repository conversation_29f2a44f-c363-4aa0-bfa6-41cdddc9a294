"""
Menu Bar

This module provides a simple menu bar for the map editor with file operations.
"""

import pygame
from typing import List, Tuple, Optional, Callable
from enum import Enum

from src.infrastructure.logging import get_logger
from ..ui_config import get_ui_config, get_colors, FONT_NORMAL, ELEMENT_MENU_HEIGHT, SPACING_NORMAL


class MenuAction(Enum):
    """Menu actions."""
    NEW_MAP = "new_map"
    OPEN_MAP = "open_map"
    SAVE_MAP = "save_map"
    SAVE_AS_MAP = "save_as_map"
    EXIT = "exit"


class MenuBar:
    """
    Simple menu bar for the map editor.
    
    This provides basic file operations and other menu items.
    """
    
    def __init__(self, x: int, y: int, width: int, height: int):
        """
        Initialize the menu bar.
        
        Args:
            x: Menu bar X position
            y: Menu bar Y position
            width: Menu bar width
            height: Menu bar height
        """
        self.x = x
        self.y = y
        self.width = width
        self.height = height
        
        self.logger = get_logger(__name__)
        self.ui_config = get_ui_config()

        # Menu state
        self.active_menu: Optional[str] = None
        self.hover_item: Optional[str] = None

        # Menu structure
        self.menus = {
            "File": [
                ("New Map", MenuAction.NEW_MAP),
                ("Open Map", MenuAction.OPEN_MAP),
                ("Save Map", MenuAction.SAVE_MAP),
                ("Save As...", MenuAction.SAVE_AS_MAP),
                ("---", None),  # Separator
                ("Exit", MenuAction.EXIT),
            ]
        }

        # Colors - using centralized color scheme
        self.colors = get_colors()

        # Fonts - using centralized font configuration
        self.font = self.ui_config.get_font(FONT_NORMAL)

        # Layout - using centralized sizing
        self.menu_padding = self.ui_config.get_spacing(SPACING_NORMAL)
        self.item_height = self.ui_config.get_element_size("button_height")
        self.dropdown_width = self.ui_config.scale_value(180)


        
        # Calculate menu positions
        self.menu_positions = {}
        current_x = self.x + self.menu_padding
        for menu_name in self.menus.keys():
            text_width = self.font.size(menu_name)[0]
            self.menu_positions[menu_name] = (current_x, current_x + text_width + self.menu_padding * 2)
            current_x += text_width + self.menu_padding * 2

        
        # Callback for menu actions
        self.action_callback: Optional[Callable[[MenuAction], None]] = None
    
    def set_action_callback(self, callback: Callable[[MenuAction], None]) -> None:
        """Set the callback function for menu actions."""
        self.action_callback = callback
    
    def update(self, mouse_pos: Tuple[int, int], mouse_clicked: bool,
               events: List[pygame.event.Event]) -> None:
        """
        Update the menu bar.

        Args:
            mouse_pos: Current mouse position
            mouse_clicked: Whether mouse was clicked this frame
            events: Pygame events for this frame
        """
        # Handle events
        for event in events:
            if event.type == pygame.MOUSEBUTTONDOWN:
                if event.button == 1:  # Left click
                    self._handle_click(event.pos)
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    self.active_menu = None
        
        # Update hover state
        self._update_hover(mouse_pos)
    
    def render(self, screen: pygame.Surface) -> None:
        """
        Render the menu bar.
        
        Args:
            screen: Surface to render to
        """
        # Draw background
        menu_rect = pygame.Rect(self.x, self.y, self.width, self.height)
        pygame.draw.rect(screen, self.colors['background_light'], menu_rect)
        pygame.draw.rect(screen, self.colors['border'], menu_rect, 1)
        
        # Draw menu items
        for menu_name, (start_x, end_x) in self.menu_positions.items():
            # Background for active/hover menu
            item_rect = pygame.Rect(start_x - self.menu_padding, self.y, 
                                  end_x - start_x, self.height)
            
            if menu_name == self.active_menu:
                pygame.draw.rect(screen, self.colors['button_active'], item_rect)
            elif menu_name == self.hover_item:
                pygame.draw.rect(screen, self.colors['button_hover'], item_rect)

            # Text
            text = self.font.render(menu_name, True, self.colors['text_primary'])
            text_rect = text.get_rect(center=(start_x + (end_x - start_x) // 2, 
                                            self.y + self.height // 2))
            screen.blit(text, text_rect)
        
        # Draw dropdown if active
        if self.active_menu:
            self._render_dropdown(screen, self.active_menu)
    
    def _handle_click(self, pos: Tuple[int, int]) -> None:
        """Handle mouse click."""
        x, y = pos


        # Check if click is in menu bar
        if self.y <= y <= self.y + self.height:
            # Check which menu was clicked
            for menu_name, (start_x, end_x) in self.menu_positions.items():
                if start_x <= x <= end_x:
                    if self.active_menu == menu_name:
                        self.active_menu = None  # Close if already open
                    else:
                        self.active_menu = menu_name
                    return
        
        # Check if click is in dropdown
        if self.active_menu:
            dropdown_rect = self._get_dropdown_rect(self.active_menu)
            if dropdown_rect.collidepoint(pos):
                item_index = (y - dropdown_rect.y) // self.item_height
                items = self.menus[self.active_menu]
                if 0 <= item_index < len(items):
                    item_text, action = items[item_index]
                    if action and self.action_callback:
                        self.action_callback(action)
                        self.active_menu = None
                return
        
        # Click outside - close menu
        self.active_menu = None
    
    def _update_hover(self, mouse_pos: Tuple[int, int]) -> None:
        """Update hover state."""
        x, y = mouse_pos
        self.hover_item = None
        
        # Check if mouse is over menu bar
        if self.y <= y <= self.y + self.height:
            for menu_name, (start_x, end_x) in self.menu_positions.items():
                if start_x <= x <= end_x:
                    self.hover_item = menu_name
                    break
    
    def _render_dropdown(self, screen: pygame.Surface, menu_name: str) -> None:
        """Render dropdown menu."""
        dropdown_rect = self._get_dropdown_rect(menu_name)

        # Background
        pygame.draw.rect(screen, self.colors['background_medium'], dropdown_rect)
        pygame.draw.rect(screen, self.colors['border'], dropdown_rect, 1)
        
        # Items
        items = self.menus[menu_name]
        for i, (item_text, action) in enumerate(items):
            item_y = dropdown_rect.y + i * self.item_height
            item_rect = pygame.Rect(dropdown_rect.x, item_y, dropdown_rect.width, self.item_height)
            
            if item_text == "---":  # Separator
                sep_y = item_y + self.item_height // 2
                pygame.draw.line(screen, self.colors['border'],
                               (dropdown_rect.x + 5, sep_y),
                               (dropdown_rect.right - 5, sep_y))
            else:
                # Check if mouse is over this item
                mouse_pos = pygame.mouse.get_pos()
                if item_rect.collidepoint(mouse_pos):
                    pygame.draw.rect(screen, self.colors['button_hover'], item_rect)

                # Text
                text_color = self.colors['text_primary'] if action else self.colors['text_disabled']
                text = self.font.render(item_text, True, text_color)
                text_rect = text.get_rect(midleft=(dropdown_rect.x + 10, item_y + self.item_height // 2))
                screen.blit(text, text_rect)
    
    def _get_dropdown_rect(self, menu_name: str) -> pygame.Rect:
        """Get the rectangle for a dropdown menu."""
        start_x, _ = self.menu_positions[menu_name]
        items = self.menus[menu_name]
        dropdown_height = len(items) * self.item_height



        return pygame.Rect(start_x - self.menu_padding, self.y + self.height,
                          self.dropdown_width, dropdown_height)
