"""
Asset Browser Panel

This module provides the left panel asset browser that displays all available
tiles and entities from the base legend.
"""

import pygame
from typing import Dict, Any, List, Tuple, Optional
from enum import Enum
from dataclasses import dataclass

from src.application.interfaces import IAssetManager
from src.infrastructure.logging import get_logger
from src.game_core.config import get_config
from ..ui_config import get_ui_config, get_colors, FONT_NORMAL, FONT_SMALL, ELEMENT_HEADER_HEIGHT, ELEMENT_ASSET_GRID_SIZE, SPACING_SMALL, SPACING_NORMAL


@dataclass
class AssetTooltipData:
    """Data for an asset tooltip."""
    title: str
    lines: List[str]
    position: Tuple[int, int]
    max_width: int = 300


class AssetCategory(Enum):
    """Asset categories for filtering."""
    ALL = "all"
    TILES = "tile"
    ENTITIES = "entity"


class AssetTooltipRenderer:
    """Renders tooltips for editor assets."""

    def __init__(self):
        """Initialize the tooltip renderer."""
        # Get configuration
        config = get_config().inventory_ui
        ui_config = get_ui_config()

        # Colors - Modern blue-grey theme
        self.colors = {
            "background": (35, 40, 50, 240),     # Blue-grey background with alpha
            "border": (70, 80, 95),              # Blue-grey border
            "title": (240, 245, 250),            # Almost white title
            "text": (180, 190, 200),             # Light grey text
            "property": (120, 180, 255),         # Light blue for properties
            "category": (100, 160, 255),         # Blue for category
        }

        # Use centralized UI font configuration
        from ..ui_config import get_font, scale_value, FONT_LARGE, FONT_NORMAL, FONT_SMALL
        self.fonts = {
            "title": get_font(FONT_LARGE),
            "text": get_font(FONT_NORMAL),
            "small": get_font(FONT_SMALL),
        }

        # Use configurable spacing and padding with scaling
        self.padding = scale_value(config.tooltip_padding)
        self.line_spacing = scale_value(config.tooltip_line_spacing)
        self.max_width = scale_value(config.tooltip_max_width)

    def generate_asset_tooltip(self, symbol: str, definition: Dict[str, Any]) -> AssetTooltipData:
        """
        Generate tooltip data for an asset.

        Args:
            symbol: Asset symbol (e.g., '#', '@', 'T')
            definition: Asset definition from base legend

        Returns:
            AssetTooltipData with tooltip information
        """
        lines = []

        # Asset symbol and type as title
        asset_type = definition.get('type', 'unknown').replace('_', ' ').title()
        title = f"{symbol} - {asset_type}"

        # Category
        category = definition.get('category', 'unknown').title()
        lines.append(f"Category: {category}")

        # Asset ID
        asset_id = definition.get('asset_id', 'N/A')
        if asset_id != 'N/A':
            lines.append(f"Asset ID: {asset_id}")

        # Data ID (for entities)
        data_id = definition.get('data_id')
        if data_id:
            lines.append(f"Data ID: {data_id}")

        # Solid property
        if 'solid' in definition:
            solid_text = "Solid" if definition['solid'] else "Passable"
            lines.append(f"Collision: {solid_text}")

        # Properties
        properties = definition.get('properties', {})
        if properties:
            lines.append("")  # Empty line for spacing
            lines.append("Properties:")
            for key, value in properties.items():
                prop_name = key.replace('_', ' ').title()
                lines.append(f"  {prop_name}: {value}")

        # Usage description based on category and type
        lines.append("")  # Empty line for spacing
        self._add_usage_description(lines, definition)

        return AssetTooltipData(
            title=title,
            lines=lines,
            position=(0, 0),  # Will be set when rendering
            max_width=self.max_width
        )

    def _add_usage_description(self, lines: List[str], definition: Dict[str, Any]) -> None:
        """Add usage description based on asset type."""
        category = definition.get('category', '')
        asset_type = definition.get('type', '')

        if category == 'tile':
            if asset_type == 'exit':
                lines.append("Click to place level transition")
                lines.append("Double-click to configure destination")
            elif asset_type in ['wall', 'mountain', 'tree']:
                lines.append("Solid terrain - blocks movement")
            elif asset_type in ['floor', 'grass', 'path']:
                lines.append("Walkable terrain")
            elif asset_type == 'door':
                lines.append("Interactive door tile")
            elif asset_type == 'water':
                lines.append("Water tile - may block movement")
            else:
                lines.append("Click to place on map")
        elif category == 'entity':
            if asset_type == 'player':
                lines.append("Player spawn point")
                lines.append("Only one per map recommended")
            elif asset_type == 'monster':
                # Add specific monster type info if available
                data_id = definition.get('data_id', '')
                if data_id in ['horse', 'cow', 'chicken', 'deer', 'pig', 'dog']:
                    lines.append("Peaceful animal")
                    lines.append("Wanders around the map")
                    if data_id == 'horse':
                        lines.append("Wild Horse - fast and majestic")
                    elif data_id == 'cow':
                        lines.append("Cow - gentle dairy animal")
                    elif data_id == 'chicken':
                        lines.append("Chicken - small farmyard bird")
                    elif data_id == 'deer':
                        lines.append("Deer - elegant forest creature")
                    elif data_id == 'pig':
                        lines.append("Pig - snorts and roots around")
                    elif data_id == 'dog':
                        lines.append("Stray Dog - friendly companion")
                else:
                    lines.append("Enemy creature")
                    lines.append("Will spawn during gameplay")
            elif asset_type == 'item':
                lines.append("Collectible item")
                lines.append("Players can pick up")
            elif asset_type in ['chest', 'altar']:
                lines.append("Interactive object")
                lines.append("Players can interact with")
            elif asset_type == 'npc':
                lines.append("Non-Player Character")
                lines.append("Can provide dialog or services")
                # Add specific NPC type info if available
                data_id = definition.get('data_id', '')
                if data_id == 'merchant':
                    lines.append("Merchant - sells general goods")
                elif data_id == 'armourer':
                    lines.append("Armourer - sells armor")
                elif data_id == 'weaponsmith':
                    lines.append("Weaponsmith - sells weapons")
                elif data_id == 'innkeeper':
                    lines.append("Innkeeper - sells food and lodging")
                elif data_id == 'commoner':
                    lines.append("Commoner - provides local dialog")
                elif data_id == 'guard':
                    lines.append("Guard - provides security dialog")
            else:
                lines.append("Click to place entity")
        else:
            lines.append("Click to place on map")

    def render_tooltip(self, screen: pygame.Surface, tooltip_data: AssetTooltipData, mouse_pos: Tuple[int, int]) -> None:
        """
        Render a tooltip on the screen.

        Args:
            screen: Surface to render to
            tooltip_data: Tooltip data to render
            mouse_pos: Current mouse position
        """
        if not tooltip_data.lines:
            return

        # Calculate tooltip size
        title_surface = self.fonts["title"].render(tooltip_data.title, True, self.colors["title"])
        title_width = title_surface.get_width()
        title_height = title_surface.get_height()

        max_line_width = title_width
        total_height = title_height + self.line_spacing

        line_surfaces = []
        for line in tooltip_data.lines:
            if line.strip():  # Non-empty line
                # Choose color based on content
                color = self.colors["text"]
                if line.startswith("Category:") or line.startswith("Asset ID:") or line.startswith("Data ID:"):
                    color = self.colors["category"]
                elif line.startswith("  ") or line.startswith("Properties:"):
                    color = self.colors["property"]

                line_surface = self.fonts["text"].render(line, True, color)
                line_surfaces.append(line_surface)
                max_line_width = max(max_line_width, line_surface.get_width())
                total_height += line_surface.get_height() + self.line_spacing
            else:  # Empty line for spacing
                line_surfaces.append(None)
                total_height += self.fonts["text"].get_height() // 2

        # Calculate tooltip position (avoid going off screen)
        tooltip_width = min(max_line_width + self.padding * 2, tooltip_data.max_width)
        tooltip_height = total_height + self.padding * 2

        screen_width, screen_height = screen.get_size()

        # Position tooltip to the right and below mouse, but keep on screen
        tooltip_x = mouse_pos[0] + 15
        tooltip_y = mouse_pos[1] + 15

        if tooltip_x + tooltip_width > screen_width:
            tooltip_x = mouse_pos[0] - tooltip_width - 15

        if tooltip_y + tooltip_height > screen_height:
            tooltip_y = mouse_pos[1] - tooltip_height - 15

        # Ensure tooltip stays on screen
        tooltip_x = max(0, min(tooltip_x, screen_width - tooltip_width))
        tooltip_y = max(0, min(tooltip_y, screen_height - tooltip_height))

        # Create tooltip surface
        tooltip_surface = pygame.Surface((tooltip_width, tooltip_height), pygame.SRCALPHA)
        tooltip_surface.fill(self.colors["background"])
        pygame.draw.rect(tooltip_surface, self.colors["border"],
                        pygame.Rect(0, 0, tooltip_width, tooltip_height), 2)

        # Render title
        tooltip_surface.blit(title_surface, (self.padding, self.padding))

        # Render lines
        y_offset = self.padding + title_height + self.line_spacing
        for line_surface in line_surfaces:
            if line_surface:
                tooltip_surface.blit(line_surface, (self.padding, y_offset))
                y_offset += line_surface.get_height() + self.line_spacing
            else:
                y_offset += self.fonts["text"].get_height() // 2

        # Blit tooltip to screen
        screen.blit(tooltip_surface, (tooltip_x, tooltip_y))


class AssetBrowserPanel:
    """
    Asset browser panel for the map editor.
    
    This panel displays all available assets from the base legend,
    allows filtering by category, and provides search functionality.
    """
    
    def __init__(self, asset_manager: IAssetManager, base_legend: Dict[str, Any], 
                 x: int, y: int, width: int, height: int):
        """
        Initialize the asset browser panel.
        
        Args:
            asset_manager: Asset manager for loading sprites
            base_legend: Base legend data
            x: Panel X position
            y: Panel Y position
            width: Panel width
            height: Panel height
        """
        self.asset_manager = asset_manager
        self.base_legend = base_legend
        self.x = x
        self.y = y
        self.width = width
        self.height = height
        
        self.logger = get_logger(__name__)
        self.config = get_config()
        self.ui_config = get_ui_config()

        # UI state
        self.current_category = AssetCategory.ALL
        self.search_text = ""
        self.selected_asset = None  # (symbol, definition) tuple
        self.scroll_offset = 0
        self.hover_asset = None

        # UI layout constants - using centralized configuration
        self.header_height = self.ui_config.get_element_size(ELEMENT_HEADER_HEIGHT)
        self.search_height = self.ui_config.get_element_size("input_height") + self.ui_config.get_spacing(SPACING_NORMAL)
        self.category_height = self.ui_config.get_element_size("button_height") + self.ui_config.get_spacing(SPACING_NORMAL)
        self.asset_grid_size = self.ui_config.get_element_size(ELEMENT_ASSET_GRID_SIZE)
        self.asset_padding = self.ui_config.get_spacing(SPACING_SMALL)
        self.scroll_speed = 3

        # Colors - using centralized color scheme
        self.colors = get_colors()

        # Fonts - using centralized font configuration
        self.font = self.ui_config.get_font(FONT_NORMAL)
        self.font_small = self.ui_config.get_font(FONT_SMALL)
        
        # Prepare asset data
        self.filtered_assets = self._filter_assets()
        
        # Input state
        self.search_active = False
        self.mouse_pos = (0, 0)
        self.mouse_clicked = False

        # Tooltip system
        self.tooltip_renderer = AssetTooltipRenderer()
        self.tooltip_delay = 0.5  # Seconds to wait before showing tooltip
        self.hover_start_time = 0.0
        self.show_tooltip = False
    
    def update(self, dt: float, mouse_pos: Tuple[int, int], mouse_clicked: bool,
               events: List[pygame.event.Event]) -> None:
        """
        Update the asset panel.

        Args:
            dt: Delta time
            mouse_pos: Current mouse position
            mouse_clicked: Whether mouse was clicked this frame
            events: Pygame events for this frame
        """
        self.mouse_pos = mouse_pos
        self.mouse_clicked = mouse_clicked

        # Handle events
        for event in events:
            self._handle_event(event)

        # Update hover state
        previous_hover = self.hover_asset
        self._update_hover()

        # Update tooltip timing
        if self.hover_asset != previous_hover:
            # Reset tooltip timer when hover changes
            self.hover_start_time = 0.0
            self.show_tooltip = False
        elif self.hover_asset:
            # Accumulate hover time
            self.hover_start_time += dt
            if self.hover_start_time >= self.tooltip_delay:
                self.show_tooltip = True
        else:
            # No hover, hide tooltip
            self.show_tooltip = False

        # Update filtered assets if needed
        self.filtered_assets = self._filter_assets()
    
    def render(self, screen: pygame.Surface) -> None:
        """
        Render the asset browser panel.

        Args:
            screen: Surface to render to
        """
        # Draw background
        panel_rect = pygame.Rect(self.x, self.y, self.width, self.height)
        pygame.draw.rect(screen, self.colors['background_medium'], panel_rect)
        pygame.draw.rect(screen, self.colors['border'], panel_rect, 2)

        # Draw header
        self._render_header(screen)

        # Draw category tabs
        self._render_category_tabs(screen)

        # Draw search box
        self._render_search_box(screen)

        # Draw asset grid
        self._render_asset_grid(screen)
    
    def get_selected_asset(self) -> Optional[Tuple[str, Dict[str, Any]]]:
        """Get the currently selected asset."""
        return self.selected_asset

    def get_tooltip_data(self) -> Optional[AssetTooltipData]:
        """Get tooltip data for the currently hovered asset, if any."""
        if self.show_tooltip and self.hover_asset:
            symbol, definition = self.hover_asset
            return self.tooltip_renderer.generate_asset_tooltip(symbol, definition)
        return None
    
    def _handle_event(self, event: pygame.event.Event) -> None:
        """Handle a pygame event."""
        if event.type == pygame.MOUSEBUTTONDOWN:
            if event.button == 1:  # Left click
                self._handle_left_click(event.pos)
            elif event.button == 4:  # Mouse wheel up
                self.scroll_offset = max(0, self.scroll_offset - self.scroll_speed)
            elif event.button == 5:  # Mouse wheel down
                max_scroll = self._get_max_scroll()
                self.scroll_offset = min(max_scroll, self.scroll_offset + self.scroll_speed)
        
        elif event.type == pygame.KEYDOWN:
            if self.search_active:
                self._handle_search_input(event)
    
    def _handle_left_click(self, pos: Tuple[int, int]) -> None:
        """Handle left mouse click."""
        x, y = pos
        
        # Check if click is within panel
        if not (self.x <= x <= self.x + self.width and self.y <= y <= self.y + self.height):
            return
        
        # Check category tabs
        tab_y = self.y + self.header_height
        if tab_y <= y <= tab_y + self.category_height:
            self._handle_category_click(x)
            return
        
        # Check search box
        search_y = tab_y + self.category_height
        if search_y <= y <= search_y + self.search_height:
            self.search_active = True
            return
        else:
            self.search_active = False
        
        # Check asset grid
        grid_y = search_y + self.search_height
        if y >= grid_y:
            self._handle_asset_click(x, y - grid_y)
    
    def _handle_category_click(self, x: int) -> None:
        """Handle click on category tabs."""
        tab_width = self.width // 3
        relative_x = x - self.x
        
        if relative_x < tab_width:
            self.current_category = AssetCategory.ALL
        elif relative_x < tab_width * 2:
            self.current_category = AssetCategory.TILES
        else:
            self.current_category = AssetCategory.ENTITIES
        
        self.scroll_offset = 0  # Reset scroll when changing category
    
    def _handle_asset_click(self, x: int, y: int) -> None:
        """Handle click on asset grid."""
        # Calculate which asset was clicked
        relative_x = x - self.x
        grid_cols = self.width // (self.asset_grid_size + self.asset_padding)
        
        col = relative_x // (self.asset_grid_size + self.asset_padding)
        row = (y + self.scroll_offset) // (self.asset_grid_size + self.asset_padding)
        
        asset_index = row * grid_cols + col
        
        if 0 <= asset_index < len(self.filtered_assets):
            self.selected_asset = self.filtered_assets[asset_index]
            self.logger.info(f"Selected asset: {self.selected_asset[0]}")
    
    def _handle_search_input(self, event: pygame.event.Event) -> None:
        """Handle search input."""
        if event.key == pygame.K_BACKSPACE:
            self.search_text = self.search_text[:-1]
        elif event.key == pygame.K_ESCAPE:
            self.search_active = False
            self.search_text = ""
        elif event.unicode.isprintable():
            self.search_text += event.unicode
        
        self.scroll_offset = 0  # Reset scroll when searching
    
    def _update_hover(self) -> None:
        """Update hover state."""
        x, y = self.mouse_pos
        
        # Check if mouse is over asset grid
        grid_y = self.y + self.header_height + self.category_height + self.search_height
        if (self.x <= x <= self.x + self.width and y >= grid_y):
            relative_x = x - self.x
            relative_y = y - grid_y
            
            grid_cols = self.width // (self.asset_grid_size + self.asset_padding)
            col = relative_x // (self.asset_grid_size + self.asset_padding)
            row = (relative_y + self.scroll_offset) // (self.asset_grid_size + self.asset_padding)
            
            asset_index = row * grid_cols + col
            
            if 0 <= asset_index < len(self.filtered_assets):
                self.hover_asset = self.filtered_assets[asset_index]
            else:
                self.hover_asset = None
        else:
            self.hover_asset = None
    
    def _filter_assets(self) -> List[Tuple[str, Dict[str, Any]]]:
        """Filter assets based on current category and search text."""
        filtered = []
        
        for symbol, definition in self.base_legend.items():
            # Filter by category
            if self.current_category != AssetCategory.ALL:
                if definition.get('category') != self.current_category.value:
                    continue
            
            # Filter by search text
            if self.search_text:
                search_lower = self.search_text.lower()
                if (search_lower not in symbol.lower() and 
                    search_lower not in definition.get('type', '').lower() and
                    search_lower not in definition.get('data_id', '').lower()):
                    continue
            
            filtered.append((symbol, definition))
        
        return filtered

    def _render_header(self, screen: pygame.Surface) -> None:
        """Render the panel header."""
        header_rect = pygame.Rect(self.x, self.y, self.width, self.header_height)
        pygame.draw.rect(screen, self.colors['background_dark'], header_rect)

        # Title
        title_text = self.font.render("Assets", True, self.colors['text_primary'])
        title_rect = title_text.get_rect(center=(self.x + self.width // 2, self.y + self.header_height // 3))
        screen.blit(title_text, title_rect)

        # Asset count
        count_text = self.font_small.render(f"{len(self.filtered_assets)} assets", True, self.colors['text_secondary'])
        count_rect = count_text.get_rect(center=(self.x + self.width // 2, self.y + 2 * self.header_height // 3))
        screen.blit(count_text, count_rect)

    def _render_category_tabs(self, screen: pygame.Surface) -> None:
        """Render category filter tabs."""
        tab_y = self.y + self.header_height
        tab_width = self.width // 3

        categories = [
            (AssetCategory.ALL, "All"),
            (AssetCategory.TILES, "Tiles"),
            (AssetCategory.ENTITIES, "Entities")
        ]

        for i, (category, label) in enumerate(categories):
            tab_x = self.x + i * tab_width
            tab_rect = pygame.Rect(tab_x, tab_y, tab_width, self.category_height)

            # Background color
            if category == self.current_category:
                color = self.colors['button_active']
            else:
                color = self.colors['button_normal']

            pygame.draw.rect(screen, color, tab_rect)
            pygame.draw.rect(screen, self.colors['border'], tab_rect, 1)

            # Text
            text_color = self.colors['text_primary'] if category == self.current_category else self.colors['text_secondary']
            text = self.font_small.render(label, True, text_color)
            text_rect = text.get_rect(center=tab_rect.center)
            screen.blit(text, text_rect)

    def _render_search_box(self, screen: pygame.Surface) -> None:
        """Render the search input box."""
        search_y = self.y + self.header_height + self.category_height
        search_rect = pygame.Rect(self.x + 5, search_y + 5, self.width - 10, self.search_height - 10)

        # Background
        pygame.draw.rect(screen, self.colors['input_background'], search_rect)
        if self.search_active:
            pygame.draw.rect(screen, self.colors['input_focus'], search_rect, 2)
        else:
            pygame.draw.rect(screen, self.colors['input_border'], search_rect, 1)

        # Text
        display_text = self.search_text if self.search_text else "Search..."
        text_color = self.colors['text_primary'] if self.search_text else self.colors['text_secondary']
        text = self.font_small.render(display_text, True, text_color)
        text_rect = text.get_rect(midleft=(search_rect.left + 5, search_rect.centery))
        screen.blit(text, text_rect)

        # Cursor
        if self.search_active and pygame.time.get_ticks() % 1000 < 500:
            cursor_x = text_rect.right + 2
            pygame.draw.line(screen, self.colors['text_primary'],
                           (cursor_x, search_rect.top + 3),
                           (cursor_x, search_rect.bottom - 3))

    def _render_asset_grid(self, screen: pygame.Surface) -> None:
        """Render the grid of assets."""
        grid_y = self.y + self.header_height + self.category_height + self.search_height
        grid_height = self.height - (self.header_height + self.category_height + self.search_height)

        # Calculate grid layout
        grid_cols = self.width // (self.asset_grid_size + self.asset_padding)

        # Create clipping rect for scrolling
        clip_rect = pygame.Rect(self.x, grid_y, self.width, grid_height)
        screen.set_clip(clip_rect)

        try:
            for i, (symbol, definition) in enumerate(self.filtered_assets):
                row = i // grid_cols
                col = i % grid_cols

                asset_x = self.x + col * (self.asset_grid_size + self.asset_padding) + self.asset_padding
                asset_y = grid_y + row * (self.asset_grid_size + self.asset_padding) + self.asset_padding - self.scroll_offset

                # Skip if not visible
                if asset_y + self.asset_grid_size < grid_y or asset_y > grid_y + grid_height:
                    continue

                asset_rect = pygame.Rect(asset_x, asset_y, self.asset_grid_size, self.asset_grid_size)

                # Background
                bg_color = self.colors['background_medium']
                if self.selected_asset and self.selected_asset[0] == symbol:
                    bg_color = self.colors['button_active']
                elif self.hover_asset and self.hover_asset[0] == symbol:
                    bg_color = self.colors['button_hover']

                pygame.draw.rect(screen, bg_color, asset_rect)
                pygame.draw.rect(screen, self.colors['border'], asset_rect, 1)

                # Asset sprite
                self._render_asset_sprite(screen, symbol, definition, asset_rect)

                # Symbol text
                symbol_text = self.font_small.render(symbol, True, self.colors['text_primary'])
                symbol_rect = symbol_text.get_rect(center=(asset_rect.centerx, asset_rect.bottom - 8))
                screen.blit(symbol_text, symbol_rect)

        finally:
            screen.set_clip(None)

    def _render_asset_sprite(self, screen: pygame.Surface, symbol: str,
                           definition: Dict[str, Any], rect: pygame.Rect) -> None:
        """Render an asset sprite in the given rectangle."""
        asset_id = definition.get('asset_id')
        data_id = definition.get('data_id', '')
        category = definition.get('category')
        entity_type = definition.get('type')

        # Determine the display asset ID
        display_asset_id = asset_id

        if category == 'entity':
            if entity_type == 'npc' and asset_id and asset_id.startswith('npc.'):
                # For NPCs in the asset panel, use icon assets instead of full character sprites
                if data_id:
                    display_asset_id = f"icon.npc.{data_id}"
            elif entity_type == 'monster':
                # For monsters, try to use icon assets for better display in asset panel
                if data_id:
                    # Try monster icon first
                    icon_asset_id = f"icon.monster.{data_id}"
                    try:
                        # Check if monster icon exists
                        from src.infrastructure.assets.registry import is_asset_registered
                        if is_asset_registered(icon_asset_id):
                            display_asset_id = icon_asset_id
                        elif asset_id:
                            # Fall back to full sprite if available
                            display_asset_id = asset_id
                        else:
                            # Try to get asset_id from monster definition
                            try:
                                from src.game_data.monsters import get_monster_definition
                                monster_def = get_monster_definition(data_id)
                                if monster_def:
                                    display_asset_id = monster_def.asset_id
                            except Exception:
                                pass
                    except Exception:
                        pass

        if display_asset_id:
            try:
                # Get the asset surface
                sprite_size = min(rect.width - 16, rect.height - 20)  # Leave room for text
                asset_surface = self.asset_manager.get_asset(display_asset_id, (sprite_size, sprite_size))

                # Center the sprite in the rectangle
                sprite_rect = asset_surface.get_rect(center=(rect.centerx, rect.centery - 5))
                screen.blit(asset_surface, sprite_rect)

            except Exception as e:
                # Fallback: draw colored rectangle
                fallback_rect = pygame.Rect(rect.centerx - 16, rect.centery - 16, 32, 32)
                # Choose color based on category using modern theme
                category = definition.get('category', 'unknown')
                if category == 'tile':
                    color = self.colors.get('text_secondary', (80, 90, 105))  # Blue-grey for tiles
                elif category == 'entity':
                    color = self.colors.get('info', (100, 160, 255))  # Blue for entities
                else:
                    color = self.colors.get('error', (255, 100, 100))  # Red for unknown

                pygame.draw.rect(screen, color, fallback_rect)
                pygame.draw.rect(screen, self.colors['border'], fallback_rect, 1)
        else:
            # No asset ID - just show the symbol
            symbol_text = self.font.render(symbol, True, self.colors['text_primary'])
            symbol_rect = symbol_text.get_rect(center=(rect.centerx, rect.centery - 5))
            screen.blit(symbol_text, symbol_rect)

    def _get_max_scroll(self) -> int:
        """Get the maximum scroll offset."""
        grid_height = self.height - (self.header_height + self.category_height + self.search_height)
        grid_cols = self.width // (self.asset_grid_size + self.asset_padding)

        if grid_cols == 0:
            return 0

        total_rows = (len(self.filtered_assets) + grid_cols - 1) // grid_cols
        total_height = total_rows * (self.asset_grid_size + self.asset_padding)

        return max(0, total_height - grid_height)
