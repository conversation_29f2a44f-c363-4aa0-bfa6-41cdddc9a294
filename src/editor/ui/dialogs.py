"""
Dialog System

This module provides dialog boxes for the map editor.
"""

import pygame
from typing import Optional, Callable, List, Tuple, Dict, Any
from pathlib import Path
from enum import Enum
import os

from src.infrastructure.logging import get_logger
from ..ui_config import get_ui_config, get_colors, FONT_NORMAL, FONT_SMALL, ELEMENT_BUTTON_HEIGHT, ELEMENT_INPUT_HEIGHT, SPACING_NORMAL


class DialogResult(Enum):
    """Dialog result types."""
    OK = "ok"
    CANCEL = "cancel"
    YES = "yes"
    NO = "no"


class BaseDialog:
    """Base class for all dialogs."""
    
    def __init__(self, title: str, width: int, height: int):
        """Initialize the dialog."""
        self.title = title
        self.width = width
        self.height = height
        self.is_open = False
        self.result: Optional[DialogResult] = None
        self.callback: Optional[Callable] = None
        
        # Calculate position (center of screen)
        screen = pygame.display.get_surface()
        screen_width, screen_height = screen.get_size()
        self.x = (screen_width - width) // 2
        self.y = (screen_height - height) // 2
        
        # UI configuration
        self.ui_config = get_ui_config()

        # Colors - using centralized color scheme
        self.colors = get_colors()

        # Fonts - using centralized font configuration
        self.font = self.ui_config.get_font(FONT_NORMAL)
        self.font_small = self.ui_config.get_font(FONT_SMALL)
        
        self.logger = get_logger(__name__)
    
    def open(self, callback: Optional[Callable] = None) -> None:
        """Open the dialog."""
        self.is_open = True
        self.result = None
        self.callback = callback
    
    def close(self, result: DialogResult) -> None:
        """Close the dialog with a result."""
        self.is_open = False
        self.result = result
        if self.callback:
            self.callback(result)
    
    def update(self, events: List[pygame.event.Event]) -> None:
        """Update the dialog."""
        if not self.is_open:
            return
        
        # Process events and remove consumed ones
        consumed_events = []
        for event in events:
            if self.handle_event(event):
                consumed_events.append(event)
        
        # Remove consumed events from the list
        for event in consumed_events:
            if event in events:
                events.remove(event)
    
    def handle_event(self, event: pygame.event.Event) -> bool:
        """
        Handle an event (to be overridden by subclasses).
        
        Returns:
            True if the event was consumed and should not be processed by other systems
        """
        if event.type == pygame.KEYDOWN:
            if event.key == pygame.K_ESCAPE:
                self.close(DialogResult.CANCEL)
                return True
        return False
    
    def render(self, screen: pygame.Surface) -> None:
        """Render the dialog."""
        if not self.is_open:
            return

        # Draw background overlay
        overlay = pygame.Surface(screen.get_size(), pygame.SRCALPHA)
        overlay.fill((0, 0, 0, 128))
        screen.blit(overlay, (0, 0))
        
        # Draw dialog box using modern panel system
        from src.editor.ui_config import draw_modern_panel
        dialog_rect = pygame.Rect(self.x, self.y, self.width, self.height)
        draw_modern_panel(
            screen,
            dialog_rect,
            background_color=self.colors.get('panel_bg', (45, 50, 60)),
            border_color=self.colors.get('border', (80, 90, 105)),
            shadow=True
        )
        
        # Draw title bar - using scaled height with modern panel
        title_height = self.ui_config.scale_value(40)
        title_rect = pygame.Rect(self.x, self.y, self.width, title_height)
        draw_modern_panel(
            screen,
            title_rect,
            background_color=self.colors.get('header_bg', (55, 65, 80)),
            border_color=self.colors.get('border', (80, 90, 105)),
            shadow=False
        )
        
        # Title text
        title_text = self.font.render(self.title, True, self.colors['text_primary'])
        title_text_rect = title_text.get_rect(center=(self.x + self.width // 2, self.y + title_height // 2))
        screen.blit(title_text, title_text_rect)
        
        # Render dialog content
        self.render_content(screen)
    
    def render_content(self, screen: pygame.Surface) -> None:
        """Render dialog content (to be overridden by subclasses)."""
        pass


class NewMapDialog(BaseDialog):
    """Dialog for creating a new map."""
    
    def __init__(self):
        """Initialize the new map dialog."""
        super().__init__("New Map", 500, 400)
        
        # Input fields
        self.map_name = "New Map"
        self.map_width = "20"
        self.map_height = "15"
        self.active_field = "name"
        
        # Button rects - increased sizes
        self.ok_button = pygame.Rect(self.x + self.width - 220, self.y + self.height - 60, 100, 40)
        self.cancel_button = pygame.Rect(self.x + self.width - 110, self.y + self.height - 60, 100, 40)
    
    def handle_event(self, event: pygame.event.Event) -> bool:
        """Handle dialog events."""
        if super().handle_event(event):
            return True
        
        if event.type == pygame.MOUSEBUTTONDOWN:
            if event.button == 1:  # Left click
                # Check button clicks
                if self.ok_button.collidepoint(event.pos):
                    self._create_map()
                    return True
                elif self.cancel_button.collidepoint(event.pos):
                    self.close(DialogResult.CANCEL)
                    return True
                else:
                    # Check field clicks
                    self._check_field_clicks(event.pos)
                    return True
        
        elif event.type == pygame.KEYDOWN:
            if event.key == pygame.K_RETURN:
                self._create_map()
                return True
            elif event.key == pygame.K_TAB:
                self._next_field()
                return True
            elif event.key == pygame.K_BACKSPACE:
                self._handle_backspace()
                return True
            elif event.unicode.isprintable():
                self._handle_text_input(event.unicode)
                return True
        
        return False
    
    def render_content(self, screen: pygame.Surface) -> None:
        """Render dialog content."""
        content_y = self.y + 60  # Adjusted for larger title bar

        # Map name field
        self._render_field(screen, "Map Name:", self.map_name, content_y, "name")

        # Map width field
        self._render_field(screen, "Width (tiles):", self.map_width, content_y + 80, "width")

        # Map height field
        self._render_field(screen, "Height (tiles):", self.map_height, content_y + 160, "height")
        
        # Buttons
        self._render_button(screen, self.ok_button, "OK")
        self._render_button(screen, self.cancel_button, "Cancel")
    
    def _render_field(self, screen: pygame.Surface, label: str, value: str, y: int, field_name: str) -> None:
        """Render an input field."""
        # Label
        label_text = self.font_small.render(label, True, self.colors['text_primary'])
        screen.blit(label_text, (self.x + 20, y))
        
        # Input box - increased height
        input_rect = pygame.Rect(self.x + 20, y + 35, self.width - 40, 35)
        border_color = self.colors['input_focus'] if self.active_field == field_name else self.colors['input_border']
        pygame.draw.rect(screen, self.colors['input_background'], input_rect)
        pygame.draw.rect(screen, border_color, input_rect, 2)
        
        # Text
        text = self.font_small.render(value, True, self.colors['text_primary'])
        text_rect = text.get_rect(midleft=(input_rect.left + 5, input_rect.centery))
        screen.blit(text, text_rect)
        
        # Cursor
        if self.active_field == field_name and pygame.time.get_ticks() % 1000 < 500:
            cursor_x = text_rect.right + 2
            pygame.draw.line(screen, self.colors['text_primary'],
                           (cursor_x, input_rect.top + 3),
                           (cursor_x, input_rect.bottom - 3))
    
    def _render_button(self, screen: pygame.Surface, rect: pygame.Rect, text: str) -> None:
        """Render a button."""
        mouse_pos = pygame.mouse.get_pos()
        color = self.colors['button_hover'] if rect.collidepoint(mouse_pos) else self.colors['button_normal']
        
        pygame.draw.rect(screen, color, rect)
        pygame.draw.rect(screen, self.colors['border'], rect, 1)
        
        button_text = self.font_small.render(text, True, self.colors['text_primary'])
        text_rect = button_text.get_rect(center=rect.center)
        screen.blit(button_text, text_rect)
    
    def _check_field_clicks(self, pos: Tuple[int, int]) -> None:
        """Check if a field was clicked."""
        content_y = self.y + 60  # Adjusted for larger title bar

        # Check each field
        fields = [
            ("name", content_y + 35),
            ("width", content_y + 115),
            ("height", content_y + 195)
        ]
        
        for field_name, field_y in fields:
            field_rect = pygame.Rect(self.x + 20, field_y, self.width - 40, 35)
            if field_rect.collidepoint(pos):
                self.active_field = field_name
                break
    
    def _next_field(self) -> None:
        """Move to next field."""
        fields = ["name", "width", "height"]
        current_index = fields.index(self.active_field)
        self.active_field = fields[(current_index + 1) % len(fields)]
    
    def _handle_backspace(self) -> None:
        """Handle backspace key."""
        if self.active_field == "name":
            self.map_name = self.map_name[:-1]
        elif self.active_field == "width":
            self.map_width = self.map_width[:-1]
        elif self.active_field == "height":
            self.map_height = self.map_height[:-1]
    
    def _handle_text_input(self, char: str) -> None:
        """Handle text input."""
        if self.active_field == "name":
            self.map_name += char
        elif self.active_field == "width" and char.isdigit():
            self.map_width += char
        elif self.active_field == "height" and char.isdigit():
            self.map_height += char
    
    def _create_map(self) -> None:
        """Create the map and close dialog."""
        try:
            width = int(self.map_width) if self.map_width else 20
            height = int(self.map_height) if self.map_height else 15
            name = self.map_name if self.map_name else "New Map"
            
            # Validate dimensions
            if width < 5 or width > 100:
                self.logger.warning("Map width must be between 5 and 100")
                return
            if height < 5 or height > 100:
                self.logger.warning("Map height must be between 5 and 100")
                return
            
            # Store the result
            self.map_data = {
                'name': name,
                'width': width,
                'height': height
            }
            
            self.close(DialogResult.OK)
            
        except ValueError:
            self.logger.warning("Invalid map dimensions")
    
    def get_map_data(self) -> dict:
        """Get the map data from the dialog."""
        return getattr(self, 'map_data', {})


class MapBrowserDialog(BaseDialog):
    """Dialog for browsing and selecting maps to open."""

    def __init__(self):
        """Initialize the map browser dialog."""
        # Use scaled dialog dimensions - make it bigger like the transition dialog
        ui_config = get_ui_config()
        dialog_width = ui_config.scale_value(700)  # Increased from 600
        dialog_height = ui_config.scale_value(650)  # Increased from 500
        super().__init__("Open Map", dialog_width, dialog_height)

        # Map list
        self.maps: List[Dict[str, Any]] = []
        self.filtered_maps: List[Dict[str, Any]] = []  # For search functionality
        self.selected_map_index: Optional[int] = None
        self.scroll_offset = 0
        self.max_visible_items = 12  # Increased for bigger dialog

        # Search functionality
        self.search_text: str = ""
        self.search_active: bool = False

        # UI elements
        self.list_rect = pygame.Rect(0, 0, 0, 0)
        self.search_input_rect = pygame.Rect(0, 0, 0, 0)
        self.scrollbar_rect = pygame.Rect(0, 0, 0, 0)

    def open(self, callback: Callable[[DialogResult], None], levels_path: str) -> None:
        """Open the dialog and scan for maps."""
        self.levels_path = Path(levels_path)
        self._scan_maps()
        
        # Reset search
        self.search_text = ""
        self.search_active = False
        self._filter_maps()
        
        super().open(callback)

    def _scan_maps(self) -> None:
        """Scan the levels directory for map files."""
        self.maps = []

        if not self.levels_path.exists():
            self.logger.warning(f"Levels path does not exist: {self.levels_path}")
            return

        # Scan all subdirectories for .map files
        for level_dir in self.levels_path.iterdir():
            if level_dir.is_dir():
                for map_file in level_dir.glob("*.map"):
                    try:
                        # Try to read basic metadata from the map file
                        map_info = self._read_map_metadata(map_file)
                        map_info['path'] = str(map_file)
                        map_info['folder'] = level_dir.name
                        self.maps.append(map_info)
                    except Exception as e:
                        self.logger.warning(f"Failed to read map metadata from {map_file}: {e}")

        self.maps.sort(key=lambda x: x['name'])
        self.filtered_maps = self.maps.copy()  # Initialize filtered list
        self.logger.info(f"Found {len(self.maps)} maps")

    def _read_map_metadata(self, map_file: Path) -> Dict[str, Any]:
        """Read basic metadata from a map file."""
        # Simple implementation - just read the first few lines to get basic info
        metadata = {
            'name': map_file.stem,
            'file': map_file.name,
            'size': 'Unknown',
            'description': ''
        }

        try:
            with open(map_file, 'r') as f:
                lines = f.readlines()

            # Look for metadata in the first few lines
            for line in lines[:20]:  # Check first 20 lines
                line = line.strip()
                if line.startswith('name:'):
                    metadata['name'] = line.split(':', 1)[1].strip()
                elif line.startswith('description:'):
                    metadata['description'] = line.split(':', 1)[1].strip()
                elif line.startswith('width:') and 'height:' in lines:
                    # Try to find dimensions
                    width = line.split(':', 1)[1].strip()
                    for next_line in lines:
                        if next_line.strip().startswith('height:'):
                            height = next_line.split(':', 1)[1].strip()
                            metadata['size'] = f"{width}x{height}"
                            break
        except Exception as e:
            self.logger.debug(f"Could not read detailed metadata from {map_file}: {e}")

        return metadata

    def _filter_maps(self) -> None:
        """Filter maps based on search text."""
        if not self.search_text:
            self.filtered_maps = self.maps.copy()
        else:
            search_lower = self.search_text.lower()
            self.filtered_maps = [
                map_info for map_info in self.maps
                if (search_lower in map_info['name'].lower() or 
                    search_lower in map_info['folder'].lower() or
                    search_lower in map_info.get('description', '').lower())
            ]

        # Reset scroll and selection when filtering
        self.scroll_offset = 0
        self.selected_map_index = None

    def render_content(self, screen: pygame.Surface) -> None:
        """Render the map browser content."""
        content_y = self.y + self.ui_config.scale_value(60)  # After title bar
        content_x = self.x + self.ui_config.scale_value(20)
        content_width = self.width - self.ui_config.scale_value(40)

        # Search box
        search_label = self.font.render("Search maps:", True, self.colors['text_primary'])
        screen.blit(search_label, (content_x, content_y))
        content_y += self.ui_config.scale_value(30)

        self._render_search_box(screen, content_x, content_y)
        content_y += self.ui_config.scale_value(50)

        # Map list with increased height for bigger dialog
        list_height = self.ui_config.scale_value(400)  # Increased height
        self.list_rect = pygame.Rect(content_x, content_y, content_width - self.ui_config.scale_value(20), list_height)

        # Draw list background
        pygame.draw.rect(screen, self.colors['input_background'], self.list_rect)
        pygame.draw.rect(screen, self.colors['border'], self.list_rect, 2)

        # Draw maps list
        if not self.filtered_maps:
            # No maps found message
            no_maps_text = self.font.render("No maps found" if self.search_text else "No maps found in levels directory", 
                                          True, self.colors['text_secondary'])
            text_rect = no_maps_text.get_rect(center=self.list_rect.center)
            screen.blit(no_maps_text, text_rect)
        else:
            self._render_map_list(screen)

        # Draw buttons
        content_y += list_height + self.ui_config.scale_value(20)
        button_y = self.y + self.height - self.ui_config.scale_value(60)
        button_height = self.ui_config.get_element_size(ELEMENT_BUTTON_HEIGHT)

        self.ok_button = pygame.Rect(self.x + self.width - self.ui_config.scale_value(180), button_y, 
                                   self.ui_config.scale_value(80), button_height)
        self.cancel_button = pygame.Rect(self.x + self.width - self.ui_config.scale_value(90), button_y, 
                                       self.ui_config.scale_value(80), button_height)

        # Enable/disable OK button based on selection
        ok_enabled = self.selected_map_index is not None
        ok_color = self.colors['button_normal'] if ok_enabled else self.colors['button_disabled']

        pygame.draw.rect(screen, ok_color, self.ok_button)
        pygame.draw.rect(screen, self.colors['border'], self.ok_button, 1)

        # Render cancel button manually
        mouse_pos = pygame.mouse.get_pos()
        cancel_color = self.colors['button_hover'] if self.cancel_button.collidepoint(mouse_pos) else self.colors['button_normal']
        pygame.draw.rect(screen, cancel_color, self.cancel_button)
        pygame.draw.rect(screen, self.colors['border'], self.cancel_button, 1)
        cancel_text = self.font_small.render("Cancel", True, self.colors['text_primary'])
        cancel_text_rect = cancel_text.get_rect(center=self.cancel_button.center)
        screen.blit(cancel_text, cancel_text_rect)

        # Button text
        ok_text_color = self.colors['text_primary'] if ok_enabled else self.colors['text_disabled']
        ok_text = self.font_small.render("Open", True, ok_text_color)
        ok_text_rect = ok_text.get_rect(center=self.ok_button.center)
        screen.blit(ok_text, ok_text_rect)

    def _render_search_box(self, screen: pygame.Surface, x: int, y: int) -> None:
        """Render the search input box."""
        search_width = self.ui_config.scale_value(300)
        search_height = self.ui_config.get_element_size('input_height')

        self.search_input_rect = pygame.Rect(x, y, search_width, search_height)

        # Background and border
        input_color = self.colors['input_focus'] if self.search_active else self.colors['input_background']
        border_color = self.colors['input_focus'] if self.search_active else self.colors['input_border']
        pygame.draw.rect(screen, input_color, self.search_input_rect)
        pygame.draw.rect(screen, border_color, self.search_input_rect, 2)

        # Text or placeholder
        display_text = self.search_text if self.search_text else "Search maps..."
        text_color = self.colors['text_primary'] if self.search_text else self.colors['text_secondary']
        text = self.font_small.render(display_text, True, text_color)
        text_rect = text.get_rect(midleft=(self.search_input_rect.left + 8, self.search_input_rect.centery))
        screen.blit(text, text_rect)

        # Cursor
        if self.search_active and pygame.time.get_ticks() % 1000 < 500:
            cursor_x = text_rect.right + 2 if self.search_text else self.search_input_rect.left + 8
            pygame.draw.line(screen, self.colors['text_primary'],
                           (cursor_x, self.search_input_rect.top + 4),
                           (cursor_x, self.search_input_rect.bottom - 4))

    def _render_map_list(self, screen: pygame.Surface) -> None:
        """Render the list of maps."""
        if not self.filtered_maps:
            font = self.ui_config.get_font(FONT_SMALL)
            text = font.render("No maps found", True, self.colors['text_secondary'])
            padding = self.ui_config.scale_value(10)
            screen.blit(text, (self.list_rect.x + padding, self.list_rect.y + padding))
            return

        item_height = self.ui_config.scale_value(60)
        visible_items = min(self.max_visible_items, len(self.filtered_maps))

        for i in range(visible_items):
            map_index = i + self.scroll_offset
            if map_index >= len(self.filtered_maps):
                break

            map_info = self.filtered_maps[map_index]
            item_y = self.list_rect.y + (i * item_height)
            item_rect = pygame.Rect(self.list_rect.x + 5, item_y + 2, self.list_rect.width - 10, item_height - 4)

            # Highlight selected item
            if map_index == self.selected_map_index:
                pygame.draw.rect(screen, self.colors['button_hover'], item_rect)

            # Map name
            name_text = self.font.render(map_info['name'], True, self.colors['text_primary'])
            screen.blit(name_text, (item_rect.x + 10, item_rect.y + 5))

            # Map details
            details = f"Folder: {map_info['folder']} | Size: {map_info['size']} | File: {map_info['file']}"
            details_text = self.font_small.render(details, True, self.colors['text_secondary'])
            screen.blit(details_text, (item_rect.x + 10, item_rect.y + 25))

            # Description if available
            if map_info['description']:
                desc_text = self.font_small.render(map_info['description'], True, self.colors['text_secondary'])
                screen.blit(desc_text, (item_rect.x + 10, item_rect.y + 40))

        # Render scrollbar if needed
        if len(self.filtered_maps) > self.max_visible_items:
            self._render_scrollbar(screen)

    def _render_scrollbar(self, screen: pygame.Surface) -> None:
        """Render scrollbar for the map list."""
        scrollbar_width = self.ui_config.scale_value(8)
        scrollbar_x = self.list_rect.right - scrollbar_width - 2
        scrollbar_rect = pygame.Rect(scrollbar_x, self.list_rect.y + 2,
                                   scrollbar_width, self.list_rect.height - 4)

        # Background
        pygame.draw.rect(screen, self.colors['scrollbar'], scrollbar_rect)

        # Thumb
        total_items = len(self.filtered_maps)
        if total_items > self.max_visible_items:
            thumb_height = max(20, int(scrollbar_rect.height * self.max_visible_items / total_items))
            thumb_y = scrollbar_rect.y + int((scrollbar_rect.height - thumb_height) * self.scroll_offset / (total_items - self.max_visible_items))
            thumb_rect = pygame.Rect(scrollbar_x, thumb_y, scrollbar_width, thumb_height)
            pygame.draw.rect(screen, self.colors['scrollbar_thumb'], thumb_rect)

    def handle_event(self, event: pygame.event.Event) -> bool:
        """Handle events."""
        if super().handle_event(event):
            return True
            
        if event.type == pygame.MOUSEBUTTONDOWN:
            if event.button == 1:  # Left click
                self.handle_click(event.pos)
                return True
            elif event.button == 4:  # Mouse wheel up
                if self.list_rect.collidepoint(pygame.mouse.get_pos()):
                    self.scroll_offset = max(0, self.scroll_offset - 1)
                    return True
            elif event.button == 5:  # Mouse wheel down
                if self.list_rect.collidepoint(pygame.mouse.get_pos()):
                    max_scroll = max(0, len(self.filtered_maps) - self.max_visible_items)
                    self.scroll_offset = min(max_scroll, self.scroll_offset + 1)
                    return True
        elif event.type == pygame.KEYDOWN:
            if self.search_active:
                self._handle_search_input(event)
                return True
            elif event.key == pygame.K_ESCAPE:
                self.close(DialogResult.CANCEL)
                return True
            elif event.key == pygame.K_UP:
                if self.selected_map_index is not None and self.selected_map_index > 0:
                    self.selected_map_index -= 1
                    # Adjust scroll if needed
                    if self.selected_map_index < self.scroll_offset:
                        self.scroll_offset = self.selected_map_index
                return True
            elif event.key == pygame.K_DOWN:
                if self.selected_map_index is not None and self.selected_map_index < len(self.filtered_maps) - 1:
                    self.selected_map_index += 1
                    # Adjust scroll if needed
                    if self.selected_map_index >= self.scroll_offset + self.max_visible_items:
                        self.scroll_offset = self.selected_map_index - self.max_visible_items + 1
                elif self.selected_map_index is None and self.filtered_maps:
                    self.selected_map_index = 0
                return True
            elif event.key == pygame.K_RETURN:
                if self.selected_map_index is not None:
                    self.close(DialogResult.OK)
                return True
        
        return False

    def _handle_search_input(self, event: pygame.event.Event) -> None:
        """Handle search input."""
        if event.key == pygame.K_RETURN or event.key == pygame.K_ESCAPE:
            self.search_active = False
        elif event.key == pygame.K_BACKSPACE:
            self.search_text = self.search_text[:-1]
            self._filter_maps()
        elif event.unicode.isprintable() and event.unicode not in ['\r', '\n']:
            self.search_text += event.unicode
            self._filter_maps()

    def handle_click(self, pos: Tuple[int, int]) -> None:
        """Handle mouse clicks."""
        # Check search input
        if self.search_input_rect.collidepoint(pos):
            self.search_active = True
            return

        # Deactivate search if clicking elsewhere
        self.search_active = False

        if hasattr(self, 'list_rect') and self.list_rect.collidepoint(pos):
            # Click in map list
            relative_y = pos[1] - self.list_rect.y
            item_height = self.ui_config.scale_value(60)
            item_index = relative_y // item_height
            map_index = item_index + self.scroll_offset

            if 0 <= map_index < len(self.filtered_maps):
                self.selected_map_index = map_index

        elif hasattr(self, 'ok_button') and self.ok_button.collidepoint(pos):
            if self.selected_map_index is not None:
                self.close(DialogResult.OK)

        elif hasattr(self, 'cancel_button') and self.cancel_button.collidepoint(pos):
            self.close(DialogResult.CANCEL)

    def get_selected_map(self) -> Optional[Dict[str, Any]]:
        """Get the selected map info."""
        if self.selected_map_index is not None and 0 <= self.selected_map_index < len(self.filtered_maps):
            return self.filtered_maps[self.selected_map_index]
        return None


class SaveAsDialog(BaseDialog):
    """Dialog for saving a map with a new name and location."""

    def __init__(self):
        """Initialize the save as dialog."""
        super().__init__("Save Map As", 500, 300)

        # Form fields
        self.folder_name = ""
        self.file_name = ""
        self.active_field = "folder"

        # UI elements
        self.folder_field_rect = pygame.Rect(0, 0, 0, 0)
        self.file_field_rect = pygame.Rect(0, 0, 0, 0)

    def open(self, callback: Callable[[DialogResult], None], levels_path: str, current_name: str = "") -> None:
        """Open the dialog."""
        self.levels_path = Path(levels_path)
        self.file_name = current_name or "new_map"
        self.folder_name = ""
        self.active_field = "folder"
        super().open(callback)

    def render_content(self, screen: pygame.Surface) -> None:
        """Render the save as dialog content."""
        content_y = self.y + self.ui_config.scale_value(60)  # After title bar

        # Instructions
        instruction_text = self.font_small.render("Create a new folder under 'levels' to save your map:", True, self.colors['text_primary'])
        screen.blit(instruction_text, (self.x + 20, content_y))

        # Folder name field
        self._render_field(screen, "Folder Name:", self.folder_name, content_y + 40, "folder")

        # File name field
        self._render_field(screen, "File Name:", self.file_name, content_y + 120, "file")

        # Preview path
        preview_path = f"levels/{self.folder_name}/{self.file_name}.map" if self.folder_name and self.file_name else "levels/[folder]/[filename].map"
        preview_text = self.font_small.render(f"Will save to: {preview_path}", True, self.colors['text_secondary'])
        screen.blit(preview_text, (self.x + 20, content_y + 200))

        # Buttons
        button_y = self.y + self.height - 60
        button_height = self.ui_config.get_element_size(ELEMENT_BUTTON_HEIGHT)

        self.ok_button = pygame.Rect(self.x + self.width - 180, button_y, 80, button_height)
        self.cancel_button = pygame.Rect(self.x + self.width - 90, button_y, 80, button_height)

        # Enable/disable OK button based on valid input
        ok_enabled = bool(self.folder_name.strip() and self.file_name.strip())
        ok_color = self.colors['button_normal'] if ok_enabled else self.colors['button_disabled']

        pygame.draw.rect(screen, ok_color, self.ok_button)
        pygame.draw.rect(screen, self.colors['border'], self.ok_button, 1)

        # Render cancel button manually
        mouse_pos = pygame.mouse.get_pos()
        cancel_color = self.colors['button_hover'] if self.cancel_button.collidepoint(mouse_pos) else self.colors['button_normal']
        pygame.draw.rect(screen, cancel_color, self.cancel_button)
        pygame.draw.rect(screen, self.colors['border'], self.cancel_button, 1)
        cancel_text = self.font_small.render("Cancel", True, self.colors['text_primary'])
        cancel_text_rect = cancel_text.get_rect(center=self.cancel_button.center)
        screen.blit(cancel_text, cancel_text_rect)

        # Button text
        ok_text_color = self.colors['text_primary'] if ok_enabled else self.colors['text_disabled']
        ok_text = self.font_small.render("Save", True, ok_text_color)
        ok_text_rect = ok_text.get_rect(center=self.ok_button.center)
        screen.blit(ok_text, ok_text_rect)

    def _render_field(self, screen: pygame.Surface, label: str, value: str, y: int, field_name: str) -> None:
        """Render an input field."""
        # Label
        label_text = self.font_small.render(label, True, self.colors['text_primary'])
        screen.blit(label_text, (self.x + 20, y))

        # Input box - increased height
        input_rect = pygame.Rect(self.x + 20, y + 35, self.width - 40, 35)
        border_color = self.colors['input_focus'] if self.active_field == field_name else self.colors['input_border']
        pygame.draw.rect(screen, self.colors['input_background'], input_rect)
        pygame.draw.rect(screen, border_color, input_rect, 2)

        # Text
        text = self.font_small.render(value, True, self.colors['text_primary'])
        text_rect = text.get_rect(midleft=(input_rect.left + 5, input_rect.centery))
        screen.blit(text, text_rect)

        # Cursor
        if self.active_field == field_name and pygame.time.get_ticks() % 1000 < 500:
            cursor_x = text_rect.right + 2
            pygame.draw.line(screen, self.colors['text_primary'],
                           (cursor_x, input_rect.top + 3),
                           (cursor_x, input_rect.bottom - 3))

    def handle_event(self, event: pygame.event.Event) -> bool:
        """Handle events."""
        if event.type == pygame.KEYDOWN:
            if event.key == pygame.K_ESCAPE:
                self.close(DialogResult.CANCEL)
                return True
            else:
                self.handle_key(event.key, event.unicode)
                return True
        elif event.type == pygame.MOUSEBUTTONDOWN:
            if event.button == 1:  # Left click
                self.handle_click(event.pos)
                return True
        
        return False

    def handle_click(self, pos: Tuple[int, int]) -> None:
        """Handle mouse clicks."""
        self._check_field_clicks(pos)

        if hasattr(self, 'ok_button') and self.ok_button.collidepoint(pos):
            if self.folder_name.strip() and self.file_name.strip():
                self._validate_and_save()

        elif hasattr(self, 'cancel_button') and self.cancel_button.collidepoint(pos):
            self.close(DialogResult.CANCEL)

    def handle_key(self, key: int, unicode_char: str) -> None:
        """Handle keyboard input."""
        if key == pygame.K_TAB:
            # Switch between fields
            self.active_field = "file" if self.active_field == "folder" else "folder"
        elif key == pygame.K_RETURN:
            if self.folder_name.strip() and self.file_name.strip():
                self._validate_and_save()
        elif key == pygame.K_ESCAPE:
            self.close(DialogResult.CANCEL)
        elif key == pygame.K_BACKSPACE:
            if self.active_field == "folder" and self.folder_name:
                self.folder_name = self.folder_name[:-1]
            elif self.active_field == "file" and self.file_name:
                self.file_name = self.file_name[:-1]
        elif unicode_char and unicode_char.isprintable():
            # Filter out invalid filename characters
            if unicode_char not in '<>:"/\\|?*':
                if self.active_field == "folder":
                    self.folder_name += unicode_char
                elif self.active_field == "file":
                    self.file_name += unicode_char

    def _check_field_clicks(self, pos: Tuple[int, int]) -> None:
        """Check if a field was clicked."""
        content_y = self.y + self.ui_config.scale_value(60)

        # Folder field
        folder_rect = pygame.Rect(self.x + 20, content_y + 75, self.width - 40, 35)
        if folder_rect.collidepoint(pos):
            self.active_field = "folder"

        # File field
        file_rect = pygame.Rect(self.x + 20, content_y + 155, self.width - 40, 35)
        if file_rect.collidepoint(pos):
            self.active_field = "file"

    def _validate_and_save(self) -> None:
        """Validate input and close dialog."""
        try:
            # Basic validation
            folder_name = self.folder_name.strip()
            file_name = self.file_name.strip()

            if not folder_name or not file_name:
                return

            # Store the save data
            self.save_data = {
                'folder': folder_name,
                'filename': file_name,
                'full_path': self.levels_path / folder_name / f"{file_name}.map"
            }

            self.close(DialogResult.OK)

        except Exception as e:
            self.logger.error(f"Error validating save data: {e}")

    def get_save_data(self) -> Optional[Dict[str, Any]]:
        """Get the save data from the dialog."""
        return getattr(self, 'save_data', None)


class TransitionDialog(BaseDialog):
    """Dialog for configuring level transitions."""

    def __init__(self):
        """Initialize the transition dialog."""
        # Use scaled dialog dimensions - make it much bigger
        ui_config = get_ui_config()
        dialog_width = ui_config.scale_value(700)  # Increased from 500
        dialog_height = ui_config.scale_value(650)  # Increased from 480
        super().__init__("Configure Transition", dialog_width, dialog_height)

        # Transition configuration
        self.transition_number: str = "0"
        self.target_map: str = ""
        self.spawn_point: str = "0"
        self.tile_asset: str = "exit_portal"  # Default tile asset
        self.levels_path: Optional[Path] = None

        # Available options
        self.available_maps: List[str] = []
        self.filtered_maps: List[str] = []  # For search functionality
        self.available_spawn_points: List[str] = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"]
        self.available_transition_numbers: List[str] = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"]

        # Transition usage tracking for color coding
        self.used_transitions: Dict[str, bool] = {}  # Maps transition number to whether it's in use
        self.target_map_transitions: Dict[str, bool] = {}  # Maps spawn point to whether it exists in target map

        # UI state
        self.selected_map_index: int = -1
        self.selected_spawn_index: int = 0
        self.selected_transition_index: int = 0
        self.map_scroll_offset: int = 0
        self.max_visible_maps: int = 10  # Increased for bigger dialog

        # Search functionality
        self.search_text: str = ""
        self.search_active: bool = False

        # Input fields
        self.tile_asset_input_active: bool = False

        # UI rects
        self.transition_list_rect = pygame.Rect(0, 0, 0, 0)
        self.tile_asset_input_rect = pygame.Rect(0, 0, 0, 0)
        self.search_input_rect = pygame.Rect(0, 0, 0, 0)
        self.map_list_rect = pygame.Rect(0, 0, 0, 0)
        self.spawn_list_rect = pygame.Rect(0, 0, 0, 0)
        self.ok_button_rect = pygame.Rect(0, 0, 0, 0)
        self.cancel_button_rect = pygame.Rect(0, 0, 0, 0)

    def open(self, callback: Callable[[DialogResult], None], levels_path: str,
             transition_number: str = "0", current_target: str = "", current_spawn: str = "0",
             current_tile_asset: str = "exit_portal", used_transitions: Dict[str, bool] = None,
             target_map_transitions: Dict[str, bool] = None) -> None:
        """Open the dialog with initial values."""
        self.levels_path = Path(levels_path)
        self.transition_number = transition_number
        self.target_map = current_target
        self.spawn_point = current_spawn
        self.tile_asset = current_tile_asset
        
        # Store transition usage information for color coding
        self.used_transitions = used_transitions or {}
        self.target_map_transitions = target_map_transitions or {}

        # Scan for available maps
        self._scan_available_maps()

        # Reset search
        self.search_text = ""
        self.search_active = False
        self._filter_maps()

        # Set initial selections
        if self.target_map in self.filtered_maps:
            self.selected_map_index = self.filtered_maps.index(self.target_map)
            # Scan target map transitions for initial target
            self._scan_target_map_transitions(self.target_map)
        else:
            self.selected_map_index = -1

        if self.spawn_point in self.available_spawn_points:
            self.selected_spawn_index = self.available_spawn_points.index(self.spawn_point)

        if self.transition_number in self.available_transition_numbers:
            self.selected_transition_index = self.available_transition_numbers.index(self.transition_number)

        super().open(callback)

    def _scan_available_maps(self) -> None:
        """Scan for available map directories."""
        self.available_maps = []

        if not self.levels_path or not self.levels_path.exists():
            self.logger.warning(f"Levels path does not exist: {self.levels_path}")
            return

        # Get all level directories that contain .map files
        for level_dir in self.levels_path.iterdir():
            if level_dir.is_dir() and any(level_dir.glob("*.map")):
                self.available_maps.append(level_dir.name)
                self.logger.info(f"Found map directory: {level_dir.name}")

        self.available_maps.sort()
        self.filtered_maps = self.available_maps.copy()  # Initialize filtered list
        self.logger.info(f"Total available maps: {len(self.available_maps)}")

    def _scan_target_map_transitions(self, target_map_name: str) -> None:
        """Scan the target map for existing transitions to color-code spawn points."""
        self.target_map_transitions = {}
        
        if not target_map_name or not self.levels_path:
            return
            
        try:
            # Try to get transitions from target map's level config
            target_level_dir = self.levels_path / target_map_name
            config_file = target_level_dir / "level_config.py"
            
            if config_file.exists():
                # Import the level config dynamically
                import importlib.util
                spec = importlib.util.spec_from_file_location("level_config", config_file)
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                
                level_config = getattr(module, "LEVEL_CONFIG", {})
                exits = level_config.get("exits", {})
                
                # Mark all transition IDs (exit numbers) that exist in the target map
                for exit_id in exits.keys():
                    if exit_id in self.available_spawn_points:
                        self.target_map_transitions[exit_id] = True
                        
                self.logger.info(f"Found {len(self.target_map_transitions)} existing transitions in target map {target_map_name}")
                        
        except Exception as e:
            self.logger.warning(f"Failed to scan target map transitions for {target_map_name}: {e}")

    def _filter_maps(self) -> None:
        """Filter maps based on search text."""
        if not self.search_text:
            self.filtered_maps = self.available_maps.copy()
        else:
            search_lower = self.search_text.lower()
            self.filtered_maps = [
                map_name for map_name in self.available_maps
                if search_lower in map_name.lower()
            ]

        # Reset scroll and selection when filtering
        self.map_scroll_offset = 0
        if self.target_map in self.filtered_maps:
            self.selected_map_index = self.filtered_maps.index(self.target_map)
        else:
            self.selected_map_index = -1

    def handle_event(self, event: pygame.event.Event) -> bool:
        """Handle dialog events."""
        if super().handle_event(event):
            return True

        if event.type == pygame.MOUSEBUTTONDOWN:
            if event.button == 1:  # Left click
                self._handle_left_click(event.pos)
                return True
            elif event.button == 4:  # Mouse wheel up
                if self.map_list_rect.collidepoint(pygame.mouse.get_pos()):
                    self.map_scroll_offset = max(0, self.map_scroll_offset - 1)
                    return True
            elif event.button == 5:  # Mouse wheel down
                if self.map_list_rect.collidepoint(pygame.mouse.get_pos()):
                    max_scroll = max(0, len(self.filtered_maps) - self.max_visible_maps)
                    self.map_scroll_offset = min(max_scroll, self.map_scroll_offset + 1)
                    return True
        elif event.type == pygame.KEYDOWN:
            if self.search_active:
                self._handle_search_input(event)
                return True
            elif self.tile_asset_input_active:
                self._handle_tile_asset_input(event)
                return True
            else:
                self._handle_navigation_keys(event)
                return True

        return False

    def _handle_left_click(self, pos: Tuple[int, int]) -> None:
        """Handle left mouse click."""
        x, y = pos

        # Check search input
        if self.search_input_rect.collidepoint(pos):
            self.search_active = True
            self.tile_asset_input_active = False
            return

        # Check tile asset input
        if self.tile_asset_input_rect.collidepoint(pos):
            self.tile_asset_input_active = True
            self.search_active = False
            return

        # Deactivate inputs if clicking elsewhere
        self.tile_asset_input_active = False
        self.search_active = False

        # Check transition number list
        if self.transition_list_rect.collidepoint(pos):
            relative_x = x - self.transition_list_rect.x
            item_width = self.ui_config.scale_value(20)
            clicked_index = relative_x // item_width

            if 0 <= clicked_index < len(self.available_transition_numbers):
                self.selected_transition_index = clicked_index
                self.transition_number = self.available_transition_numbers[clicked_index]
            return

        # Check map list
        if self.map_list_rect.collidepoint(pos):
            relative_y = y - self.map_list_rect.y
            item_height = self.ui_config.scale_value(30)
            clicked_index = (relative_y // item_height) + self.map_scroll_offset

            if 0 <= clicked_index < len(self.filtered_maps):
                self.selected_map_index = clicked_index
                self.target_map = self.filtered_maps[clicked_index]
                # Update target map transitions when map changes
                self._scan_target_map_transitions(self.target_map)
            return

        # Check spawn point list
        if self.spawn_list_rect.collidepoint(pos):
            relative_x = x - self.spawn_list_rect.x
            item_width = self.ui_config.scale_value(20)
            clicked_index = relative_x // item_width

            if 0 <= clicked_index < len(self.available_spawn_points):
                self.selected_spawn_index = clicked_index
                self.spawn_point = self.available_spawn_points[clicked_index]
            return

        # Check buttons
        self._handle_button_click(pos)

    def _handle_search_input(self, event: pygame.event.Event) -> None:
        """Handle search input."""
        if event.key == pygame.K_RETURN or event.key == pygame.K_ESCAPE:
            self.search_active = False
        elif event.key == pygame.K_BACKSPACE:
            self.search_text = self.search_text[:-1]
            self._filter_maps()
        elif event.unicode.isprintable() and event.unicode not in ['\r', '\n']:
            self.search_text += event.unicode
            self._filter_maps()

    def _handle_tile_asset_input(self, event: pygame.event.Event) -> None:
        """Handle tile asset input."""
        if event.key == pygame.K_RETURN or event.key == pygame.K_ESCAPE:
            self.tile_asset_input_active = False
        elif event.key == pygame.K_BACKSPACE:
            self.tile_asset = self.tile_asset[:-1]
        elif event.unicode.isprintable() and event.unicode not in ['\r', '\n']:
            self.tile_asset += event.unicode

    def _handle_navigation_keys(self, event: pygame.event.Event) -> None:
        """Handle navigation keys."""
        if event.key == pygame.K_UP:
            if self.selected_map_index > 0:
                self.selected_map_index -= 1
                self.target_map = self.filtered_maps[self.selected_map_index]
                # Update target map transitions when map changes
                self._scan_target_map_transitions(self.target_map)
                # Adjust scroll if needed
                if self.selected_map_index < self.map_scroll_offset:
                    self.map_scroll_offset = self.selected_map_index
        elif event.key == pygame.K_DOWN:
            if self.selected_map_index < len(self.filtered_maps) - 1:
                self.selected_map_index += 1
                self.target_map = self.filtered_maps[self.selected_map_index]
                # Update target map transitions when map changes
                self._scan_target_map_transitions(self.target_map)
                # Adjust scroll if needed
                if self.selected_map_index >= self.map_scroll_offset + self.max_visible_maps:
                    self.map_scroll_offset = self.selected_map_index - self.max_visible_maps + 1

    def render_content(self, screen: pygame.Surface) -> None:
        """Render the transition dialog content."""
        content_y = self.y + self.ui_config.scale_value(60)  # After title bar
        content_x = self.x + self.ui_config.scale_value(20)
        content_width = self.width - self.ui_config.scale_value(40)

        # Current map transition section
        label = self.font.render("Current map transition:", True, self.colors['text_primary'])
        screen.blit(label, (content_x, content_y))
        content_y += self.ui_config.scale_value(30)

        transition_list_width = self.ui_config.scale_value(200)
        transition_list_height = self.ui_config.scale_value(40)
        self.transition_list_rect = pygame.Rect(content_x, content_y, transition_list_width, transition_list_height)
        pygame.draw.rect(screen, self.colors['background_light'], self.transition_list_rect)
        pygame.draw.rect(screen, self.colors['border'], self.transition_list_rect, 2)

        # Render transition number list (horizontal)
        self._render_transition_list(screen)

        content_y += transition_list_height + self.ui_config.scale_value(20)

        # Custom tile asset section
        self._render_tile_asset_field(screen, content_x, content_y)
        content_y += self.ui_config.scale_value(50)

        # Target map section
        label = self.font.render("Target Map:", True, self.colors['text_primary'])
        screen.blit(label, (content_x, content_y))
        content_y += self.ui_config.scale_value(30)

        # Search box
        self._render_search_box(screen, content_x, content_y)
        content_y += self.ui_config.scale_value(50)

        # Map list - increased height for bigger dialog
        list_height = self.ui_config.scale_value(250)
        self.map_list_rect = pygame.Rect(content_x, content_y, content_width - self.ui_config.scale_value(20), list_height)
        pygame.draw.rect(screen, self.colors['background_light'], self.map_list_rect)
        pygame.draw.rect(screen, self.colors['border'], self.map_list_rect, 2)

        # Render map list
        self._render_map_list(screen)

        content_y += list_height + self.ui_config.scale_value(20)

        # Target map transition section
        label = self.font.render("Target map transition:", True, self.colors['text_primary'])
        screen.blit(label, (content_x, content_y))
        content_y += self.ui_config.scale_value(30)

        spawn_list_width = self.ui_config.scale_value(200)
        spawn_list_height = self.ui_config.scale_value(40)
        self.spawn_list_rect = pygame.Rect(content_x, content_y, spawn_list_width, spawn_list_height)
        pygame.draw.rect(screen, self.colors['background_light'], self.spawn_list_rect)
        pygame.draw.rect(screen, self.colors['border'], self.spawn_list_rect, 2)

        # Render spawn point list (horizontal)
        self._render_spawn_list(screen)

        # Render buttons
        self._render_buttons(screen)

    def _render_search_box(self, screen: pygame.Surface, x: int, y: int) -> None:
        """Render the search input box."""
        search_width = self.ui_config.scale_value(300)
        search_height = self.ui_config.get_element_size('input_height')

        self.search_input_rect = pygame.Rect(x, y, search_width, search_height)

        # Background and border
        input_color = self.colors['input_focus'] if self.search_active else self.colors['input_background']
        border_color = self.colors['input_focus'] if self.search_active else self.colors['input_border']
        pygame.draw.rect(screen, input_color, self.search_input_rect)
        pygame.draw.rect(screen, border_color, self.search_input_rect, 2)

        # Text or placeholder
        display_text = self.search_text if self.search_text else "Search maps..."
        text_color = self.colors['text_primary'] if self.search_text else self.colors['text_secondary']
        text = self.font_small.render(display_text, True, text_color)
        text_rect = text.get_rect(midleft=(self.search_input_rect.left + 8, self.search_input_rect.centery))
        screen.blit(text, text_rect)

        # Cursor
        if self.search_active and pygame.time.get_ticks() % 1000 < 500:
            cursor_x = text_rect.right + 2 if self.search_text else self.search_input_rect.left + 8
            pygame.draw.line(screen, self.colors['text_primary'],
                           (cursor_x, self.search_input_rect.top + 5),
                           (cursor_x, self.search_input_rect.bottom - 5), 2)

    def _render_tile_asset_field(self, screen: pygame.Surface, x: int, y: int) -> None:
        """Render the tile asset input field."""
        label = self.font.render("Tile Asset:", True, self.colors['text_primary'])
        screen.blit(label, (x, y))

        input_width = self.ui_config.scale_value(300)
        input_height = self.ui_config.get_element_size('input_height')
        label_offset = self.ui_config.scale_value(100)

        self.tile_asset_input_rect = pygame.Rect(x + label_offset, y - 5, input_width, input_height)
        input_color = self.colors['input_focus'] if self.tile_asset_input_active else self.colors['input_background']
        border_color = self.colors['input_focus'] if self.tile_asset_input_active else self.colors['input_border']
        pygame.draw.rect(screen, input_color, self.tile_asset_input_rect)
        pygame.draw.rect(screen, border_color, self.tile_asset_input_rect, 2)

        input_text = self.font_small.render(self.tile_asset, True, self.colors['text_primary'])
        text_rect = input_text.get_rect(midleft=(self.tile_asset_input_rect.left + 8, self.tile_asset_input_rect.centery))
        screen.blit(input_text, text_rect)

        # Cursor
        if self.tile_asset_input_active and pygame.time.get_ticks() % 1000 < 500:
            cursor_x = text_rect.right + 2
            pygame.draw.line(screen, self.colors['text_primary'],
                           (cursor_x, self.tile_asset_input_rect.top + 5),
                           (cursor_x, self.tile_asset_input_rect.bottom - 5), 2)

    def _render_map_list(self, screen: pygame.Surface) -> None:
        """Render the map selection list."""
        if not self.filtered_maps:
            font = self.ui_config.get_font(FONT_SMALL)
            text = font.render("No maps found", True, self.colors['text_secondary'])
            padding = self.ui_config.scale_value(10)
            screen.blit(text, (self.map_list_rect.x + padding, self.map_list_rect.y + padding))
            return

        font = self.ui_config.get_font(FONT_SMALL)
        item_height = self.ui_config.scale_value(30)
        visible_items = min(self.max_visible_maps, len(self.filtered_maps))

        for i in range(visible_items):
            map_index = i + self.map_scroll_offset
            if map_index >= len(self.filtered_maps):
                break

            map_name = self.filtered_maps[map_index]
            y_pos = self.map_list_rect.y + (i * item_height)

            # Highlight selected item
            if map_index == self.selected_map_index:
                highlight_rect = pygame.Rect(self.map_list_rect.x, y_pos, self.map_list_rect.width, item_height)
                pygame.draw.rect(screen, self.colors['button_hover'], highlight_rect)

            text = font.render(map_name, True, self.colors['text_primary'])
            padding = self.ui_config.scale_value(10)
            text_padding = self.ui_config.scale_value(5)
            screen.blit(text, (self.map_list_rect.x + padding, y_pos + text_padding))

        # Render scrollbar if needed
        if len(self.filtered_maps) > self.max_visible_maps:
            self._render_scrollbar(screen)

    def _render_scrollbar(self, screen: pygame.Surface) -> None:
        """Render scrollbar for the map list."""
        scrollbar_width = self.ui_config.scale_value(8)
        scrollbar_x = self.map_list_rect.right - scrollbar_width - 2
        scrollbar_rect = pygame.Rect(scrollbar_x, self.map_list_rect.y + 2,
                                   scrollbar_width, self.map_list_rect.height - 4)

        # Background
        pygame.draw.rect(screen, self.colors['scrollbar'], scrollbar_rect)

        # Thumb
        total_items = len(self.filtered_maps)
        thumb_height = max(20, int(scrollbar_rect.height * self.max_visible_maps / total_items))
        thumb_y = scrollbar_rect.y + int((scrollbar_rect.height - thumb_height) * self.map_scroll_offset / (total_items - self.max_visible_maps))
        thumb_rect = pygame.Rect(scrollbar_x, thumb_y, scrollbar_width, thumb_height)
        pygame.draw.rect(screen, self.colors['scrollbar_thumb'], thumb_rect)

    def _render_transition_list(self, screen: pygame.Surface) -> None:
        """Render the transition number selection list."""
        font = self.ui_config.get_font(FONT_SMALL)
        item_width = self.ui_config.scale_value(20)

        for i, transition_number in enumerate(self.available_transition_numbers):
            x_pos = self.transition_list_rect.x + (i * item_width)
            y_pos = self.transition_list_rect.y + self.ui_config.scale_value(5)

            # Determine background color based on usage
            background_color = None
            if transition_number in self.used_transitions and self.used_transitions[transition_number]:
                # Red background for transitions already in use (excluding current one)
                if transition_number != self.transition_number:
                    background_color = (255, 100, 100, 180)  # Semi-transparent red
            
            # Draw background if needed
            if background_color:
                bg_rect = pygame.Rect(x_pos, y_pos, item_width, self.ui_config.scale_value(20))
                bg_surface = pygame.Surface((item_width, self.ui_config.scale_value(20)), pygame.SRCALPHA)
                bg_surface.fill(background_color)
                screen.blit(bg_surface, bg_rect)

            # Highlight selected item
            if i == self.selected_transition_index:
                highlight_height = self.ui_config.scale_value(20)
                highlight_rect = pygame.Rect(x_pos, y_pos, item_width, highlight_height)
                pygame.draw.rect(screen, self.colors['button_hover'], highlight_rect)

            text = font.render(transition_number, True, self.colors['text_primary'])
            text_y_offset = self.ui_config.scale_value(10)
            text_rect = text.get_rect(center=(x_pos + item_width // 2, y_pos + text_y_offset))
            screen.blit(text, text_rect)

    def _render_spawn_list(self, screen: pygame.Surface) -> None:
        """Render the spawn point selection list."""
        font = self.ui_config.get_font(FONT_SMALL)
        item_width = self.ui_config.scale_value(20)

        for i, spawn_point in enumerate(self.available_spawn_points):
            x_pos = self.spawn_list_rect.x + (i * item_width)
            y_pos = self.spawn_list_rect.y + self.ui_config.scale_value(5)

            # Determine background color based on whether spawn point exists in target map
            background_color = None
            if spawn_point in self.target_map_transitions and self.target_map_transitions[spawn_point]:
                # Blue background for spawn points that exist in the target map
                background_color = (100, 150, 255, 180)  # Semi-transparent blue
            
            # Draw background if needed
            if background_color:
                bg_rect = pygame.Rect(x_pos, y_pos, item_width, self.ui_config.scale_value(20))
                bg_surface = pygame.Surface((item_width, self.ui_config.scale_value(20)), pygame.SRCALPHA)
                bg_surface.fill(background_color)
                screen.blit(bg_surface, bg_rect)

            # Highlight selected item
            if i == self.selected_spawn_index:
                highlight_height = self.ui_config.scale_value(20)
                highlight_rect = pygame.Rect(x_pos, y_pos, item_width, highlight_height)
                pygame.draw.rect(screen, self.colors['button_hover'], highlight_rect)

            text = font.render(spawn_point, True, self.colors['text_primary'])
            text_y_offset = self.ui_config.scale_value(10)
            text_rect = text.get_rect(center=(x_pos + item_width // 2, y_pos + text_y_offset))
            screen.blit(text, text_rect)

    def _render_buttons(self, screen: pygame.Surface) -> None:
        """Render OK and Cancel buttons."""
        button_width = self.ui_config.scale_value(80)
        button_height = self.ui_config.get_element_size('button_height')
        button_spacing = self.ui_config.get_spacing('normal')

        # Position buttons at bottom of dialog
        bottom_margin = self.ui_config.scale_value(50)
        side_margin = self.ui_config.scale_value(20)
        buttons_y = self.y + self.height - bottom_margin
        cancel_x = self.x + self.width - button_width - side_margin
        ok_x = cancel_x - button_width - button_spacing

        # OK button
        self.ok_button_rect = pygame.Rect(ok_x, buttons_y, button_width, button_height)
        pygame.draw.rect(screen, self.colors['button_normal'], self.ok_button_rect)
        pygame.draw.rect(screen, self.colors['border'], self.ok_button_rect, 2)

        ok_text = self.font.render("OK", True, self.colors['text_primary'])
        ok_text_rect = ok_text.get_rect(center=self.ok_button_rect.center)
        screen.blit(ok_text, ok_text_rect)

        # Cancel button
        self.cancel_button_rect = pygame.Rect(cancel_x, buttons_y, button_width, button_height)
        pygame.draw.rect(screen, self.colors['button_normal'], self.cancel_button_rect)
        pygame.draw.rect(screen, self.colors['border'], self.cancel_button_rect, 2)

        cancel_text = self.font.render("Cancel", True, self.colors['text_primary'])
        cancel_text_rect = cancel_text.get_rect(center=self.cancel_button_rect.center)
        screen.blit(cancel_text, cancel_text_rect)

    def _handle_button_click(self, pos: Tuple[int, int]) -> None:
        """Handle button clicks."""
        if self.ok_button_rect.collidepoint(pos):
            self.close(DialogResult.OK)
        elif self.cancel_button_rect.collidepoint(pos):
            self.close(DialogResult.CANCEL)

    def get_transition_config(self) -> Dict[str, str]:
        """Get the configured transition data."""
        return {
            'exit_number': self.transition_number,
            'target_map': self.target_map,
            'spawn_point': self.spawn_point,
            'tile_asset': self.tile_asset
        }


