"""
Info Panel

This module provides an info panel that displays detailed information about selected tiles and entities.
"""

import pygame
from typing import Optional, Dict, Any, List, Tuple

from src.infrastructure.logging import get_logger
from src.game_data.npcs import get_npc_definition, get_npc_data_id_mapping
from ..ui_config import get_ui_config, get_colors, FONT_NORMAL, FONT_SMALL, SPACING_NORMAL, SPACING_SMALL


class InfoPanel:
    """
    Info panel for displaying detailed tile and entity information.
    
    This panel appears when a tile is selected in selection mode and shows
    information about the tile type, any entities present, and special properties.
    """
    
    def __init__(self, x: int, y: int, width: int, height: int):
        """
        Initialize the info panel.
        
        Args:
            x: Panel X position
            y: Panel Y position  
            width: Panel width
            height: Panel height
        """
        self.x = x
        self.y = y
        self.width = width
        self.height = height
        
        self.logger = get_logger(__name__)
        self.ui_config = get_ui_config()

        # Colors - using centralized color scheme
        self.colors = get_colors()

        # Fonts - using centralized font configuration
        self.font = self.ui_config.get_font(FONT_NORMAL)
        self.font_small = self.ui_config.get_font(FONT_SMALL)

        # Layout - using centralized spacing
        self.padding = self.ui_config.get_spacing(SPACING_NORMAL)
        self.small_padding = self.ui_config.get_spacing(SPACING_SMALL)
        
        # Panel state
        self.is_visible = False
        self.selection_info: Dict[str, Any] = {}
        self.legend: Dict[str, Dict[str, Any]] = {}
        self.transition_data: Dict[Tuple[int, int], int] = {}
        self.level_config: Dict[str, Any] = {}
        
        # Delete functionality
        self.delete_callback = None
        self.delete_button_rects: List[Tuple[pygame.Rect, int]] = []  # (rect, entity_index)
    
    def set_legend(self, legend: Dict[str, Dict[str, Any]]) -> None:
        """Set the legend for looking up asset information."""
        self.legend = legend
    
    def set_transition_data(self, transition_data: Dict[Tuple[int, int], int]) -> None:
        """Set the transition metadata for looking up portal information."""
        self.transition_data = transition_data
    
    def set_level_config(self, level_config: Dict[str, Any]) -> None:
        """Set the level configuration for transition information."""
        self.level_config = level_config
    
    def set_delete_callback(self, callback) -> None:
        """Set the callback for deleting entities."""
        self.delete_callback = callback
    
    def handle_mouse_click(self, pos: Tuple[int, int], button: int) -> bool:
        """
        Handle mouse click events.
        
        Args:
            pos: Mouse position
            button: Mouse button (1=left, 3=right)
            
        Returns:
            True if the click was handled
        """
        if not self.is_visible or button != 1:  # Only handle left clicks
            return False
        
        # Check if any delete button was clicked
        for button_rect, entity_index in self.delete_button_rects:
            if button_rect.collidepoint(pos):
                self._delete_entity(entity_index)
                return True
        
        return False
    
    def _delete_entity(self, entity_index: int) -> None:
        """Delete an entity at the given index."""
        if not self.delete_callback:
            return
        
        entities = self.selection_info.get('entities', [])
        if 0 <= entity_index < len(entities):
            entity = entities[entity_index]
            position = self.selection_info.get('position')
            
            # Check if this is a transition entity
            entity_symbol = self._find_entity_symbol(entity)
            is_transition = False
            
            # Check if this position has a transition tile
            if position and position in self.transition_data:
                transition_number = self.transition_data[position]
                is_transition = True
                self.logger.info(f"Deleting transition entity at position {position} with transition number {transition_number}")
            
            # Call the delete callback
            self.delete_callback(entity_index, position, is_transition)
    
    def update_selection(self, selection_info: Dict[str, Any]) -> None:
        """
        Update the panel with new selection information.
        
        Args:
            selection_info: Dictionary containing position, tile_asset, and entities
        """
        self.selection_info = selection_info
        self.is_visible = bool(selection_info and selection_info.get('position'))
    
    def hide(self) -> None:
        """Hide the info panel."""
        self.is_visible = False
        self.selection_info = {}
    
    def render(self, screen: pygame.Surface) -> None:
        """
        Render the info panel.
        
        Args:
            screen: Surface to render to
        """
        if not self.is_visible or not self.selection_info:
            return
        
        # Draw panel background
        panel_rect = pygame.Rect(self.x, self.y, self.width, self.height)
        pygame.draw.rect(screen, self.colors['background_panel'], panel_rect)
        pygame.draw.rect(screen, self.colors['border_primary'], panel_rect, 2)
        
        # Draw panel title
        title_text = "Tile Information"
        title_surface = self.font.render(title_text, True, self.colors['text_primary'])
        title_y = self.y + self.padding
        screen.blit(title_surface, (self.x + self.padding, title_y))
        
        # Draw separator line
        separator_y = title_y + title_surface.get_height() + self.small_padding
        pygame.draw.line(screen, self.colors['border_secondary'], 
                        (self.x + self.padding, separator_y), 
                        (self.x + self.width - self.padding, separator_y))
        
        # Current Y position for content
        current_y = separator_y + self.small_padding
        
        # Draw position information
        position = self.selection_info.get('position', (0, 0))
        pos_text = f"Position: ({position[0]}, {position[1]})"
        pos_surface = self.font_small.render(pos_text, True, self.colors['text_secondary'])
        screen.blit(pos_surface, (self.x + self.padding, current_y))
        current_y += pos_surface.get_height() + self.small_padding
        
        # Draw tile information
        tile_asset = self.selection_info.get('tile_asset')
        if tile_asset:
            current_y = self._render_tile_info(screen, tile_asset, current_y)
        else:
            no_tile_text = "No tile data"
            no_tile_surface = self.font_small.render(no_tile_text, True, self.colors['text_disabled'])
            screen.blit(no_tile_surface, (self.x + self.padding, current_y))
            current_y += no_tile_surface.get_height() + self.small_padding
        
        # Draw entity information
        entities = self.selection_info.get('entities', [])
        if entities:
            current_y = self._render_entities_info(screen, entities, current_y)
        
        # Draw transition information if this is a portal tile
        if tile_asset == "tile.exit.portal":
            current_y = self._render_transition_info(screen, current_y)
    
        # Add some spacing at bottom if there's room
        if current_y < self.y + self.height - self.padding:
            # Draw grid coordinates for reference
            grid_text = f"Grid: {position[0]}, {position[1]}"
            grid_surface = self.font_small.render(grid_text, True, self.colors['text_muted'])
            bottom_y = self.y + self.height - self.padding - grid_surface.get_height()
            if bottom_y > current_y:
                screen.blit(grid_surface, (self.x + self.padding, bottom_y))
    
    def _render_tile_info(self, screen: pygame.Surface, tile_asset: str, start_y: int) -> int:
        """
        Render tile information section.
        
        Args:
            screen: Surface to render to
            tile_asset: The tile asset symbol
            start_y: Starting Y position
            
        Returns:
            New Y position after rendering
        """
        current_y = start_y
        
        # Section header
        header_text = "Tile:"
        header_surface = self.font.render(header_text, True, self.colors['text_primary'])
        screen.blit(header_surface, (self.x + self.padding, current_y))
        current_y += header_surface.get_height() + self.small_padding
        
        # Asset symbol and basic info
        symbol_text = f"Symbol: '{tile_asset}'"
        symbol_surface = self.font_small.render(symbol_text, True, self.colors['text_secondary'])
        screen.blit(symbol_surface, (self.x + self.padding * 2, current_y))
        current_y += symbol_surface.get_height() + self.small_padding
        
        # Look up asset in legend
        asset_info = self.legend.get(tile_asset, {})
        if asset_info:
            # Asset category
            category = asset_info.get('category', 'unknown')
            category_text = f"Category: {category}"
            category_surface = self.font_small.render(category_text, True, self.colors['text_secondary'])
            screen.blit(category_surface, (self.x + self.padding * 2, current_y))
            current_y += category_surface.get_height() + self.small_padding
            
            # Asset type
            asset_type = asset_info.get('type', 'unknown')
            type_text = f"Type: {asset_type}"
            type_surface = self.font_small.render(type_text, True, self.colors['text_secondary'])
            screen.blit(type_surface, (self.x + self.padding * 2, current_y))
            current_y += type_surface.get_height() + self.small_padding
            
            # Asset ID
            data_id = asset_info.get('data_id', 'unknown')
            id_text = f"ID: {data_id}"
            id_surface = self.font_small.render(id_text, True, self.colors['text_secondary'])
            screen.blit(id_surface, (self.x + self.padding * 2, current_y))
            current_y += id_surface.get_height() + self.small_padding
            
            # Additional properties
            properties = asset_info.get('properties', {})
            if properties:
                props_text = "Properties:"
                props_surface = self.font_small.render(props_text, True, self.colors['text_secondary'])
                screen.blit(props_surface, (self.x + self.padding * 2, current_y))
                current_y += props_surface.get_height() + self.small_padding
                
                for key, value in properties.items():
                    prop_text = f"  {key}: {value}"
                    prop_surface = self.font_small.render(prop_text, True, self.colors['text_muted'])
                    screen.blit(prop_surface, (self.x + self.padding * 3, current_y))
                    current_y += prop_surface.get_height() + self.small_padding
        else:
            # Unknown asset
            unknown_text = "Unknown asset"
            unknown_surface = self.font_small.render(unknown_text, True, self.colors['text_disabled'])
            screen.blit(unknown_surface, (self.x + self.padding * 2, current_y))
            current_y += unknown_surface.get_height() + self.small_padding
        
        return current_y + self.small_padding
    
    def _render_entities_info(self, screen: pygame.Surface, entities: List[Dict[str, Any]], start_y: int) -> int:
        """
        Render entities information section.
        
        Args:
            screen: Surface to render to
            entities: List of entity dictionaries
            start_y: Starting Y position
            
        Returns:
            New Y position after rendering
        """
        current_y = start_y
        
        # Clear previous delete button rects
        self.delete_button_rects = []
        
        # Section header
        count = len(entities)
        header_text = f"Entities ({count}):"
        header_surface = self.font.render(header_text, True, self.colors['text_primary'])
        screen.blit(header_surface, (self.x + self.padding, current_y))
        current_y += header_surface.get_height() + self.small_padding
        
        for i, entity in enumerate(entities):
            # Entity index and delete button
            entity_header = f"Entity {i + 1}:"
            entity_surface = self.font_small.render(entity_header, True, self.colors['text_accent'])
            screen.blit(entity_surface, (self.x + self.padding * 2, current_y))
            
            # Add delete button next to entity header
            delete_button_width = 60
            delete_button_height = 20
            delete_button_x = self.x + self.width - self.padding - delete_button_width
            delete_button_y = current_y
            delete_button_rect = pygame.Rect(delete_button_x, delete_button_y, delete_button_width, delete_button_height)
            
            # Draw delete button
            pygame.draw.rect(screen, self.colors['button_danger'], delete_button_rect)
            pygame.draw.rect(screen, self.colors['border_primary'], delete_button_rect, 1)
            
            # Delete button text
            delete_text = self.font_small.render("Delete", True, self.colors['text_primary'])
            text_rect = delete_text.get_rect(center=delete_button_rect.center)
            screen.blit(delete_text, text_rect)
            
            # Store button rect for click detection
            self.delete_button_rects.append((delete_button_rect, i))
            
            current_y += entity_surface.get_height() + self.small_padding
            
            # Find entity symbol by looking up data_id and type in legend
            entity_symbol = self._find_entity_symbol(entity)
            char_text = f"Symbol: '{entity_symbol}'"
            char_surface = self.font_small.render(char_text, True, self.colors['text_secondary'])
            screen.blit(char_surface, (self.x + self.padding * 3, current_y))
            current_y += char_surface.get_height() + self.small_padding
            
            # Look up entity in legend using the found symbol
            entity_info = self.legend.get(entity_symbol, {}) if entity_symbol else {}
            if entity_info:
                # Entity category and type
                category = entity_info.get('category', 'unknown')
                entity_type = entity_info.get('type', 'unknown')
                type_text = f"Type: {category}/{entity_type}"
                type_surface = self.font_small.render(type_text, True, self.colors['text_secondary'])
                screen.blit(type_surface, (self.x + self.padding * 3, current_y))
                current_y += type_surface.get_height() + self.small_padding
                
                # Entity ID and enhanced NPC information
                data_id = entity_info.get('data_id', 'unknown')
                id_text = f"ID: {data_id}"
                id_surface = self.font_small.render(id_text, True, self.colors['text_secondary'])
                screen.blit(id_surface, (self.x + self.padding * 3, current_y))
                current_y += id_surface.get_height() + self.small_padding
                
                # Enhanced NPC information
                if entity_type == 'npc' and data_id != 'unknown':
                    current_y = self._render_npc_details(screen, data_id, current_y)
            else:
                # Unknown entity - show what we know from the entity data
                entity_type = entity.get('type', 'unknown')
                entity_data_id = entity.get('data_id', 'unknown')
                type_text = f"Type: {entity_type}"
                type_surface = self.font_small.render(type_text, True, self.colors['text_secondary'])
                screen.blit(type_surface, (self.x + self.padding * 3, current_y))
                current_y += type_surface.get_height() + self.small_padding
                
                id_text = f"Data ID: {entity_data_id}"
                id_surface = self.font_small.render(id_text, True, self.colors['text_secondary'])
                screen.blit(id_surface, (self.x + self.padding * 3, current_y))
                current_y += id_surface.get_height() + self.small_padding
                
                # Enhanced NPC information even for unknown entities
                if entity_type == 'npc' and entity_data_id != 'unknown':
                    current_y = self._render_npc_details(screen, entity_data_id, current_y)
            
            # Entity overrides/properties
            overrides = entity.get('overrides', {})
            if overrides:
                override_text = "Overrides:"
                override_surface = self.font_small.render(override_text, True, self.colors['text_secondary'])
                screen.blit(override_surface, (self.x + self.padding * 3, current_y))
                current_y += override_surface.get_height() + self.small_padding
                
                for key, value in overrides.items():
                    override_prop_text = f"  {key}: {value}"
                    override_prop_surface = self.font_small.render(override_prop_text, True, self.colors['text_muted'])
                    screen.blit(override_prop_surface, (self.x + self.padding * 4, current_y))
                    current_y += override_prop_surface.get_height() + self.small_padding
            
            # Add spacing between entities
            if i < len(entities) - 1:
                current_y += self.small_padding
        
        return current_y
    
    def _find_entity_symbol(self, entity: Dict[str, Any]) -> Optional[str]:
        """
        Find the symbol for an entity by looking up its data_id and type in the legend.
        
        Args:
            entity: Entity dictionary with type and data_id
            
        Returns:
            Symbol character for the entity, or None if not found
        """
        entity_type = entity.get('type')
        entity_data_id = entity.get('data_id')
        
        if not entity_type or not entity_data_id:
            return None
        
        # Search through legend to find matching symbol
        for symbol, definition in self.legend.items():
            if (definition.get('category') == 'entity' and
                definition.get('type') == entity_type and
                definition.get('data_id') == entity_data_id):
                return symbol
        
        return None
    
    def _render_npc_details(self, screen: pygame.Surface, data_id: str, start_y: int) -> int:
        """
        Render enhanced NPC information using the NPC data system.
        
        Args:
            screen: Surface to render to
            data_id: The NPC data ID from the legend
            start_y: Starting Y position
            
        Returns:
            New Y position after rendering
        """
        current_y = start_y
        
        # Look up the NPC type from the data_id
        npc_type = get_npc_data_id_mapping(data_id)
        if not npc_type:
            # No mapping found
            no_mapping_text = f"Unknown NPC type for ID: {data_id}"
            no_mapping_surface = self.font_small.render(no_mapping_text, True, self.colors['text_disabled'])
            screen.blit(no_mapping_surface, (self.x + self.padding * 3, current_y))
            return current_y + no_mapping_surface.get_height() + self.small_padding
        
        # Get the NPC definition
        npc_definition = get_npc_definition(npc_type)
        if not npc_definition:
            # No definition found
            no_def_text = f"No definition for NPC type: {npc_type}"
            no_def_surface = self.font_small.render(no_def_text, True, self.colors['text_disabled'])
            screen.blit(no_def_surface, (self.x + self.padding * 3, current_y))
            return current_y + no_def_surface.get_height() + self.small_padding
        
        # Render NPC details header
        npc_header_text = "NPC Details:"
        npc_header_surface = self.font_small.render(npc_header_text, True, self.colors['text_accent'])
        screen.blit(npc_header_surface, (self.x + self.padding * 3, current_y))
        current_y += npc_header_surface.get_height() + self.small_padding
        
        # NPC Name
        name_text = f"Name: {npc_definition.name}"
        name_surface = self.font_small.render(name_text, True, self.colors['text_secondary'])
        screen.blit(name_surface, (self.x + self.padding * 4, current_y))
        current_y += name_surface.get_height() + self.small_padding
        
        # NPC Type
        type_text = f"NPC Type: {npc_definition.npc_type}"
        type_surface = self.font_small.render(type_text, True, self.colors['text_secondary'])
        screen.blit(type_surface, (self.x + self.padding * 4, current_y))
        current_y += type_surface.get_height() + self.small_padding
        
        # Behavior
        behavior_text = f"Behavior: {npc_definition.behavior}"
        behavior_surface = self.font_small.render(behavior_text, True, self.colors['text_secondary'])
        screen.blit(behavior_surface, (self.x + self.padding * 4, current_y))
        current_y += behavior_surface.get_height() + self.small_padding
        
        # Default inventory (for store NPCs)
        if npc_definition.default_inventory:
            inventory_header_text = "Default Inventory:"
            inventory_header_surface = self.font_small.render(inventory_header_text, True, self.colors['text_secondary'])
            screen.blit(inventory_header_surface, (self.x + self.padding * 4, current_y))
            current_y += inventory_header_surface.get_height() + self.small_padding
            
            # Show first few items (to avoid overflow)
            max_items = 3
            items_shown = 0
            for item in npc_definition.default_inventory:
                if items_shown >= max_items:
                    more_text = f"... and {len(npc_definition.default_inventory) - max_items} more"
                    more_surface = self.font_small.render(more_text, True, self.colors['text_muted'])
                    screen.blit(more_surface, (self.x + self.padding * 5, current_y))
                    current_y += more_surface.get_height() + self.small_padding
                    break
                
                item_text = f"• {item}"
                item_surface = self.font_small.render(item_text, True, self.colors['text_muted'])
                screen.blit(item_surface, (self.x + self.padding * 5, current_y))
                current_y += item_surface.get_height() + self.small_padding
                items_shown += 1
        
        # Description
        if npc_definition.description:
            desc_text = f"Description: {npc_definition.description}"
            # Handle long descriptions by wrapping
            max_width = self.width - (self.padding * 5)
            if len(desc_text) * 6 > max_width:  # Rough estimate of text width
                desc_text = desc_text[:max_width // 6 - 3] + "..."
            
            desc_surface = self.font_small.render(desc_text, True, self.colors['text_muted'])
            screen.blit(desc_surface, (self.x + self.padding * 4, current_y))
            current_y += desc_surface.get_height() + self.small_padding
        
        return current_y + self.small_padding
    
    def _render_transition_info(self, screen: pygame.Surface, start_y: int) -> int:
        """
        Render transition/portal information.
        
        Args:
            screen: Surface to render to
            start_y: Starting Y position
            
        Returns:
            New Y position after rendering
        """
        current_y = start_y
        
        # Section header
        header_text = "Transition Portal:"
        header_surface = self.font.render(header_text, True, self.colors['text_primary'])
        screen.blit(header_surface, (self.x + self.padding, current_y))
        current_y += header_surface.get_height() + self.small_padding
        
        # Get position for transition lookup
        position = self.selection_info.get('position', (0, 0))
        
        # Look up exit number from transition metadata
        exit_number = self.transition_data.get(position)
        if exit_number is not None:
            exit_text = f"Exit Number: {exit_number}"
            exit_surface = self.font_small.render(exit_text, True, self.colors['text_secondary'])
            screen.blit(exit_surface, (self.x + self.padding * 2, current_y))
            current_y += exit_surface.get_height() + self.small_padding
            
            # Look up destination in level config
            exits = self.level_config.get('exits', {})
            exit_config = exits.get(str(exit_number), {})
            
            if exit_config:
                # Target map
                target_map = exit_config.get('target_map', 'Not configured')
                target_text = f"Target Map: {target_map}"
                target_surface = self.font_small.render(target_text, True, self.colors['text_secondary'])
                screen.blit(target_surface, (self.x + self.padding * 2, current_y))
                current_y += target_surface.get_height() + self.small_padding
                
                # Spawn point
                spawn_point = exit_config.get('spawn_point', 'Not configured')
                spawn_text = f"Spawn Point: {spawn_point}"
                spawn_surface = self.font_small.render(spawn_text, True, self.colors['text_secondary'])
                screen.blit(spawn_surface, (self.x + self.padding * 2, current_y))
                current_y += spawn_surface.get_height() + self.small_padding
            else:
                # No configuration found
                no_config_text = "Destination not configured"
                no_config_surface = self.font_small.render(no_config_text, True, self.colors['text_disabled'])
                screen.blit(no_config_surface, (self.x + self.padding * 2, current_y))
                current_y += no_config_surface.get_height() + self.small_padding
        else:
            # No exit number found
            no_exit_text = "Exit number not found"
            no_exit_surface = self.font_small.render(no_exit_text, True, self.colors['text_disabled'])
            screen.blit(no_exit_surface, (self.x + self.padding * 2, current_y))
            current_y += no_exit_surface.get_height() + self.small_padding
        
        return current_y + self.small_padding
