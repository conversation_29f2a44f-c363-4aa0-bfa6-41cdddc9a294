"""
Toolbar

This module provides a toolbar with tool selection buttons.
"""

import pygame
from typing import Dict, List, Tuple, Optional, Callable
from enum import Enum

from ..tools import ToolType
from src.infrastructure.logging import get_logger
from ..ui_config import get_ui_config, get_colors, FONT_NORMAL, FONT_SMALL, ELEMENT_TOOLBAR_BUTTON, SPACING_SMALL


class ToolbarButton:
    """A button in the toolbar."""
    
    def __init__(self, tool_type: ToolType, name: str, shortcut: str, x: int, y: int, size: int):
        """Initialize the button."""
        self.tool_type = tool_type
        self.name = name
        self.shortcut = shortcut
        self.rect = pygame.Rect(x, y, size, size)
        self.is_active = False
        self.is_hovered = False


class Toolbar:
    """
    Toolbar for tool selection.
    
    This provides buttons for switching between different editing tools.
    """
    
    def __init__(self, x: int, y: int, width: int, height: int):
        """
        Initialize the toolbar.
        
        Args:
            x: Toolbar X position
            y: Toolbar Y position
            width: Toolbar width
            height: Toolbar height
        """
        self.x = x
        self.y = y
        self.width = width
        self.height = height
        
        self.logger = get_logger(__name__)
        self.ui_config = get_ui_config()

        # Colors - using centralized color scheme
        self.colors = get_colors()

        # Fonts - using centralized font configuration
        self.font = self.ui_config.get_font(FONT_NORMAL)
        self.font_small = self.ui_config.get_font(FONT_SMALL)

        # Button layout - using centralized sizing
        self.button_size = self.ui_config.get_element_size(ELEMENT_TOOLBAR_BUTTON)
        self.button_padding = self.ui_config.get_spacing(SPACING_SMALL)
        
        # Create buttons
        self.buttons: List[ToolbarButton] = []
        self._create_buttons()
        
        # Callback for tool changes
        self.tool_change_callback: Optional[Callable[[ToolType], None]] = None
        
        # Current active tool
        self.active_tool: Optional[ToolType] = ToolType.SELECTION
        self._update_active_button()
    
    def set_tool_change_callback(self, callback: Callable[[ToolType], None]) -> None:
        """Set the callback for tool changes."""
        self.tool_change_callback = callback
    
    def set_active_tool(self, tool_type: ToolType) -> None:
        """Set the active tool."""
        self.active_tool = tool_type
        self._update_active_button()
    
    def update(self, mouse_pos: Tuple[int, int], mouse_clicked: bool, 
               events: List[pygame.event.Event]) -> None:
        """
        Update the toolbar.
        
        Args:
            mouse_pos: Current mouse position
            mouse_clicked: Whether mouse was clicked this frame
            events: Pygame events for this frame
        """
        # Update hover states
        for button in self.buttons:
            button.is_hovered = button.rect.collidepoint(mouse_pos)
        
        # Handle events
        for event in events:
            if event.type == pygame.MOUSEBUTTONDOWN:
                if event.button == 1:  # Left click
                    self._handle_click(event.pos)
            elif event.type == pygame.KEYDOWN:
                self._handle_shortcut(event.key)
    
    def render(self, screen: pygame.Surface) -> None:
        """
        Render the toolbar.
        
        Args:
            screen: Surface to render to
        """
        # Draw background
        toolbar_rect = pygame.Rect(self.x, self.y, self.width, self.height)
        pygame.draw.rect(screen, self.colors['background_medium'], toolbar_rect)
        pygame.draw.rect(screen, self.colors['border'], toolbar_rect, 1)
        
        # Draw buttons
        for button in self.buttons:
            self._render_button(screen, button)
    
    def _create_buttons(self) -> None:
        """Create toolbar buttons."""
        tools = [
            (ToolType.SELECTION, "Select", "S"),
            (ToolType.PAINT, "Paint", "P"),
            (ToolType.TRANSITION, "Transition", "T"),
        ]
        
        current_x = self.x + self.button_padding
        current_y = self.y + self.button_padding
        
        for tool_type, name, shortcut in tools:
            button = ToolbarButton(
                tool_type, name, shortcut,
                current_x, current_y, self.button_size
            )
            self.buttons.append(button)
            
            current_x += self.button_size + self.button_padding
            
            # Wrap to next row if needed
            if current_x + self.button_size > self.x + self.width:
                current_x = self.x + self.button_padding
                current_y += self.button_size + self.button_padding
    
    def _update_active_button(self) -> None:
        """Update which button is active."""
        for button in self.buttons:
            button.is_active = (button.tool_type == self.active_tool)
    
    def _handle_click(self, pos: Tuple[int, int]) -> None:
        """Handle mouse click."""
        for button in self.buttons:
            if button.rect.collidepoint(pos):
                self._select_tool(button.tool_type)
                break
    
    def _handle_shortcut(self, key: int) -> None:
        """Handle keyboard shortcuts."""
        # Map keys to tools
        key_map = {
            pygame.K_s: ToolType.SELECTION,
            pygame.K_p: ToolType.PAINT,
        }
        
        if key in key_map:
            self._select_tool(key_map[key])
    
    def _select_tool(self, tool_type: ToolType) -> None:
        """Select a tool."""
        if tool_type != self.active_tool:
            self.active_tool = tool_type
            self._update_active_button()
            
            if self.tool_change_callback:
                self.tool_change_callback(tool_type)
            
            self.logger.info(f"Selected tool: {tool_type.value}")
    
    def _render_button(self, screen: pygame.Surface, button: ToolbarButton) -> None:
        """Render a single button."""
        # Button background
        if button.is_active:
            color = self.colors['button_active']
        elif button.is_hovered:
            color = self.colors['button_hover']
        else:
            color = self.colors['button_normal']
        
        pygame.draw.rect(screen, color, button.rect)
        pygame.draw.rect(screen, self.colors['border'], button.rect, 1)
        
        # Button icon/text (simplified - just show first letter)
        icon_text = button.name[0]
        text_color = self.colors['text_primary'] if button.is_active else self.colors['text_primary']
        text = self.font.render(icon_text, True, text_color)
        text_rect = text.get_rect(center=(button.rect.centerx, button.rect.centery - 5))
        screen.blit(text, text_rect)

        # Shortcut key
        shortcut_text = self.font_small.render(button.shortcut, True, self.colors['text_secondary'])
        shortcut_rect = shortcut_text.get_rect(center=(button.rect.centerx, button.rect.bottom - 8))
        screen.blit(shortcut_text, shortcut_rect)
        
        # Tooltip on hover
        if button.is_hovered:
            self._render_tooltip(screen, button)

    def _render_tooltip(self, screen: pygame.Surface, button: ToolbarButton) -> None:
        """Render tooltip for a button."""
        tooltip_text = f"{button.name} ({button.shortcut})"
        text = self.font_small.render(tooltip_text, True, self.colors['text_primary'])

        # Position tooltip above button
        tooltip_x = button.rect.centerx - text.get_width() // 2
        tooltip_y = button.rect.top - text.get_height() - 5

        # Background
        bg_rect = pygame.Rect(tooltip_x - 3, tooltip_y - 2,
                             text.get_width() + 6, text.get_height() + 4)
        pygame.draw.rect(screen, self.colors['background_dark'], bg_rect)
        pygame.draw.rect(screen, self.colors['border'], bg_rect, 1)

        # Text
        screen.blit(text, (tooltip_x, tooltip_y))
