"""
Status Bar

This module provides a status bar showing current tool, coordinates, and other info.
"""

import pygame
from typing import Optional, Dict, Any

from src.infrastructure.logging import get_logger
from ..ui_config import get_ui_config, get_colors, FONT_NORMAL, ELEMENT_STATUS_HEIGHT, SPACING_NORMAL


class StatusBar:
    """
    Status bar for the map editor.
    
    This displays current tool, mouse coordinates, map info, and other status information.
    """
    
    def __init__(self, x: int, y: int, width: int, height: int):
        """
        Initialize the status bar.
        
        Args:
            x: Status bar X position
            y: Status bar Y position
            width: Status bar width
            height: Status bar height
        """
        self.x = x
        self.y = y
        self.width = width
        self.height = height
        
        self.logger = get_logger(__name__)
        self.ui_config = get_ui_config()

        # Status information
        self.tool_status = "Tool: Selection"
        self.coordinate_status = "Position: (0, 0)"
        self.map_status = "No map loaded"
        self.zoom_status = "Zoom: 1.0x"

        # Colors - using centralized color scheme
        self.colors = get_colors()

        # Fonts - using centralized font configuration
        self.font = self.ui_config.get_font(FONT_NORMAL)

        # Layout - using centralized spacing
        self.padding = self.ui_config.get_spacing(SPACING_NORMAL)
        self.separator_width = 1
    
    def update_tool_status(self, status: str) -> None:
        """Update the tool status text."""
        self.tool_status = status
    
    def update_coordinate_status(self, tile_pos: Optional[tuple], world_pos: Optional[tuple]) -> None:
        """Update the coordinate status text."""
        if tile_pos:
            self.coordinate_status = f"Tile: ({tile_pos[0]}, {tile_pos[1]})"
            if world_pos:
                self.coordinate_status += f" | World: ({world_pos[0]:.1f}, {world_pos[1]:.1f})"
        else:
            self.coordinate_status = "Position: --"
    
    def update_map_status(self, map_name: str, width: int, height: int, modified: bool = False) -> None:
        """Update the map status text."""
        modified_indicator = "*" if modified else ""
        self.map_status = f"Map: {map_name}{modified_indicator} ({width}x{height})"
    
    def update_zoom_status(self, zoom_level: float) -> None:
        """Update the zoom status text."""
        self.zoom_status = f"Zoom: {zoom_level:.2f}x"
    
    def render(self, screen: pygame.Surface) -> None:
        """
        Render the status bar.
        
        Args:
            screen: Surface to render to
        """
        # Draw background
        status_rect = pygame.Rect(self.x, self.y, self.width, self.height)
        pygame.draw.rect(screen, self.colors['background_dark'], status_rect)
        pygame.draw.rect(screen, self.colors['border'], status_rect, 1)
        
        # Prepare status sections
        sections = [
            self.tool_status,
            self.coordinate_status,
            self.map_status,
            self.zoom_status
        ]
        
        # Calculate section widths
        section_widths = []
        total_text_width = 0
        for section in sections:
            text_width = self.font.size(section)[0]
            section_widths.append(text_width)
            total_text_width += text_width
        
        # Calculate spacing
        available_width = self.width - (self.padding * 2) - (len(sections) - 1) * self.separator_width * 3
        extra_space = max(0, available_width - total_text_width)
        section_spacing = extra_space // len(sections) if len(sections) > 0 else 0
        
        # Render sections
        current_x = self.x + self.padding
        for i, (section, text_width) in enumerate(zip(sections, section_widths)):
            # Render text
            text = self.font.render(section, True, self.colors['text_primary'])
            text_rect = text.get_rect(midleft=(current_x, self.y + self.height // 2))
            screen.blit(text, text_rect)

            current_x += text_width + section_spacing

            # Draw separator (except after last section)
            if i < len(sections) - 1:
                sep_x = current_x + self.separator_width
                pygame.draw.line(screen, self.colors['border'],
                               (sep_x, self.y + 5), (sep_x, self.y + self.height - 5))
                current_x += self.separator_width * 3
    
    def get_help_text(self) -> str:
        """Get help text for keyboard shortcuts."""
        return "Shortcuts: S=Select, P=Paint, G=Grid, Home=Reset View, Esc=Exit"
