"""
Paint Tool

This module implements the paint brush tool for placing tiles and entities.
"""

import pygame
from typing import Optional, Tu<PERSON>, Dict, Any

from .base_tool import BaseTool, ToolType
from ..map_operations import EditorMapData


class PaintTool(BaseTool):
    """
    Paint brush tool for placing tiles and entities.
    
    This tool allows users to paint tiles by clicking or dragging.
    """
    
    def __init__(self):
        """Initialize the paint tool."""
        super().__init__(ToolType.PAINT, "Paint Brush")
        
        # Current asset to paint with
        self.current_asset_id: Optional[str] = None
        self.current_symbol: Optional[str] = None
        self.current_definition: Optional[Dict[str, Any]] = None
        
        # Paint settings
        self.paint_on_drag = True
        
        # Visual feedback
        self.preview_color = (255, 255, 0, 128)  # Yellow with transparency
    
    def set_current_asset(self, symbol: str, definition: Dict[str, Any]) -> None:
        """
        Set the current asset to paint with.

        Args:
            symbol: The symbol character
            definition: Asset definition from base legend
        """
        self.current_symbol = symbol
        self.current_definition = definition

        # Get asset_id, resolving it for monsters if needed
        asset_id = definition.get('asset_id')

        # If no asset_id and this is a monster, try to get it from monster definition
        if not asset_id and definition.get('category') == 'entity' and definition.get('type') == 'monster':
            data_id = definition.get('data_id')
            if data_id:
                try:
                    from src.game_data.monsters import get_monster_definition
                    monster_def = get_monster_definition(data_id)
                    if monster_def:
                        asset_id = monster_def.asset_id
                except Exception:
                    pass

        self.current_asset_id = asset_id
    
    def activate(self) -> None:
        """Called when the tool becomes active."""
        super().activate()
    
    def deactivate(self) -> None:
        """Called when the tool becomes inactive."""
        super().deactivate()
    
    def handle_mouse_down(self, pos: Tuple[int, int], button: int, 
                         map_data: Optional[EditorMapData]) -> bool:
        """Handle mouse button down event."""
        if not map_data or not self.current_asset_id:
            return False
        
        if button == 1:  # Left click - paint
            self.is_dragging = True
            self.drag_start_pos = pos
            self.last_pos = pos
            return self._paint_at_position(pos, map_data)
        elif button == 3:  # Right click - erase (place default floor)
            self.is_dragging = True
            self.drag_start_pos = pos
            self.last_pos = pos
            return self._erase_at_position(pos, map_data)
        
        return False
    
    def handle_mouse_up(self, pos: Tuple[int, int], button: int,
                       map_data: Optional[EditorMapData]) -> bool:
        """Handle mouse button up event."""
        if button in [1, 3]:  # Left or right click
            self.is_dragging = False
            self.drag_start_pos = None
            self.last_pos = None
        
        return False
    
    def handle_mouse_move(self, pos: Tuple[int, int], 
                         map_data: Optional[EditorMapData]) -> bool:
        """Handle mouse move event."""
        if not map_data or not self.is_dragging or not self.paint_on_drag:
            return False
        
        # Only paint if we've moved to a different tile
        if self.last_pos and pos != self.last_pos:
            self.last_pos = pos
            
            # Check which mouse button is being held
            mouse_buttons = pygame.mouse.get_pressed()
            if mouse_buttons[0]:  # Left button - paint
                return self._paint_at_position(pos, map_data)
            elif mouse_buttons[2]:  # Right button - erase
                return self._erase_at_position(pos, map_data)
        
        return False
    
    def handle_key_down(self, key: int, map_data: Optional[EditorMapData]) -> bool:
        """Handle key down event."""
        # No special key handling for paint tool
        return False
    
    def render_overlay(self, screen: pygame.Surface, canvas_rect: pygame.Rect,
                      world_to_screen_func, tile_size: int) -> None:
        """Render tool-specific overlay on the canvas."""
        if not self.is_active:
            return
        
        # Show preview of tile at cursor position
        if self.current_asset_id:
            # Convert cursor tile position to world coordinates
            # tile_size is now the base tile size, world_to_screen_func handles zoom scaling
            cursor_world_x = self.cursor_pos[0] * tile_size
            cursor_world_y = self.cursor_pos[1] * tile_size
            screen_x, screen_y = world_to_screen_func(cursor_world_x, cursor_world_y)
            
            # Calculate the display size by checking how much a tile spans on screen
            test_x, test_y = world_to_screen_func(tile_size, tile_size)
            origin_x, origin_y = world_to_screen_func(0, 0)
            display_size = int(test_x - origin_x)  # This gives us the zoomed tile size
            
            # Draw preview rectangle
            preview_rect = pygame.Rect(screen_x, screen_y, display_size, display_size)
            if canvas_rect.colliderect(preview_rect):
                # Create a surface with alpha for the preview
                preview_surface = pygame.Surface((display_size, display_size), pygame.SRCALPHA)
                preview_surface.fill(self.preview_color)
                screen.blit(preview_surface, (screen_x, screen_y))
    
    def get_cursor_type(self) -> int:
        """Get the pygame cursor type for this tool."""
        return pygame.SYSTEM_CURSOR_CROSSHAIR
    
    def _paint_at_position(self, pos: Tuple[int, int], map_data: EditorMapData) -> bool:
        """
        Paint the current asset at the given position.
        
        Args:
            pos: Tile position to paint at
            map_data: Map data to modify
            
        Returns:
            True if painting was successful
        """
        if not self.current_asset_id:
            return False
        
        # Check if we're painting a tile or entity
        if self.current_definition and self.current_definition.get('category') == 'tile':
            # Paint tile
            return self.set_tile_at_pos(pos, self.current_asset_id, map_data)
        elif self.current_definition and self.current_definition.get('category') == 'entity':
            # Place entity (this is more complex as entities can overlap)
            return self._place_entity_at_position(pos, map_data)
        
        return False
    
    def _erase_at_position(self, pos: Tuple[int, int], map_data: EditorMapData) -> bool:
        """
        Erase (place default floor) at the given position.
        
        Args:
            pos: Tile position to erase at
            map_data: Map data to modify
            
        Returns:
            True if erasing was successful
        """
        # Place default floor tile
        default_floor = "tile.floor.dirt"
        return self.set_tile_at_pos(pos, default_floor, map_data)
    
    def _place_entity_at_position(self, pos: Tuple[int, int], map_data: EditorMapData) -> bool:
        """
        Place an entity at the given position.

        Args:
            pos: Tile position to place entity at
            map_data: Map data to modify

        Returns:
            True if entity placement was successful
        """
        if not self.current_definition:
            return False

        x, y = pos

        # Remove any existing entity at this position
        map_data.layout.entities = [
            entity for entity in map_data.layout.entities
            if not (entity.get('x') == x and entity.get('y') == y)
        ]

        # Create new entity
        entity_data = {
            'type': self.current_definition.get('type', 'unknown'),
            'data_id': self.current_definition.get('data_id', ''),
            'x': x,
            'y': y,
        }

        # Only add asset_id if we have one (for entities with explicit asset_id in base_legend)
        if self.current_asset_id:
            entity_data['asset_id'] = self.current_asset_id

        # Add any additional properties from the definition
        if 'properties' in self.current_definition:
            entity_data['properties'] = self.current_definition['properties'].copy()

        map_data.layout.entities.append(entity_data)
        map_data.modified = True

        return True
