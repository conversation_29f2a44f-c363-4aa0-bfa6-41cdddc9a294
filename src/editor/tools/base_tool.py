"""
Base Tool Class

This module provides the abstract base class for all editing tools.
"""

from abc import ABC, abstractmethod
from enum import Enum
from typing import Optional, Tuple, List, Dict, Any
import pygame

from ..map_operations import EditorMapData


class ToolType(Enum):
    """Types of editing tools."""
    SELECTION = "selection"
    PAINT = "paint"
    TRANSITION = "transition"
    LINE = "line"
    FILL = "fill"
    ERASER = "eraser"


class BaseTool(ABC):
    """
    Abstract base class for all editing tools.
    
    This class defines the interface that all tools must implement.
    """
    
    def __init__(self, tool_type: ToolType, name: str):
        """
        Initialize the tool.
        
        Args:
            tool_type: The type of this tool
            name: Human-readable name of the tool
        """
        self.tool_type = tool_type
        self.name = name
        self.is_active = False
        self.cursor_pos = (0, 0)
        
        # Tool state
        self.is_dragging = False
        self.drag_start_pos = None
        self.last_pos = None
    
    @abstractmethod
    def activate(self) -> None:
        """Called when the tool becomes active."""
        self.is_active = True
    
    @abstractmethod
    def deactivate(self) -> None:
        """Called when the tool becomes inactive."""
        self.is_active = False
        self.is_dragging = False
        self.drag_start_pos = None
        self.last_pos = None
    
    @abstractmethod
    def handle_mouse_down(self, pos: Tuple[int, int], button: int, 
                         map_data: Optional[EditorMapData]) -> bool:
        """
        Handle mouse button down event.
        
        Args:
            pos: Mouse position in tile coordinates
            button: Mouse button (1=left, 2=middle, 3=right)
            map_data: Current map data
            
        Returns:
            True if the event was handled and map was modified
        """
        pass
    
    @abstractmethod
    def handle_mouse_up(self, pos: Tuple[int, int], button: int,
                       map_data: Optional[EditorMapData]) -> bool:
        """
        Handle mouse button up event.
        
        Args:
            pos: Mouse position in tile coordinates
            button: Mouse button (1=left, 2=middle, 3=right)
            map_data: Current map data
            
        Returns:
            True if the event was handled and map was modified
        """
        pass
    
    @abstractmethod
    def handle_mouse_move(self, pos: Tuple[int, int], 
                         map_data: Optional[EditorMapData]) -> bool:
        """
        Handle mouse move event.
        
        Args:
            pos: Mouse position in tile coordinates
            map_data: Current map data
            
        Returns:
            True if the event was handled and map was modified
        """
        pass
    
    @abstractmethod
    def handle_key_down(self, key: int, map_data: Optional[EditorMapData]) -> bool:
        """
        Handle key down event.
        
        Args:
            key: Pygame key constant
            map_data: Current map data
            
        Returns:
            True if the event was handled and map was modified
        """
        pass
    
    @abstractmethod
    def render_overlay(self, screen: pygame.Surface, canvas_rect: pygame.Rect,
                      world_to_screen_func, tile_size: int) -> None:
        """
        Render tool-specific overlay on the canvas.
        
        Args:
            screen: Surface to render to
            canvas_rect: Canvas rectangle for clipping
            world_to_screen_func: Function to convert world to screen coordinates
            tile_size: Current tile size in pixels
        """
        pass
    
    def update_cursor(self, pos: Tuple[int, int]) -> None:
        """Update the cursor position."""
        self.cursor_pos = pos
    
    def get_cursor_type(self) -> int:
        """Get the pygame cursor type for this tool."""
        return pygame.SYSTEM_CURSOR_ARROW
    
    def is_valid_tile_pos(self, pos: Tuple[int, int], map_data: Optional[EditorMapData]) -> bool:
        """Check if a tile position is valid for the current map."""
        if not map_data:
            return False
        
        x, y = pos
        return (0 <= x < map_data.layout.width and 
                0 <= y < map_data.layout.height)
    
    def set_tile_at_pos(self, pos: Tuple[int, int], asset_id: str, 
                       map_data: EditorMapData) -> bool:
        """
        Set a tile at the given position.
        
        Args:
            pos: Tile position (x, y)
            asset_id: Asset ID to place
            map_data: Map data to modify
            
        Returns:
            True if the tile was set successfully
        """
        if not self.is_valid_tile_pos(pos, map_data):
            return False
        
        x, y = pos
        if y < len(map_data.layout.tiles) and x < len(map_data.layout.tiles[y]):
            map_data.layout.tiles[y][x] = asset_id
            map_data.modified = True
            return True
        
        return False
    
    def get_tile_at_pos(self, pos: Tuple[int, int], map_data: EditorMapData) -> Optional[str]:
        """
        Get the tile asset ID at the given position.
        
        Args:
            pos: Tile position (x, y)
            map_data: Map data to read from
            
        Returns:
            Asset ID at the position, or None if invalid
        """
        if not self.is_valid_tile_pos(pos, map_data):
            return None
        
        x, y = pos
        if y < len(map_data.layout.tiles) and x < len(map_data.layout.tiles[y]):
            return map_data.layout.tiles[y][x]
        
        return None
