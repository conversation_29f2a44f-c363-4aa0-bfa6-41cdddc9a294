"""
Selection Tool

This module implements the selection tool for selecting and inspecting tiles/entities.
"""

import pygame
from typing import Optional, Tuple, List

from .base_tool import BaseTool, ToolType
from ..map_operations import EditorMapData


class SelectionTool(BaseTool):
    """
    Selection tool for selecting and inspecting tiles and entities.
    
    This tool allows users to click on tiles to select and inspect them.
    """
    
    def __init__(self):
        """Initialize the selection tool."""
        super().__init__(ToolType.SELECTION, "Selection")
        
        # Selection state
        self.selected_pos: Optional[Tuple[int, int]] = None
        self.selected_tile_asset: Optional[str] = None
        self.selected_entities: List[dict] = []
        
        # Visual feedback
        self.selection_color = (255, 255, 0)  # Yellow
        self.selection_width = 2
    
    def activate(self) -> None:
        """Called when the tool becomes active."""
        super().activate()
    
    def deactivate(self) -> None:
        """Called when the tool becomes inactive."""
        super().deactivate()
        self.clear_selection()
    
    def handle_mouse_down(self, pos: Tuple[int, int], button: int, 
                         map_data: Optional[EditorMapData]) -> bool:
        """Handle mouse button down event."""
        if not map_data:
            return False
        
        if button == 1:  # Left click - select
            self._select_at_position(pos, map_data)
            return False  # Selection doesn't modify the map
        
        return False
    
    def handle_mouse_up(self, pos: Tuple[int, int], button: int,
                       map_data: Optional[EditorMapData]) -> bool:
        """Handle mouse button up event."""
        return False
    
    def handle_mouse_move(self, pos: Tuple[int, int], 
                         map_data: Optional[EditorMapData]) -> bool:
        """Handle mouse move event."""
        return False
    
    def handle_key_down(self, key: int, map_data: Optional[EditorMapData]) -> bool:
        """Handle key down event."""
        if key == pygame.K_ESCAPE:
            self.clear_selection()
            return False
        
        return False
    
    def render_overlay(self, screen: pygame.Surface, canvas_rect: pygame.Rect,
                      world_to_screen_func, tile_size: int) -> None:
        """Render tool-specific overlay on the canvas."""
        if not self.is_active or not self.selected_pos:
            return
        
        # Calculate screen position of selected tile
        # tile_size is now the base tile size, world_to_screen_func handles zoom scaling
        world_x = self.selected_pos[0] * tile_size
        world_y = self.selected_pos[1] * tile_size
        screen_x, screen_y = world_to_screen_func(world_x, world_y)
        
        # Calculate the display size by checking how much a tile spans on screen
        test_x, test_y = world_to_screen_func(tile_size, tile_size)
        origin_x, origin_y = world_to_screen_func(0, 0)
        display_size = int(test_x - origin_x)  # This gives us the zoomed tile size
        
        # Draw selection rectangle
        selection_rect = pygame.Rect(screen_x, screen_y, display_size, display_size)
        
        # Only draw if within canvas bounds
        if canvas_rect.colliderect(selection_rect):
            # Clip to canvas
            clipped_rect = selection_rect.clip(canvas_rect)
            pygame.draw.rect(screen, self.selection_color, clipped_rect, self.selection_width)
    
    def get_cursor_type(self) -> int:
        """Get the pygame cursor type for this tool."""
        return pygame.SYSTEM_CURSOR_HAND
    
    def clear_selection(self) -> None:
        """Clear the current selection."""
        self.selected_pos = None
        self.selected_tile_asset = None
        self.selected_entities = []
    
    def get_selection_info(self) -> dict:
        """
        Get information about the current selection.
        
        Returns:
            Dictionary with selection information
        """
        if not self.selected_pos:
            return {}
        
        info = {
            'position': self.selected_pos,
            'tile_asset': self.selected_tile_asset,
            'entities': self.selected_entities.copy()
        }
        
        return info
    
    def _select_at_position(self, pos: Tuple[int, int], map_data: EditorMapData) -> None:
        """
        Select the tile and entities at the given position.
        
        Args:
            pos: Tile position to select
            map_data: Map data to read from
        """
        if not self.is_valid_tile_pos(pos, map_data):
            self.clear_selection()
            return
        
        self.selected_pos = pos
        
        # Get tile at position
        self.selected_tile_asset = self.get_tile_at_pos(pos, map_data)
        
        # Get entities at position
        x, y = pos
        self.selected_entities = [
            entity for entity in map_data.layout.entities
            if entity.get('x') == x and entity.get('y') == y
        ]
