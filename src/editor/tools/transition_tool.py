"""
Transition Tool

This module provides a tool for placing and configuring level transition tiles.
"""

import pygame
from typing import Op<PERSON>, <PERSON><PERSON>, Dict, Any, Callable
from pathlib import Path

from .base_tool import BaseTool, ToolType
from ..map_operations import EditorMapData
from src.infrastructure.logging import get_logger


class TransitionTool(BaseTool):
    """
    Transition tool for placing and configuring level exit tiles.
    
    This tool allows users to place numbered exit tiles (0-9) and configure
    their destination maps and spawn points.
    """
    
    def __init__(self):
        """Initialize the transition tool."""
        super().__init__(ToolType.TRANSITION, "Transition")
        self.logger = get_logger(__name__)
        
        # Current transition number to place (0-9)
        self.current_transition_number: str = "0"
        
        # Callback for opening transition configuration dialog
        self.config_dialog_callback: Optional[Callable] = None
        
        # Map operations for metadata handling
        self.map_operations = None
        
        # Visual feedback
        self.preview_color = (0, 255, 255, 128)  # Cyan with transparency
        
    def set_config_dialog_callback(self, callback: Callable) -> None:
        """Set the callback for opening the transition configuration dialog."""
        self.config_dialog_callback = callback
    
    def set_map_operations(self, map_operations) -> None:
        """Set the map operations instance for metadata handling."""
        self.map_operations = map_operations
    
    def set_transition_number(self, number: str) -> None:
        """Set the current transition number to place."""
        if number in "0123456789":
            self.current_transition_number = number
            self.logger.info(f"Set transition number to: {number}")
    
    def activate(self) -> None:
        """Called when the tool becomes active."""
        super().activate()
        self.logger.info("Transition tool activated")
    
    def deactivate(self) -> None:
        """Called when the tool becomes inactive."""
        super().deactivate()
        self.logger.info("Transition tool deactivated")
    
    def handle_mouse_down(self, pos: Tuple[int, int], button: int, 
                         map_data: Optional[EditorMapData]) -> bool:
        """Handle mouse button down event."""
        if not map_data:
            return False
        
        if button == 1:  # Left click - place transition tile
            return self._place_transition_at_position(pos, map_data)
        elif button == 3:  # Right click - configure existing transition
            return self._configure_transition_at_position(pos, map_data)
        
        return False
    
    def handle_mouse_up(self, pos: Tuple[int, int], button: int,
                       map_data: Optional[EditorMapData]) -> bool:
        """Handle mouse button up event."""
        # No special handling needed for mouse up
        return False
    
    def handle_mouse_move(self, pos: Tuple[int, int], 
                         map_data: Optional[EditorMapData]) -> bool:
        """Handle mouse move event."""
        # Update cursor position for preview
        self.update_cursor(pos)
        return False
    
    def handle_key_down(self, key: int, map_data: Optional[EditorMapData]) -> bool:
        """Handle key down event."""
        # Number keys 0-9 to select transition number
        if pygame.K_0 <= key <= pygame.K_9:
            number = str(key - pygame.K_0)
            self.set_transition_number(number)
            return False
        
        return False
    
    def render_overlay(self, screen: pygame.Surface, canvas_rect: pygame.Rect,
                      world_to_screen_func, tile_size: int) -> None:
        """Render tool-specific overlay on the canvas."""
        if not self.is_active:
            return
        
        # Draw preview at cursor position
        # tile_size is now the base tile size, world_to_screen_func handles zoom scaling
        cursor_world_x = self.cursor_pos[0] * tile_size
        cursor_world_y = self.cursor_pos[1] * tile_size
        screen_x, screen_y = world_to_screen_func(cursor_world_x, cursor_world_y)
        
        # Calculate the display size by checking how much a tile spans on screen
        test_x, test_y = world_to_screen_func(tile_size, tile_size)
        origin_x, origin_y = world_to_screen_func(0, 0)
        display_size = int(test_x - origin_x)  # This gives us the zoomed tile size
        
        # Only draw if within canvas bounds
        if (canvas_rect.left <= screen_x <= canvas_rect.right - display_size and
            canvas_rect.top <= screen_y <= canvas_rect.bottom - display_size):
            
            # Draw preview rectangle
            preview_rect = pygame.Rect(screen_x, screen_y, display_size, display_size)
            preview_surface = pygame.Surface((display_size, display_size), pygame.SRCALPHA)
            preview_surface.fill(self.preview_color)
            screen.blit(preview_surface, preview_rect)
            
            # Draw transition number in the center
            font_size = max(24, display_size // 4)
            font = pygame.font.Font(None, font_size)
            text_surface = font.render(self.current_transition_number, True, (255, 255, 255))
            text_rect = text_surface.get_rect(center=preview_rect.center)
            screen.blit(text_surface, text_rect)
    
    def get_cursor_type(self) -> int:
        """Get the pygame cursor type for this tool."""
        return pygame.SYSTEM_CURSOR_CROSSHAIR
    
    def _place_transition_at_position(self, pos: Tuple[int, int], map_data: EditorMapData) -> bool:
        """
        Place a transition tile at the given position, or configure existing one.

        Args:
            pos: Tile position to place transition at
            map_data: Map data to modify

        Returns:
            True if transition placement was successful or existing tile was configured
        """
        if not self.is_valid_tile_pos(pos, map_data):
            return False

        # Check if there's already a transition tile at this position
        existing_tile_id = self.get_tile_at_pos(pos, map_data)
        if existing_tile_id and existing_tile_id.startswith("tile.exit."):
            # Configure existing transition instead of placing a new one
            return self._configure_transition_at_position(pos, map_data)

        # Place the exit tile with the correct asset ID
        # All transition tiles use the same asset but different symbols in the map file
        success = self.set_tile_at_pos(pos, "tile.exit.portal", map_data)

        if success:
            # Store the transition number metadata for proper symbol selection during save
            if self.map_operations:
                self.map_operations.set_transition_at_position(pos, int(self.current_transition_number), map_data)

            self.logger.info(f"Placed transition {self.current_transition_number} at {pos}")

            # Open configuration dialog if callback is set
            if self.config_dialog_callback:
                self.config_dialog_callback(self.current_transition_number, pos)

        return success
    
    def _configure_transition_at_position(self, pos: Tuple[int, int], map_data: EditorMapData) -> bool:
        """
        Configure an existing transition tile at the given position.
        
        Args:
            pos: Tile position to configure
            map_data: Map data to read from
            
        Returns:
            True if configuration dialog was opened
        """
        if not self.is_valid_tile_pos(pos, map_data):
            return False
        
        # Check if there's a transition tile at this position
        tile_id = self.get_tile_at_pos(pos, map_data)
        if tile_id and tile_id.startswith("tile.exit."):
            # Try to get the stored transition number
            transition_number = None
            if self.map_operations:
                transition_number = self.map_operations.get_transition_at_position(pos, map_data)

            # If no metadata found, use current selected number as fallback
            if transition_number is None:
                transition_number = int(self.current_transition_number)
                # Store it for future reference
                if self.map_operations:
                    self.map_operations.set_transition_at_position(pos, transition_number, map_data)

            self.logger.info(f"Configuring transition {transition_number} at {pos}")

            # Open configuration dialog if callback is set
            if self.config_dialog_callback:
                self.config_dialog_callback(str(transition_number), pos)
                return True
        
        return False
