"""
Game logging utility module.

This module provides a centralized logging system that includes file name 
and line number information in log messages.
"""

import logging
import sys
from pathlib import Path
from typing import Optional


class GameFormatter(logging.Formatter):
    """Custom formatter that prepends file name and line number to log messages."""
    
    def format(self, record: logging.LogRecord) -> str:
        # Get the base filename without the full path
        filename = Path(record.pathname).name
        
        # Create the prefix with file, line number, and log level
        prefix = f"{filename} ({record.lineno}) - {record.levelname}:"
        
        # Format the original message
        original_msg = super().format(record)
        
        # Combine prefix with the message
        return f"{prefix} {original_msg}"


def setup_logging(verbose: bool = False, log_file: Optional[str] = None) -> None:
    """
    Set up the logging system for the game.
    
    Args:
        verbose: If True, enable debug logging
        log_file: Optional file path to write logs to
    """
    # Set the log level based on verbose flag
    log_level = logging.DEBUG if verbose else logging.INFO
    
    # Create formatter
    formatter = GameFormatter()
    
    # Set up root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)
    
    # Clear any existing handlers
    root_logger.handlers.clear()
    
    # Create console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(log_level)
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # Create file handler if specified
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(log_level)
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)


def get_logger(name: str = "") -> logging.Logger:
    """
    Get a logger instance.
    
    Args:
        name: Name for the logger (typically __name__)
        
    Returns:
        Configured logger instance
    """
    return logging.getLogger(name)
