"""
Pygame Event Bus Implementation

This module provides a concrete implementation of the IEventBus interface using Pygame.
"""

from typing import Dict, List, Callable
from collections import defaultdict
import time

from src.application.interfaces import IEventBus
from src.game_core import GameEvent
from src.infrastructure.logging import get_logger


class PygameEventBus(IEventBus):
    """
    Concrete implementation of IEventBus for the Pygame infrastructure.
    
    This event bus allows decoupled communication between game systems.
    Systems can publish events and subscribe to events they're interested in.
    """
    
    def __init__(self):
        # Dictionary mapping event types to lists of handler functions
        self._subscribers: Dict[str, List[Callable[[GameEvent], None]]] = defaultdict(list)
        
        # Statistics for debugging
        self._events_published = 0
        self._events_by_type: Dict[str, int] = defaultdict(int)
        
        # Get logger for this module
        self.logger = get_logger(__name__)
    
    def publish(self, event: GameEvent) -> None:
        """
        Publish an event to all subscribers.
        
        Args:
            event: The event to publish
        """
        # Update statistics
        self._events_published += 1
        self._events_by_type[event.event_type] += 1
        
        # Get all subscribers for this event type
        handlers = self._subscribers.get(event.event_type, [])
        
        # Call each handler, catching and logging any exceptions
        for handler in handlers:
            try:
                handler(event)
            except Exception as e:
                self.logger.error(f"Error in event handler for {event.event_type}: {e}")
                # Continue processing other handlers even if one fails
    
    def subscribe(self, event_type: str, handler: Callable[[GameEvent], None]) -> None:
        """
        Subscribe to events of a specific type.
        
        Args:
            event_type: The type of events to subscribe to
            handler: The function to call when events of this type are published
        """
        if handler not in self._subscribers[event_type]:
            self._subscribers[event_type].append(handler)
    
    def unsubscribe(self, event_type: str, handler: Callable[[GameEvent], None]) -> None:
        """
        Unsubscribe from events of a specific type.
        
        Args:
            event_type: The type of events to unsubscribe from
            handler: The handler function to remove
        """
        if handler in self._subscribers[event_type]:
            self._subscribers[event_type].remove(handler)
            
            # Clean up empty lists
            if not self._subscribers[event_type]:
                del self._subscribers[event_type]
    
    def get_subscriber_count(self, event_type: str) -> int:
        """Get the number of subscribers for a specific event type."""
        return len(self._subscribers.get(event_type, []))
    
    def get_all_event_types(self) -> List[str]:
        """Get a list of all event types that have subscribers."""
        return list(self._subscribers.keys())
    
    def get_statistics(self) -> Dict[str, int]:
        """Get event bus statistics for debugging."""
        return {
            "total_events_published": self._events_published,
            "unique_event_types": len(self._events_by_type),
            "total_subscribers": sum(len(handlers) for handlers in self._subscribers.values()),
            **self._events_by_type
        }
    
    def clear_statistics(self) -> None:
        """Reset event statistics."""
        self._events_published = 0
        self._events_by_type.clear()
    
    def publish_delayed(self, event: GameEvent, delay_seconds: float) -> None:
        """
        Publish an event after a delay (useful for timed effects).
        
        Args:
            event: The event to publish
            delay_seconds: How long to wait before publishing
        """
        # For now, we'll implement this synchronously
        # In a real implementation, you might want to use threading or a scheduler
        import threading
        
        def delayed_publish():
            time.sleep(delay_seconds)
            self.publish(event)
        
        thread = threading.Thread(target=delayed_publish)
        thread.daemon = True
        thread.start()
    
    def clear_all_subscribers(self) -> None:
        """Clear all subscribers (useful for cleanup or testing)."""
        self._subscribers.clear()


class EventLogger:
    """
    A utility class that can be used to log all events for debugging.
    Subscribe this to the event bus to see all events flowing through the system.
    """
    
    def __init__(self, verbose: bool = False):
        self.verbose = verbose
        self.logged_events: List[GameEvent] = []
        self.logger = get_logger(__name__)
    
    def log_event(self, event: GameEvent) -> None:
        """Log an event."""
        self.logged_events.append(event)
        
        if self.verbose:
            self.logger.debug(f"[EVENT] {event.event_type}: {event.data}")
    
    def get_events_by_type(self, event_type: str) -> List[GameEvent]:
        """Get all logged events of a specific type."""
        return [event for event in self.logged_events if event.event_type == event_type]
    
    def clear_log(self) -> None:
        """Clear the event log."""
        self.logged_events.clear()
