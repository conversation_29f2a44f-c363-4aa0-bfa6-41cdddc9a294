"""
Pygame Audio Player Implementation

This module provides a concrete implementation of the IAudioPlayer interface using Pygame.
"""

import pygame
from typing import Dict, Optional
from pathlib import Path

from src.application.interfaces import IAudioPlayer
from src.infrastructure.logging import get_logger


class PygameAudioPlayer(IAudioPlayer):
    """
    Pygame implementation of the audio player interface.
    
    This implementation uses procedural audio generation and basic Pygame audio capabilities.
    For a production game, you might want to load actual audio files.
    """
    
    def __init__(self, sound_directory: Optional[str] = None, music_directory: Optional[str] = None):
        """
        Initialize the Pygame audio player.
        
        Args:
            sound_directory: Directory containing sound effect files (optional for procedural audio)
            music_directory: Directory containing music files (optional for procedural audio)
        """
        # Initialize pygame mixer
        if not pygame.mixer.get_init():
            pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)
        
        self.sound_directory = Path(sound_directory) if sound_directory else None
        self.music_directory = Path(music_directory) if music_directory else None
        
        # Cache for loaded sounds
        self._sound_cache: Dict[str, pygame.mixer.Sound] = {}
        
        # Volume settings
        self._master_volume = 1.0
        self._music_volume = 0.7
        self._sound_volume = 0.8
        
        # Current music state
        self._current_music: Optional[str] = None
        
        # Ambient sounds management
        self._ambient_sounds: Dict[str, pygame.mixer.Channel] = {}
        self._ambient_volume = 0.4
        
        # Get logger for this module
        self.logger = get_logger(__name__)
        self.logger.info("PygameAudioPlayer initialized")
    
    def play_sound(self, sound_id: str, volume: float = 1.0) -> None:
        """Play a sound effect."""
        try:
            sound = self._get_sound(sound_id)
            if sound:
                # Apply volume scaling
                final_volume = volume * self._sound_volume * self._master_volume
                sound.set_volume(min(1.0, max(0.0, final_volume)))
                sound.play()
        except Exception as e:
            self.logger.warning(f"Failed to play sound '{sound_id}': {e}")
    
    def play_music(self, music_id: str, loop: bool = True, volume: float = 1.0) -> None:
        """Play background music."""
        try:
            # Stop current music if different
            if self._current_music != music_id:
                pygame.mixer.music.stop()
                
                # For now, generate a simple tone or use a placeholder
                # In a real game, you'd load the actual music file
                music_file = self._get_music_file(music_id)
                if music_file and music_file.exists():
                    pygame.mixer.music.load(str(music_file))
                else:
                    # Generate procedural music (very basic example)
                    music_sound = self._generate_music(music_id)
                    if music_sound:
                        # Create a temporary file for pygame.mixer.music
                        # For simplicity, we'll just skip music in this implementation
                        self.logger.debug(f"Playing procedural music: {music_id}")
                        return
                
                # Set volume and play
                final_volume = volume * self._music_volume * self._master_volume
                pygame.mixer.music.set_volume(min(1.0, max(0.0, final_volume)))
                
                loops = -1 if loop else 0
                pygame.mixer.music.play(loops)
                
                self._current_music = music_id
                
        except Exception as e:
            self.logger.warning(f"Failed to play music '{music_id}': {e}")
    
    def stop_music(self) -> None:
        """Stop currently playing music."""
        pygame.mixer.music.stop()
        self._current_music = None
    
    def set_master_volume(self, volume: float) -> None:
        """Set the master volume (0.0 to 1.0)."""
        self._master_volume = min(1.0, max(0.0, volume))
        
        # Update current music volume
        if pygame.mixer.music.get_busy():
            music_vol = self._music_volume * self._master_volume
            pygame.mixer.music.set_volume(music_vol)
    
    def set_music_volume(self, volume: float) -> None:
        """Set the music volume (0.0 to 1.0)."""
        self._music_volume = min(1.0, max(0.0, volume))
        
        # Update current music volume if playing
        if pygame.mixer.music.get_busy():
            music_vol = self._music_volume * self._master_volume
            pygame.mixer.music.set_volume(music_vol)
    
    def set_sound_volume(self, volume: float) -> None:
        """Set the sound effects volume (0.0 to 1.0)."""
        self._sound_volume = min(1.0, max(0.0, volume))
    
    def play_ambient(self, sound_id: str, volume: float = 1.0, fade_in_ms: int = 2000) -> None:
        """Play an ambient sound that loops continuously."""
        try:
            sound = self._get_sound(sound_id)
            if sound:
                # Stop any existing ambient sound with this ID
                self.stop_ambient(sound_id)
                
                # Find a free channel or allocate one
                channel = pygame.mixer.find_channel()
                if not channel:
                    # Force allocate more channels if needed
                    pygame.mixer.set_num_channels(pygame.mixer.get_num_channels() + 4)
                    channel = pygame.mixer.find_channel()
                
                if channel:
                    # Apply volume scaling
                    final_volume = volume * self._ambient_volume * self._master_volume
                    sound.set_volume(min(1.0, max(0.0, final_volume)))
                    
                    # Play with fade-in effect
                    channel.play(sound, loops=-1, fade_ms=fade_in_ms)
                    self._ambient_sounds[sound_id] = channel
                    self.logger.debug(f"Started ambient sound: {sound_id}")
                    
        except Exception as e:
            self.logger.warning(f"Failed to play ambient sound '{sound_id}': {e}")
    
    def stop_ambient(self, sound_id: str, fade_out_ms: int = 1000) -> None:
        """Stop a specific ambient sound."""
        if sound_id in self._ambient_sounds:
            channel = self._ambient_sounds[sound_id]
            if channel.get_busy():
                channel.fadeout(fade_out_ms)
            del self._ambient_sounds[sound_id]
            self.logger.debug(f"Stopped ambient sound: {sound_id}")
    
    def stop_all_ambient(self, fade_out_ms: int = 1000) -> None:
        """Stop all ambient sounds."""
        for sound_id in list(self._ambient_sounds.keys()):
            self.stop_ambient(sound_id, fade_out_ms)
    
    def set_ambient_volume(self, volume: float) -> None:
        """Set the ambient sounds volume (0.0 to 1.0)."""
        self._ambient_volume = min(1.0, max(0.0, volume))
        
        # Update volume for currently playing ambient sounds
        for sound_id, channel in self._ambient_sounds.items():
            if channel.get_busy():
                sound = self._get_sound(sound_id)
                if sound:
                    final_volume = self._ambient_volume * self._master_volume
                    sound.set_volume(min(1.0, max(0.0, final_volume)))
    
    def _get_sound(self, sound_id: str) -> Optional[pygame.mixer.Sound]:
        """Get a sound, loading it from cache or generating it."""
        if sound_id in self._sound_cache:
            return self._sound_cache[sound_id]
        
        # Try to load from file first
        if self.sound_directory:
            sound_file = self.sound_directory / f"{sound_id}.wav"
            if sound_file.exists():
                try:
                    sound = pygame.mixer.Sound(str(sound_file))
                    self._sound_cache[sound_id] = sound
                    return sound
                except Exception as e:
                    self.logger.warning(f"Failed to load sound file '{sound_file}': {e}")
        
        # Generate procedural sound
        sound = self._generate_sound(sound_id)
        if sound:
            self._sound_cache[sound_id] = sound
        
        return sound
    
    def _get_music_file(self, music_id: str) -> Optional[Path]:
        """Get the path to a music file."""
        if not self.music_directory:
            return None
        
        # Try common music file extensions
        for ext in ['.ogg', '.mp3', '.wav']:
            music_file = self.music_directory / f"{music_id}{ext}"
            if music_file.exists():
                return music_file
        
        return None
    
    def _generate_sound(self, sound_id: str) -> Optional[pygame.mixer.Sound]:
        """Generate a procedural sound effect."""
        try:
            import numpy as np
            
            # Sound parameters
            sample_rate = 22050
            duration = 0.3  # seconds
            
            # Generate different sounds based on ID
            if 'hit' in sound_id or 'damage' in sound_id:
                # Sharp impact sound
                t = np.linspace(0, duration, int(sample_rate * duration))
                frequency = 200
                wave = np.sin(2 * np.pi * frequency * t) * np.exp(-t * 8)
                
            elif 'attack' in sound_id or 'sword' in sound_id:
                # Swish sound
                t = np.linspace(0, duration * 0.5, int(sample_rate * duration * 0.5))
                frequency = 150
                wave = np.sin(2 * np.pi * frequency * t) * np.exp(-t * 6)
                
            elif 'pickup' in sound_id or 'coin' in sound_id:
                # Pleasant pickup sound
                t = np.linspace(0, duration * 0.3, int(sample_rate * duration * 0.3))
                frequency = 440
                wave = np.sin(2 * np.pi * frequency * t) * np.exp(-t * 4)
                
            elif 'footstep' in sound_id or 'move' in sound_id:
                # Soft thud
                t = np.linspace(0, duration * 0.2, int(sample_rate * duration * 0.2))
                frequency = 80
                wave = np.sin(2 * np.pi * frequency * t) * np.exp(-t * 15)
                
            elif sound_id == 'birds':
                # Bird chirping sounds - multiple overlapping chirps
                duration = 5.0  # Longer for ambient sounds
                t = np.linspace(0, duration, int(sample_rate * duration))
                wave = np.zeros_like(t)
                
                # Generate multiple bird calls at different times
                for i in range(0, int(duration), 1):
                    if np.random.random() < 0.3:  # 30% chance per second
                        start_idx = int(i * sample_rate)
                        chirp_duration = 0.2 + np.random.random() * 0.3
                        end_idx = min(len(t), start_idx + int(chirp_duration * sample_rate))
                        
                        # Create a chirp
                        chirp_t = t[start_idx:end_idx] - t[start_idx]
                        freq = 800 + np.random.random() * 1200  # Random frequency 800-2000 Hz
                        freq_mod = freq * (1 + 0.5 * np.sin(10 * chirp_t))  # Frequency modulation
                        chirp = np.sin(2 * np.pi * freq_mod * chirp_t) * np.exp(-chirp_t * 8)
                        wave[start_idx:end_idx] += chirp * (0.3 + np.random.random() * 0.4)
                
            elif sound_id == 'wind':
                # Wind sound - pink noise with low-pass filtering
                duration = 8.0  # Longer ambient sound
                t = np.linspace(0, duration, int(sample_rate * duration))
                
                # Generate pink noise (1/f noise)
                freqs = np.fft.fftfreq(len(t), 1/sample_rate)
                # Create pink noise spectrum (1/f amplitude)
                pink_spectrum = np.zeros_like(freqs, dtype=complex)
                for i, f in enumerate(freqs):
                    if f > 0:
                        # Pink noise has 1/f amplitude characteristic
                        amplitude = 1.0 / np.sqrt(f) if f > 10 else 1.0 / np.sqrt(10)
                        phase = np.random.random() * 2 * np.pi
                        pink_spectrum[i] = amplitude * np.exp(1j * phase)
                
                # Apply low-pass filter for wind effect
                cutoff_freq = 300  # Hz
                pink_spectrum[np.abs(freqs) > cutoff_freq] *= 0.1
                
                # Convert back to time domain
                wave = np.real(np.fft.ifft(pink_spectrum))
                wave = wave / np.max(np.abs(wave))  # Normalize
                
                # Add some gentle modulation for wind gusts
                modulation = 0.7 + 0.3 * np.sin(2 * np.pi * 0.1 * t)  # 0.1 Hz modulation
                wave = wave * modulation
                
            else:
                # Default beep
                t = np.linspace(0, duration * 0.1, int(sample_rate * duration * 0.1))
                frequency = 440
                wave = np.sin(2 * np.pi * frequency * t)
            
            # Convert to pygame sound format
            wave = (wave * 32767).astype(np.int16)
            stereo_wave = np.array([wave, wave]).T.copy()  # Ensure C-contiguous
            
            return pygame.sndarray.make_sound(stereo_wave)
            
        except ImportError:
            self.logger.warning("NumPy not available for procedural audio generation")
            return None
        except Exception as e:
            self.logger.warning(f"Failed to generate sound '{sound_id}': {e}")
            return None
    
    def _generate_music(self, music_id: str) -> Optional[pygame.mixer.Sound]:
        """Generate procedural music (very basic implementation)."""
        # For this demo, we'll skip procedural music generation
        # In a real game, you might generate simple melodies or ambient sounds
        self.logger.debug(f"Procedural music generation for '{music_id}' not implemented")
        return None
