"""
Save Game Repository Implementation

This module provides a concrete implementation of the ISaveGameRepository interface
using JSON files for persistence.
"""

import json
from typing import Optional, List, Dict, Any
from pathlib import Path
from datetime import datetime

from src.application.interfaces import ISaveGameRepository, GameStateData
from src.game_core import Player, Item, Position, Stats
from src.infrastructure.logging import get_logger


class JsonSaveGameRepository(ISaveGameRepository):
    """
    JSON file-based implementation of the save game repository.
    
    This implementation stores game saves as JSON files in a designated save directory.
    """
    
    def __init__(self, save_directory: str = "saves"):
        """
        Initialize the save game repository.
        
        Args:
            save_directory: Directory to store save game files
        """
        self.save_directory = Path(save_directory)
        self.save_directory.mkdir(exist_ok=True)
        
        # Get logger for this module
        self.logger = get_logger(__name__)
        self.logger.info(f"JsonSaveGameRepository initialized (saves in: {self.save_directory})")
    
    def save_game(self, game_state: GameStateData, save_slot: str = "default") -> None:
        """Save the current game state."""
        try:
            save_file = self.save_directory / f"{save_slot}.json"

            # Convert game state to serializable format
            save_data = self._serialize_game_state(game_state)

            # Add enhanced metadata
            save_data["metadata"] = {
                "save_slot": save_slot,
                "timestamp": datetime.now().isoformat(),
                "version": "1.0.0",
                "level_name": self._get_level_display_name(game_state.current_level_id),
                "player_level": game_state.player.level if game_state.player else 1,
                "player_gold": game_state.player.inventory.get("gold", 0) if game_state.player else 0,
                "player_name": game_state.player.name if game_state.player else "Unknown"
            }

            # Write to file
            with open(save_file, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, indent=2, ensure_ascii=False)

            self.logger.info(f"Game saved to slot '{save_slot}'")

        except Exception as e:
            self.logger.error(f"Failed to save game to slot '{save_slot}': {e}")
            raise
    
    def load_game(self, save_slot: str = "default") -> Optional[GameStateData]:
        """Load a saved game state."""
        try:
            save_file = self.save_directory / f"{save_slot}.json"
            
            if not save_file.exists():
                self.logger.warning(f"No save file found for slot '{save_slot}'")
                return None
            
            # Read save file
            with open(save_file, 'r', encoding='utf-8') as f:
                save_data = json.load(f)
            
            # Deserialize game state
            game_state = self._deserialize_game_state(save_data)
            
            self.logger.info(f"Game loaded from slot '{save_slot}'")
            return game_state
            
        except Exception as e:
            self.logger.error(f"Failed to load game from slot '{save_slot}': {e}")
            return None
    
    def delete_save(self, save_slot: str) -> None:
        """Delete a saved game."""
        try:
            save_file = self.save_directory / f"{save_slot}.json"
            
            if save_file.exists():
                save_file.unlink()
                self.logger.info(f"Save slot '{save_slot}' deleted")
            else:
                self.logger.warning(f"Save slot '{save_slot}' does not exist")
                
        except Exception as e:
            self.logger.error(f"Failed to delete save slot '{save_slot}': {e}")
            raise
    
    def list_saves(self) -> List[str]:
        """List all available save slots."""
        try:
            save_files = list(self.save_directory.glob("*.json"))
            save_slots = [f.stem for f in save_files]
            save_slots.sort()
            return save_slots
            
        except Exception as e:
            self.logger.error(f"Failed to list save slots: {e}")
            return []
    
    def get_save_info(self, save_slot: str) -> Optional[Dict[str, Any]]:
        """Get information about a save slot without fully loading it."""
        try:
            save_file = self.save_directory / f"{save_slot}.json"
            
            if not save_file.exists():
                return None
            
            # Read just the metadata
            with open(save_file, 'r', encoding='utf-8') as f:
                save_data = json.load(f)
            
            metadata = save_data.get("metadata", {})
            
            # Add file information
            stat = save_file.stat()
            metadata["file_size"] = stat.st_size
            metadata["file_modified"] = datetime.fromtimestamp(stat.st_mtime).isoformat()
            
            # Add basic game state info
            if "current_level_id" in save_data:
                metadata["current_level"] = save_data["current_level_id"]
            
            if "player" in save_data and save_data["player"]:
                player_data = save_data["player"]
                metadata["player_level"] = player_data.get("level", 1)
                if "stats" in player_data:
                    stats = player_data["stats"]
                    metadata["player_hp"] = f"{stats.get('hp', 0)}/{stats.get('max_hp', 0)}"
            
            return metadata
            
        except Exception as e:
            self.logger.error(f"Failed to get save info for slot '{save_slot}': {e}")
            return None

    def list_save_slots(self) -> Dict[str, Optional[Dict[str, Any]]]:
        """
        List all save slots (1-10) with their metadata.

        Returns:
            Dictionary mapping slot names to metadata (None for empty slots)
        """
        slots = {}
        for i in range(1, 11):
            slot_name = f"slot_{i}"
            slots[slot_name] = self.get_save_info(slot_name)
        return slots

    def get_numbered_slot_name(self, slot_number: int) -> str:
        """
        Get the slot name for a numbered slot (1-10).

        Args:
            slot_number: Slot number (1-10)

        Returns:
            Slot name string
        """
        if not 1 <= slot_number <= 10:
            raise ValueError(f"Slot number must be between 1 and 10, got {slot_number}")
        return f"slot_{slot_number}"

    def delete_save_slot(self, save_slot: str) -> bool:
        """
        Delete a save slot.

        Args:
            save_slot: Name of the save slot to delete

        Returns:
            True if deletion was successful, False otherwise
        """
        try:
            save_file = self.save_directory / f"{save_slot}.json"

            if not save_file.exists():
                self.logger.warning(f"Save slot '{save_slot}' does not exist")
                return False

            save_file.unlink()
            self.logger.info(f"Save slot '{save_slot}' deleted")
            return True

        except Exception as e:
            self.logger.error(f"Failed to delete save slot '{save_slot}': {e}")
            return False

    def _get_level_display_name(self, level_id: str) -> str:
        """
        Get a human-readable display name for a level.

        Args:
            level_id: Level identifier

        Returns:
            Human-readable level name
        """
        # Convert level IDs to display names
        level_names = {
            "town_caledon": "Town of Caledon",
            "forest_entrance": "Forest Entrance",
            "deep_forest": "Deep Forest",
            "mountain_pass": "Mountain Pass",
            "cave_entrance": "Cave Entrance",
            "underground_cavern": "Underground Cavern"
        }

        return level_names.get(level_id, level_id.replace("_", " ").title())

    def _serialize_game_state(self, game_state: GameStateData) -> Dict[str, Any]:
        """Convert GameStateData to a JSON-serializable dictionary."""
        data = {}
        
        # Serialize player
        if game_state.player:
            data["player"] = self._serialize_player(game_state.player)
        else:
            data["player"] = None
        
        # Note: monsters and npcs are not saved as they should be loaded fresh from level files
        # This matches the behavior of level transitions and keeps the game consistent

        # Serialize items
        data["items"] = {
            item_id: self._serialize_item(item)
            for item_id, item in game_state.items.items()
        }

        # Simple data
        data["current_level_id"] = game_state.current_level_id
        # Note: collision_map and level_tiles are not saved as they should be loaded from level files

        # Quest data
        data["quest_data"] = game_state.quest_data.copy() if game_state.quest_data else {}

        return data
    
    def _deserialize_game_state(self, data: Dict[str, Any]) -> GameStateData:
        """Convert a dictionary back to GameStateData."""
        
        # Deserialize player
        player = None
        if data.get("player"):
            player = self._deserialize_player(data["player"])

        # Deserialize items
        items = {
            item_id: self._deserialize_item(item_data)
            for item_id, item_data in data.get("items", {}).items()
        }

        return GameStateData(
            player=player,
            items=items,
            current_level_id=data.get("current_level_id"),
            quest_data=data.get("quest_data", {})
            # Note: collision_map, level_tiles, monsters, and npcs are not loaded from save files
            # They will be loaded fresh from level files by the game engine
        )
    
    def _serialize_player(self, player: Player) -> Dict[str, Any]:
        """Serialize a Player object."""
        return {
            "id": player.id,
            "name": player.name,
            "position": {"x": player.position.x, "y": player.position.y},
            "asset_id": player.asset_id,
            "stats": self._serialize_stats(player.stats),
            "size": {"width": player.size[0], "height": player.size[1]},
            "level": player.level,
            "experience": player.experience,
            "inventory": player.inventory,
            "head_equipment": player.head_equipment,
            "chest_equipment": player.chest_equipment,
            "legs_equipment": player.legs_equipment,
            "boots_equipment": player.boots_equipment,
            "main_hand_weapon": player.main_hand_weapon,
            "off_hand_equipment": player.off_hand_equipment,
            "inventory_max_size": player.inventory_max_size
        }
    
    def _deserialize_player(self, data: Dict[str, Any]) -> Player:
        """Deserialize a Player object."""
        # Handle size field (with backward compatibility)
        size = (64, 64)  # Default size
        if "size" in data:
            size = (data["size"]["width"], data["size"]["height"])

        return Player(
            id=data["id"],
            name=data["name"],
            position=Position(data["position"]["x"], data["position"]["y"]),
            asset_id=data["asset_id"],
            stats=self._deserialize_stats(data["stats"]),
            size=size,
            level=data["level"],
            experience=data["experience"],
            inventory=data["inventory"],
            head_equipment=data.get("head_equipment"),
            chest_equipment=data.get("chest_equipment"),
            legs_equipment=data.get("legs_equipment"),
            boots_equipment=data.get("boots_equipment"),
            main_hand_weapon=data.get("main_hand_weapon"),
            off_hand_equipment=data.get("off_hand_equipment"),
            inventory_max_size=data.get("inventory_max_size", 10)
        )
    

    
    def _serialize_item(self, item: Item) -> Dict[str, Any]:
        """Serialize an Item object."""
        return {
            "id": item.id,
            "name": item.name,
            "position": {"x": item.position.x, "y": item.position.y},
            "asset_id": item.asset_id,
            "item_type": item.item_type,
            "value": item.value,
            "properties": item.properties
        }
    
    def _deserialize_item(self, data: Dict[str, Any]) -> Item:
        """Deserialize an Item object."""
        return Item(
            id=data["id"],
            name=data["name"],
            position=Position(data["position"]["x"], data["position"]["y"]),
            asset_id=data["asset_id"],
            item_type=data["item_type"],
            value=data["value"],
            properties=data["properties"]
        )


    
    def _serialize_stats(self, stats: Stats) -> Dict[str, Any]:
        """Serialize a Stats object."""
        return {
            "hp": stats.hp,
            "max_hp": stats.max_hp,
            "mp": stats.mp,
            "max_mp": stats.max_mp,
            "strength": stats.strength,
            "defense": stats.defense,
            "speed": stats.speed
        }
    
    def _deserialize_stats(self, data: Dict[str, Any]) -> Stats:
        """Deserialize a Stats object."""
        return Stats(
            hp=data["hp"],
            max_hp=data["max_hp"],
            mp=data["mp"],
            max_mp=data["max_mp"],
            strength=data["strength"],
            defense=data["defense"],
            speed=data["speed"]
        )
