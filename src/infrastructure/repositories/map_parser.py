"""
Map Parser Implementation

This module handles parsing of .map files and merging with the base legend.
It converts text-based map files into structured data for the application layer.
"""

import yaml
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

from src.application.interfaces import LevelLayoutData
from src.infrastructure.logging import get_logger


class MapParser:
    """
    Parser for .map files that combines base legend with level-specific overrides.
    
    This class implements the "base-plus-override" model described in the architecture.
    It loads the base legend, then merges level-specific symbols and placements.
    """
    
    def __init__(self, base_legend_path: str):
        """
        Initialize the map parser.
        
        Args:
            base_legend_path: Path to the base_legend.yaml file
        """
        self.base_legend_path = Path(base_legend_path)
        self.base_legend: Dict[str, Any] = {}
        self.logger = get_logger(__name__)
        self._load_base_legend()
    
    def _load_base_legend(self) -> None:
        """Load the base legend from YAML file."""
        try:
            with open(self.base_legend_path, 'r') as f:
                self.base_legend = yaml.safe_load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"Base legend file not found: {self.base_legend_path}")
        except yaml.YAMLError as e:
            raise ValueError(f"Invalid YAML in base legend file: {e}")
    
    def parse_map_file(self, map_file_path: str) -> LevelLayoutData:
        """
        Parse a .map file and return structured level data.
        
        Args:
            map_file_path: Path to the .map file to parse
            
        Returns:
            LevelLayoutData object containing the parsed level information
        """
        map_path = Path(map_file_path)
        
        if not map_path.exists():
            raise FileNotFoundError(f"Map file not found: {map_path}")
        
        try:
            with open(map_path, 'r') as f:
                content = f.read()
            
            # Split into YAML header and ASCII body
            if '---' in content:
                yaml_part, ascii_part = content.split('---', 1)
            else:
                # No YAML header, just ASCII
                yaml_part = ""
                ascii_part = content
            
            # Parse YAML header
            level_config = {}
            if yaml_part.strip():
                try:
                    level_config = yaml.safe_load(yaml_part) or {}
                except yaml.YAMLError as e:
                    raise ValueError(f"Invalid YAML in map file header: {e}")
            
            # Merge legends (level overrides base)
            merged_legend = self.base_legend.copy()
            if 'legend' in level_config and level_config['legend'] is not None:
                merged_legend.update(level_config['legend'])
            
            # Parse ASCII map
            ascii_lines = [line.rstrip() for line in ascii_part.strip().split('\n') if line.strip()]
            
            if not ascii_lines:
                raise ValueError("Map file contains no ASCII map data")
            
            # Determine map dimensions
            width = max(len(line) for line in ascii_lines)
            height = len(ascii_lines)
            
            # Pad all lines to the same width
            ascii_lines = [line.ljust(width) for line in ascii_lines]
            
            # Parse the map
            tiles = []
            entities = []
            collision_map = []

            for y, line in enumerate(ascii_lines):
                tile_row = []
                collision_row = []

                for x, char in enumerate(line):
                    # Get symbol definition from merged legend
                    symbol_def = merged_legend.get(char, {})

                    if not symbol_def:
                        # Unknown symbol, treat as empty space
                        symbol_def = merged_legend.get('.', {
                            'category': 'tile',
                            'type': 'floor',
                            'solid': False,
                            'asset_id': 'tile.floor.dirt'
                        })

                    category = symbol_def.get('category', 'tile')
                    
                    if category == 'tile':
                        # Add tile information
                        tile_row.append(symbol_def.get('asset_id', 'tile.floor.dirt'))
                        collision_row.append(symbol_def.get('solid', False))
                    
                    elif category == 'entity':
                        # Add entity and determine appropriate tile underneath
                        # Look at neighboring tiles to determine what the entity should be standing on
                        neighbor_tile_asset = self._determine_entity_tile(x, y, ascii_lines, merged_legend)
                        tile_row.append(neighbor_tile_asset)

                        # Get collision info for the determined tile
                        # Find the tile definition that matches this asset_id
                        tile_solid = False
                        for symbol, definition in merged_legend.items():
                            if (definition.get('category') == 'tile' and
                                definition.get('asset_id') == neighbor_tile_asset):
                                tile_solid = definition.get('solid', False)
                                break
                        collision_row.append(tile_solid)

                        # Create entity data
                        asset_id = symbol_def.get('asset_id', '')

                        # If no asset_id and this is a monster, try to get it from monster definition
                        if not asset_id and symbol_def.get('category') == 'entity' and symbol_def.get('type') == 'monster':
                            data_id = symbol_def.get('data_id')
                            if data_id:
                                try:
                                    from src.game_data.monsters import get_monster_definition
                                    monster_def = get_monster_definition(data_id)
                                    if monster_def:
                                        asset_id = monster_def.asset_id
                                except Exception:
                                    pass

                        entity_data = {
                            'type': symbol_def.get('type', 'unknown'),
                            'data_id': symbol_def.get('data_id', ''),
                            'x': x,
                            'y': y,
                            'asset_id': asset_id,
                            'properties': symbol_def.get('properties', {})
                        }

                        # Check for placement overrides
                        placements = level_config.get('placements', []) or []
                        placement_override = self._find_placement_override(
                            placements, x, y, char
                        )
                        if placement_override:
                            entity_data.update(placement_override.get('overrides', {}))

                        entities.append(entity_data)
                
                tiles.append(tile_row)
                collision_map.append(collision_row)

            # Create and return level layout data
            return LevelLayoutData(
                width=width,
                height=height,
                tiles=tiles,
                entities=entities,
                collision_map=collision_map,
                metadata=level_config.get('metadata', {})
            )
            
        except Exception as e:
            raise ValueError(f"Error parsing map file {map_path}: {e}") from e
    
    def _find_placement_override(self, placements: List[Dict[str, Any]], x: int, y: int, char: str) -> Optional[Dict[str, Any]]:
        """
        Find a placement override for a specific position and character.
        
        Args:
            placements: List of placement configurations
            x: X coordinate
            y: Y coordinate  
            char: The character at this position
            
        Returns:
            Placement override data if found, None otherwise
        """
        for placement in placements:
            if (placement.get('char') == char and 
                placement.get('position', {}).get('x') == x and
                placement.get('position', {}).get('y') == y):
                return placement
        return None
    
    def validate_map_file(self, map_file_path: str) -> List[str]:
        """
        Validate a map file and return a list of warnings/errors.
        
        Args:
            map_file_path: Path to the .map file to validate
            
        Returns:
            List of validation messages (empty if valid)
        """
        issues = []
        
        try:
            layout_data = self.parse_map_file(map_file_path)
            
            # Check for common issues
            if layout_data.width == 0 or layout_data.height == 0:
                issues.append("Map has zero width or height")
            
            if not any(entity['type'] == 'player' for entity in layout_data.entities):
                issues.append("No player start position found")
            
            # Check for unknown asset IDs (in a real implementation, you'd validate against the asset registry)
            unique_asset_ids = set()
            for row in layout_data.tiles:
                unique_asset_ids.update(row)
            
            for entity in layout_data.entities:
                if entity.get('asset_id'):
                    unique_asset_ids.add(entity['asset_id'])
            
            # You could add more validation here
            
        except Exception as e:
            issues.append(f"Failed to parse map file: {e}")
        
        return issues
    
    def save_map_file(self, map_file_path: str, layout_data: LevelLayoutData,
                      legend_overrides: Optional[Dict[str, Any]] = None,
                      placements: Optional[List[Dict[str, Any]]] = None,
                      transition_metadata: Optional[Dict[Tuple[int, int], int]] = None) -> None:
        """
        Save a map file with the given layout data.

        Args:
            map_file_path: Path where to save the .map file
            layout_data: The level layout data to save
            legend_overrides: Optional legend overrides for this level
            placements: Optional unique entity placements
            transition_metadata: Optional mapping from position to transition number
        """
        try:
            # Prepare the YAML header
            yaml_content = {}

            # Add legend overrides if provided
            if legend_overrides:
                yaml_content['legend'] = legend_overrides

            # Add placements if provided
            if placements:
                yaml_content['placements'] = placements

            # Add metadata
            if layout_data.metadata:
                yaml_content['metadata'] = layout_data.metadata

            # Convert layout data back to ASCII grid
            ascii_grid = self._layout_data_to_ascii(layout_data, transition_metadata)

            # Write the file
            with open(map_file_path, 'w') as f:
                # Write YAML header if there's any content
                if yaml_content:
                    import yaml
                    f.write(yaml.dump(yaml_content, default_flow_style=False))
                    f.write('\n---\n')

                # Write ASCII grid
                for row in ascii_grid:
                    f.write(row + '\n')

            self.logger.info(f"Successfully saved map file: {map_file_path}")

        except Exception as e:
            raise ValueError(f"Error saving map file {map_file_path}: {e}") from e

    def _layout_data_to_ascii(self, layout_data: LevelLayoutData, 
                             transition_metadata: Optional[Dict[Tuple[int, int], int]] = None) -> List[str]:
        """
        Convert layout data back to ASCII grid representation.

        Args:
            layout_data: The level layout data to convert
            transition_metadata: Optional mapping from position to transition number

        Returns:
            List of strings representing the ASCII grid
        """
        # Create a grid filled with default floor tiles
        grid = [['.' for _ in range(layout_data.width)] for _ in range(layout_data.height)]

        # First, place tiles
        for y in range(layout_data.height):
            for x in range(layout_data.width):
                if y < len(layout_data.tiles) and x < len(layout_data.tiles[y]):
                    asset_id = layout_data.tiles[y][x]
                    
                    # Special handling for transition tiles (any exit tile type)
                    if asset_id and asset_id.startswith("tile.exit.") and transition_metadata:
                        transition_number = transition_metadata.get((x, y))
                        if transition_number is not None:
                            # Find the specific transition symbol for this number
                            symbol = self._find_symbol_for_transition(transition_number)
                            if symbol:
                                grid[y][x] = symbol
                                continue
                    
                    # Standard symbol lookup for non-transition tiles
                    symbol = self._find_symbol_for_asset(asset_id)
                    if symbol:
                        grid[y][x] = symbol

        # Then, place entities (they override tiles)
        for entity in layout_data.entities:
            # Handle both position formats: {'x': x, 'y': y} and {'position': {'x': x, 'y': y}}
            if 'position' in entity:
                x = int(entity['position']['x'] // self._get_tile_size())
                y = int(entity['position']['y'] // self._get_tile_size())
            else:
                x = int(entity.get('x', 0))
                y = int(entity.get('y', 0))

            if 0 <= x < layout_data.width and 0 <= y < layout_data.height:
                # Find the symbol for this entity
                symbol = self._find_symbol_for_entity(entity)
                if symbol:
                    grid[y][x] = symbol

        # Convert grid to strings
        return [''.join(row) for row in grid]

    def _find_symbol_for_asset(self, asset_id: str) -> Optional[str]:
        """Find the symbol that corresponds to an asset ID."""
        for symbol, definition in self.base_legend.items():
            if definition.get('asset_id') == asset_id:
                return symbol
        return None
    
    def _find_symbol_for_transition(self, transition_number: int) -> Optional[str]:
        """Find the symbol that corresponds to a specific transition number."""
        target_symbol = str(transition_number)
        if target_symbol in self.base_legend:
            definition = self.base_legend[target_symbol]
            if (definition.get('asset_id') == "tile.exit.portal" and 
                definition.get('properties', {}).get('exit_number') == transition_number):
                return target_symbol
        return None

    def _find_symbol_for_entity(self, entity: Dict[str, Any]) -> Optional[str]:
        """Find the symbol that corresponds to an entity."""
        entity_type = entity.get('type')
        data_id = entity.get('data_id')

        for symbol, definition in self.base_legend.items():
            if (definition.get('category') == 'entity' and
                definition.get('type') == entity_type and
                definition.get('data_id') == data_id):
                return symbol
        return None

    def _determine_entity_tile(self, entity_x: int, entity_y: int, ascii_lines: List[str], merged_legend: Dict[str, Any]) -> str:
        """
        Determine what tile an entity should be standing on by examining neighboring tiles.

        Args:
            entity_x: X coordinate of the entity
            entity_y: Y coordinate of the entity
            ascii_lines: The ASCII map lines
            merged_legend: The merged legend dictionary

        Returns:
            Asset ID of the tile the entity should be standing on
        """
        # Collect neighboring tile asset IDs (passable tiles only)
        passable_neighbors = []

        # Check all 8 directions around the entity
        directions = [
            (-1, -1), (-1, 0), (-1, 1),  # Top row
            (0, -1),           (0, 1),   # Middle row (skip center)
            (1, -1),  (1, 0),  (1, 1)    # Bottom row
        ]

        for dx, dy in directions:
            neighbor_x = entity_x + dx
            neighbor_y = entity_y + dy

            # Check bounds
            if (0 <= neighbor_y < len(ascii_lines) and
                0 <= neighbor_x < len(ascii_lines[neighbor_y])):

                neighbor_char = ascii_lines[neighbor_y][neighbor_x]
                neighbor_def = merged_legend.get(neighbor_char, {})

                # Only consider tile neighbors (not other entities)
                if neighbor_def.get('category') == 'tile':
                    # Only consider passable tiles
                    if not neighbor_def.get('solid', False):
                        asset_id = neighbor_def.get('asset_id', 'tile.floor.dirt')
                        passable_neighbors.append(asset_id)

        # If we found passable neighbors, pick one randomly
        if passable_neighbors:
            import random
            return random.choice(passable_neighbors)

        # Fallback to dirt tile if no passable neighbors found
        floor_tile = merged_legend.get('.', {})
        return floor_tile.get('asset_id', 'tile.floor.dirt')

    def _get_tile_size(self) -> int:
        """Get the tile size from config."""
        from src.game_core.config import get_config
        return get_config().rendering.tile_size

    def get_base_legend_info(self) -> Dict[str, Any]:
        """Get information about the loaded base legend."""
        symbol_counts = {}
        for symbol, definition in self.base_legend.items():
            category = definition.get('category', 'unknown')
            symbol_counts[category] = symbol_counts.get(category, 0) + 1

        return {
            "total_symbols": len(self.base_legend),
            "symbols_by_category": symbol_counts,
            "available_symbols": list(self.base_legend.keys())
        }
