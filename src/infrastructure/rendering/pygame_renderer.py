"""
Basic Pygame Renderer Implementation

This module provides a concrete implementation of the IRenderer interface using Pygame.
"""

import pygame
import math
from typing import <PERSON><PERSON>, Dict, Any, Union, Optional
from src.application.interfaces import IRenderer, GameStateData, IAssetManager
from src.game_core import Position, Vector2, AnimationTransform, WeaponAnimationTransform, CombinedAnimationTransform
from src.game_core.config import get_config
from src.infrastructure.logging import get_logger


class PygameRenderer(IRenderer):
    """
    Concrete implementation of IRenderer using Pygame.
    
    This renderer handles all visual presentation of the game state.
    """
    
    def __init__(self, asset_manager: IAssetManager, screen_width: int = None, screen_height: int = None, fullscreen: bool = False):
        """
        Initialize the Pygame renderer.
        
        Args:
            asset_manager: Asset manager for loading sprites
            screen_width: Width of the game window (0 for auto-detect)
            screen_height: Height of the game window (0 for auto-detect)
            fullscreen: Whether to run in fullscreen mode
        """
        self.asset_manager = asset_manager
        self.fullscreen = fullscreen
        self.logger = get_logger(__name__)
        
        # Get configuration
        self.config = get_config()
        
        # Use provided dimensions or config defaults
        if screen_width is None:
            screen_width = self.config.rendering.screen_width
        if screen_height is None:
            screen_height = self.config.rendering.screen_height
        
        # Get screen resolution
        if screen_width == 0 or screen_height == 0 or fullscreen:
            # Get the display resolution
            info = pygame.display.Info()
            self.screen_width = info.current_w
            self.screen_height = info.current_h
        else:
            self.screen_width = screen_width
            self.screen_height = screen_height
        
        # Use configured tile size
        self.tile_size = self.config.rendering.tile_size
        
        # Initialize Pygame display
        if fullscreen:
            self.screen = pygame.display.set_mode((self.screen_width, self.screen_height), pygame.FULLSCREEN)
        else:
            self.screen = pygame.display.set_mode((self.screen_width, self.screen_height))
        pygame.display.set_caption("Treebeard's Revenge")
        
        # Camera offset for scrolling
        self.camera_x = 0.0
        self.camera_y = 0.0
        
        # Colors
        self.background_color = (0, 0, 0)  # Black
        self.ui_color = (50, 50, 50)       # Dark gray
        self.text_color = (255, 255, 255)  # White
        
        # Initialize font for text rendering
        pygame.font.init()
        self.font = pygame.font.Font(None, 24)
        self.small_font = pygame.font.Font(None, 16)

    def _apply_animation_transform(self, surface: pygame.Surface, transform: AnimationTransform) -> Tuple[pygame.Surface, Tuple[int, int]]:
        """
        Apply animation transform to a surface.

        Args:
            surface: Original surface to transform
            transform: Animation transform to apply

        Returns:
            Tuple of (transformed_surface, offset_adjustment)
        """
        if transform.rotation == 0.0 and transform.offset_x == 0.0 and transform.offset_y == 0.0:
            # No transformation needed
            return surface, (0, 0)

        transformed_surface = surface
        offset_adjustment = (0, 0)

        # Apply rotation if needed
        if transform.rotation != 0.0:
            # Rotate the surface
            transformed_surface = pygame.transform.rotate(surface, transform.rotation)

            # Calculate offset adjustment due to rotation
            # When a surface is rotated, its size changes and we need to adjust positioning
            original_rect = surface.get_rect()
            rotated_rect = transformed_surface.get_rect()

            # Center the rotated surface on the original position
            offset_x = (original_rect.width - rotated_rect.width) // 2
            offset_y = (original_rect.height - rotated_rect.height) // 2
            offset_adjustment = (offset_x, offset_y)

        # Apply position offset from animation (vibration, etc.)
        final_offset_x = offset_adjustment[0] + int(transform.offset_x)
        final_offset_y = offset_adjustment[1] + int(transform.offset_y)

        return transformed_surface, (final_offset_x, final_offset_y)

    def _apply_weapon_animation_transform(self, surface: pygame.Surface, transform: WeaponAnimationTransform) -> Tuple[pygame.Surface, Tuple[int, int]]:
        """
        Apply weapon animation transform to a surface.

        Args:
            surface: Original weapon surface to transform
            transform: Weapon animation transform to apply

        Returns:
            Tuple of (transformed_surface, offset_adjustment)
        """
        if (transform.rotation == 0.0 and transform.offset_x == 0.0 and
            transform.offset_y == 0.0):
            # No transformation needed
            return surface, (0, 0)

        transformed_surface = surface
        offset_adjustment = (0, 0)

        # Apply rotation if needed
        if transform.rotation != 0.0:
            # For weapons, we need to rotate around the hilt/handle (pivot point)
            original_rect = surface.get_rect()

            # Calculate pivot point in pixels (where the weapon should rotate from)
            pivot_x = int(original_rect.width * transform.pivot_x)
            pivot_y = int(original_rect.height * transform.pivot_y)

            # Create a much larger surface to accommodate rotation without clipping
            max_dim = max(original_rect.width, original_rect.height) * 3
            large_surface = pygame.Surface((max_dim, max_dim), pygame.SRCALPHA)

            # Position the original surface so that the pivot point is at the center of the large surface
            # This way, when we rotate the large surface, it rotates around the weapon's pivot point
            weapon_x = max_dim // 2 - pivot_x
            weapon_y = max_dim // 2 - pivot_y
            large_surface.blit(surface, (weapon_x, weapon_y))

            # Rotate the large surface around its center (which is now the weapon's pivot point)
            rotated_large = pygame.transform.rotate(large_surface, -transform.rotation)  # Negative for correct direction

            # Calculate offset to position the rotated weapon correctly
            rotated_rect = rotated_large.get_rect()
            large_rect = large_surface.get_rect()

            # The offset needs to account for:
            # 1. The change in size due to rotation
            # 2. Moving the pivot point back to where it should be relative to the original position
            center_offset_x = (large_rect.width - rotated_rect.width) // 2
            center_offset_y = (large_rect.height - rotated_rect.height) // 2

            # Adjust so the pivot point ends up at the original position
            pivot_offset_x = (max_dim // 2) - pivot_x
            pivot_offset_y = (max_dim // 2) - pivot_y

            transformed_surface = rotated_large
            offset_adjustment = (center_offset_x - pivot_offset_x, center_offset_y - pivot_offset_y)

        # Apply position offset from animation
        final_offset_x = offset_adjustment[0] + int(transform.offset_x)
        final_offset_y = offset_adjustment[1] + int(transform.offset_y)

        return transformed_surface, (final_offset_x, final_offset_y)
    
    def render_frame(self, game_state: GameStateData, player_animation_transform: Optional[CombinedAnimationTransform] = None) -> None:
        """
        Render a complete frame of the game.

        Args:
            game_state: Current game state to render
            player_animation_transform: Optional combined animation transform for player and weapon
        """
        # Clear screen
        self.screen.fill(self.background_color)

        if game_state.current_level_id:
            # Update camera to follow player
            if game_state.player:
                self._update_camera(game_state.player.position)

            # Render level tiles
            self._render_tiles(game_state)

            # Render entities
            self._render_entities(game_state, player_animation_transform)

            # Render UI
            self._render_ui(game_state)

            # Render debug overlays
            self._render_debug_overlays(game_state)
        else:
            # No level loaded - show loading screen
            self._render_loading_screen()

        # Note: Display update moved to game engine after UI rendering
    
    def get_screen_size(self) -> Tuple[int, int]:
        """Get the current screen size."""
        return (self.screen_width, self.screen_height)
    
    def toggle_fullscreen(self) -> None:
        """Toggle between fullscreen and windowed mode."""
        self.fullscreen = not self.fullscreen
        
        if self.fullscreen:
            # Get current display resolution
            info = pygame.display.Info()
            self.screen_width = info.current_w
            self.screen_height = info.current_h
            self.screen = pygame.display.set_mode((self.screen_width, self.screen_height), pygame.FULLSCREEN)
        else:
            # Switch to windowed mode with configured size
            self.screen_width = self.config.rendering.screen_width
            self.screen_height = self.config.rendering.screen_height
            self.screen = pygame.display.set_mode((self.screen_width, self.screen_height))
    
    def world_to_screen(self, world_pos: Union[Position, Vector2, Tuple[float, float]]) -> Tuple[int, int]:
        """
        Convert world coordinates to screen coordinates.
        
        Args:
            world_pos: Position in world coordinates (pixels)
            
        Returns:
            Screen coordinates as (x, y) tuple
        """
        if isinstance(world_pos, tuple):
            x, y = world_pos
        else:
            x, y = world_pos.x, world_pos.y
            
        screen_x = x - self.camera_x
        screen_y = y - self.camera_y
        return (int(screen_x), int(screen_y))
    
    def screen_to_world(self, screen_pos: Tuple[int, int]) -> Position:
        """
        Convert screen coordinates to world coordinates.
        
        Args:
            screen_pos: Screen coordinates as (x, y) tuple
            
        Returns:
            Position in world coordinates
        """
        world_x = (screen_pos[0] + self.camera_x) // self.tile_size
        world_y = (screen_pos[1] + self.camera_y) // self.tile_size
        return Position(int(world_x), int(world_y))
    
    def _update_camera(self, player_pos: Position) -> None:
        """Update camera position to follow the player."""
        # Center camera on player's position (convert from pixels to screen space)
        target_camera_x = player_pos.x - (self.screen_width // 2)
        target_camera_y = player_pos.y - (self.screen_height // 2)
        
        # Smooth camera movement (lerp) - less aggressive
        lerp_factor = 0.15
        self.camera_x += (target_camera_x - self.camera_x) * lerp_factor
        self.camera_y += (target_camera_y - self.camera_y) * lerp_factor
        
        # Ensure camera doesn't go too far negative
        self.camera_x = max(-self.tile_size * 2, self.camera_x)
        self.camera_y = max(-self.tile_size * 2, self.camera_y)
    
    def _render_tiles(self, game_state: GameStateData) -> None:
        """Render the level tiles."""
        if not hasattr(game_state, 'level_tiles') or not game_state.level_tiles:
            self.logger.debug("No level tiles found!")
            return
        
        # Calculate visible tile range using integer camera positions
        cam_x = int(self.camera_x)
        cam_y = int(self.camera_y)
        
        # Calculate visible tile bounds with safety margins
        start_x = max(0, (cam_x // self.tile_size) - 2)
        start_y = max(0, (cam_y // self.tile_size) - 2)
        end_x = min(len(game_state.level_tiles[0]) if game_state.level_tiles else 0, 
                   start_x + (self.screen_width // self.tile_size) + 4)
        end_y = min(len(game_state.level_tiles) if game_state.level_tiles else 0,
                   start_y + (self.screen_height // self.tile_size) + 4)
        
        # Ensure we don't go negative
        start_x = max(0, start_x)
        start_y = max(0, start_y)
        
        tiles_rendered = 0
        
        # Render visible tiles
        for y in range(start_y, end_y):
            for x in range(start_x, end_x):
                if (0 <= y < len(game_state.level_tiles) and 
                    0 <= x < len(game_state.level_tiles[y])):
                    
                    asset_id = game_state.level_tiles[y][x]

                    # Check for dynamic tile state that might change the asset ID
                    tile_key = f"{x},{y}"
                    if (hasattr(game_state, 'tile_states') and
                        tile_key in game_state.tile_states):
                        tile_state = game_state.tile_states[tile_key]
                        if tile_state.get('can_interact', False):
                            if tile_state.get('is_open', False):
                                # Use open asset ID if available
                                if asset_id == "tile.door.wooden":
                                    asset_id = "tile.door.wooden.open"

                    if asset_id:  # Only render if there's an asset ID
                        tiles_rendered += 1
                        try:
                            tile_surface = self.asset_manager.get_asset(asset_id, (self.tile_size, self.tile_size))
                            # Convert tile coordinates to pixel coordinates for rendering
                            screen_pos = self.world_to_screen((x * self.tile_size, y * self.tile_size))
                            # Only render if the tile is actually visible on screen
                            if (-self.tile_size <= screen_pos[0] <= self.screen_width and 
                                -self.tile_size <= screen_pos[1] <= self.screen_height):
                                self.screen.blit(tile_surface, screen_pos)
                        except RuntimeError as e:
                            self.logger.error(f"Failed to generate tile '{asset_id}' at ({x},{y}): {e}")
                            # Fallback to colored rectangle
                            screen_pos = self.world_to_screen((x * self.tile_size, y * self.tile_size))
                            if (-self.tile_size <= screen_pos[0] <= self.screen_width and 
                                -self.tile_size <= screen_pos[1] <= self.screen_height):
                                # Draw red rectangle for generation errors
                                pygame.draw.rect(self.screen, (255, 0, 0), 
                                               (*screen_pos, self.tile_size, self.tile_size))
                        except KeyError as e:
                            self.logger.warning(f"Unregistered tile asset '{asset_id}' at ({x},{y}): {e}")
                            # Fallback to colored rectangle
                            screen_pos = self.world_to_screen((x * self.tile_size, y * self.tile_size))
                            if (-self.tile_size <= screen_pos[0] <= self.screen_width and 
                                -self.tile_size <= screen_pos[1] <= self.screen_height):
                                # Draw yellow rectangle for unregistered assets
                                pygame.draw.rect(self.screen, (255, 255, 0), 
                                               (*screen_pos, self.tile_size, self.tile_size))
                        except Exception as e:
                            self.logger.error(f"Unexpected error with tile '{asset_id}' at ({x},{y}): {e}")
                            # Fallback to colored rectangle
                            screen_pos = self.world_to_screen((x * self.tile_size, y * self.tile_size))
                            if (-self.tile_size <= screen_pos[0] <= self.screen_width and 
                                -self.tile_size <= screen_pos[1] <= self.screen_height):
                                # Draw magenta rectangle for unexpected errors
                                pygame.draw.rect(self.screen, (255, 0, 255), 
                                               (*screen_pos, self.tile_size, self.tile_size))
        
        # Debug: Log render stats occasionally
        # if tiles_rendered > 0 and int(self.camera_x) % 32 == 0:
            # self.logger.debug(f"Rendered {tiles_rendered} tiles, camera at ({cam_x}, {cam_y})")
    
    def _render_entities(self, game_state: GameStateData, player_animation_transform: Optional[CombinedAnimationTransform] = None) -> None:
        """Render all entities (player, monsters, items)."""
        
        # Render items first (so they appear under other entities)
        for item in game_state.items.values():
            screen_pos = self.world_to_screen(item.position)
            try:
                item_surface = self.asset_manager.get_asset(item.asset_id)
                self.screen.blit(item_surface, screen_pos)
            except Exception:
                # Fallback
                pygame.draw.circle(self.screen, (255, 255, 0), 
                                 (screen_pos[0] + self.tile_size//2, screen_pos[1] + self.tile_size//2), 8)
        
        # Render monsters
        for monster in game_state.monsters.values():
            screen_pos = self.world_to_screen(monster.position)
            try:
                monster_surface = self.asset_manager.get_asset(monster.asset_id, (self.tile_size, self.tile_size))
                # Monster position is at center of tile, but blit() renders from top-left
                # Adjust to render sprite from top-left corner of the tile (same as NPCs)
                render_x = screen_pos[0] - self.tile_size // 2
                render_y = screen_pos[1] - self.tile_size // 2
                self.screen.blit(monster_surface, (render_x, render_y))

                # Render health bar above monster (use original screen_pos for centering)
                self._render_health_bar(screen_pos, monster.stats.hp, monster.stats.max_hp)

            except Exception:
                # Fallback
                pygame.draw.circle(self.screen, (255, 0, 0),
                                 (screen_pos[0], screen_pos[1]), 12)

        # Render NPCs
        for npc in game_state.npcs.values():
            screen_pos = self.world_to_screen(npc.position)
            try:
                npc_surface = self.asset_manager.get_asset(npc.asset_id, (self.tile_size, self.tile_size))
                # NPC position is at center of tile, but blit() renders from top-left
                # Adjust to render sprite from top-left corner of the tile
                render_x = screen_pos[0] - self.tile_size // 2
                render_y = screen_pos[1] - self.tile_size // 2
                self.screen.blit(npc_surface, (render_x, render_y))
            except Exception:
                # Fallback
                pygame.draw.circle(self.screen, (0, 255, 0),
                                 (screen_pos[0], screen_pos[1]), 12)

        # Render player last (on top)
        if game_state.player:
            self._render_player_with_weapon_animation(game_state.player, player_animation_transform)

            # Debug: Show player center and attack direction
            if self.config.show_weapon_direction:
                self._render_debug_attack_direction(game_state.player)

            # Debug: Show attack arc
            if self.config.show_attack_arc:
                self._render_debug_attack_arc(game_state.player)

        # Debug: Show entity hitboxes
        if self.config.show_entity_hitboxes:
            self._render_debug_entity_hitboxes(game_state)
    
    def _render_health_bar(self, screen_pos: Tuple[int, int], current_hp: int, max_hp: int) -> None:
        """Render a health bar above an entity."""
        if current_hp >= max_hp:
            return  # Don't show full health bars
        
        bar_width = 24
        bar_height = 4
        bar_x = screen_pos[0] + (self.tile_size - bar_width) // 2
        bar_y = screen_pos[1] - 8
        
        # Background (red)
        pygame.draw.rect(self.screen, (100, 0, 0), (bar_x, bar_y, bar_width, bar_height))
        
        # Health (green)
        health_width = int((current_hp / max_hp) * bar_width)
        if health_width > 0:
            pygame.draw.rect(self.screen, (0, 200, 0), (bar_x, bar_y, health_width, bar_height))

        # Border (black)
        pygame.draw.rect(self.screen, (0, 0, 0), (bar_x - 1, bar_y - 1, bar_width + 2, bar_height + 2), 1)

    def _render_player_with_weapon_animation(self, player, animation_transform: Optional[CombinedAnimationTransform] = None) -> None:
        """
        Render player with separate weapon animation support.

        Args:
            player: Player entity to render
            animation_transform: Optional combined animation transform for player and weapon
        """
        screen_pos = self.world_to_screen(player.position)
        render_x = screen_pos[0] - self.tile_size // 2
        render_y = screen_pos[1] - self.tile_size // 2

        try:
            # Get player equipment
            equipment_ids = {
                "head_equipment": player.head_equipment,
                "chest_equipment": player.chest_equipment,
                "legs_equipment": player.legs_equipment,
                "boots_equipment": player.boots_equipment,
                "main_hand_weapon": None,  # Exclude weapon from body rendering
                "off_hand_equipment": player.off_hand_equipment
            }

            # Render player body (without weapon)
            player_body_surface = self.asset_manager.get_equipped_player_asset(
                equipment_ids, (self.tile_size, self.tile_size)
            )

            # Apply player body animation transform
            if animation_transform and animation_transform.player_transform:
                transformed_body, body_offset = self._apply_animation_transform(
                    player_body_surface, animation_transform.player_transform
                )
                body_render_x = render_x + body_offset[0]
                body_render_y = render_y + body_offset[1]
                self.screen.blit(transformed_body, (body_render_x, body_render_y))
            else:
                self.screen.blit(player_body_surface, (render_x, render_y))

            # Render weapon on top if equipped
            if player.main_hand_weapon:
                self._render_weapon_separately(
                    player.main_hand_weapon,
                    render_x, render_y,
                    animation_transform.weapon_transform if animation_transform else None
                )



        except Exception as e:
            self.logger.error(f"Error rendering player with weapon animation: {e}")
            # Fallback to simple rendering
            try:
                player_surface = self.asset_manager.get_asset(player.asset_id, (self.tile_size, self.tile_size))

                if animation_transform and animation_transform.player_transform:
                    transformed_surface, offset_adjustment = self._apply_animation_transform(
                        player_surface, animation_transform.player_transform
                    )
                    final_render_x = render_x + offset_adjustment[0]
                    final_render_y = render_y + offset_adjustment[1]
                    self.screen.blit(transformed_surface, (final_render_x, final_render_y))
                else:
                    self.screen.blit(player_surface, (render_x, render_y))
            except Exception:
                # Final fallback - simple circle
                pygame.draw.circle(self.screen, (255, 0, 0), screen_pos, self.tile_size // 4)

    def _render_weapon_separately(self, weapon_id: str, base_x: int, base_y: int,
                                weapon_transform: Optional[WeaponAnimationTransform] = None) -> None:
        """
        Render a weapon separately with its own animation transform.

        Args:
            weapon_id: ID of the weapon to render
            base_x: Base X position for weapon rendering (player's top-left)
            base_y: Base Y position for weapon rendering (player's top-left)
            weapon_transform: Optional weapon animation transform
        """
        try:
            # Get weapon definition
            from src.game_data import get_item_definition
            weapon_def = get_item_definition(weapon_id)
            if not weapon_def:
                self.logger.warning(f"No weapon definition found for {weapon_id}")
                return

            # Create a surface to draw the weapon on
            weapon_surface = pygame.Surface((self.tile_size, self.tile_size), pygame.SRCALPHA)

            # Get weapon properties
            props = weapon_def.properties
            weapon_type = props.get("weapon_type", "sword")
            visual_style = props.get("visual_style", "basic")

            # Get colors from properties or use defaults
            color_primary = tuple(props.get("color_primary", [139, 69, 19]))  # Brown
            color_secondary = tuple(props.get("color_secondary", [192, 192, 192]))  # Silver

            # Render weapon directly on the surface using the same logic as equipment rendering
            self._render_weapon_on_surface(weapon_surface, weapon_type, visual_style,
                                         color_primary, color_secondary, (self.tile_size, self.tile_size))

            # Apply weapon animation transform
            if weapon_transform:
                transformed_weapon, weapon_offset = self._apply_weapon_animation_transform(
                    weapon_surface, weapon_transform
                )
                weapon_render_x = base_x + weapon_offset[0]
                weapon_render_y = base_y + weapon_offset[1]
                self.screen.blit(transformed_weapon, (weapon_render_x, weapon_render_y))
            else:
                # Render weapon at base position
                self.screen.blit(weapon_surface, (base_x, base_y))

        except Exception as e:
            self.logger.error(f"Error rendering weapon {weapon_id}: {e}")
            # Don't render weapon if there's an error

    def _render_weapon_on_surface(self, surface: pygame.Surface, weapon_type: str, visual_style: str,
                                color_primary: tuple, color_secondary: tuple, size: tuple) -> None:
        """
        Render weapon directly on a surface using the same logic as equipment rendering.

        Args:
            surface: Surface to draw on
            weapon_type: Type of weapon (sword, bow, etc.)
            visual_style: Visual style of the weapon
            color_primary: Primary color
            color_secondary: Secondary color
            size: Size of the surface
        """
        s_w, s_h = size

        if weapon_type == "sword":
            # Position sword so handle is in player's right hand (around 70% right, 60% down)
            # Sword blade (vertical, pointing up)
            blade_rect = pygame.Rect(s_w*0.68, s_h*0.15, s_w*0.08, s_h*0.45)
            pygame.draw.rect(surface, color_secondary, blade_rect)

            # Cross guard (at the transition between blade and handle)
            guard_rect = pygame.Rect(s_w*0.65, s_h*0.58, s_w*0.14, s_h*0.03)
            pygame.draw.rect(surface, color_primary, guard_rect)

            # Handle (positioned where player's hand would be - this is the pivot point)
            handle_rect = pygame.Rect(s_w*0.69, s_h*0.61, s_w*0.06, s_h*0.2)
            pygame.draw.rect(surface, color_primary, handle_rect)

            # Add a bright outline to make it more visible
            pygame.draw.rect(surface, (255, 255, 255), blade_rect, 1)
            pygame.draw.rect(surface, (255, 255, 255), guard_rect, 1)
            pygame.draw.rect(surface, (255, 255, 255), handle_rect, 1)

        elif weapon_type == "bow":
            # Bow held in left hand (around 25% from left, 60% down for grip)
            bow_rect = pygame.Rect(s_w*0.22, s_h*0.2, s_w*0.04, s_h*0.6)
            pygame.draw.rect(surface, color_primary, bow_rect)
            # Bowstring
            string_rect = pygame.Rect(s_w*0.27, s_h*0.22, s_w*0.01, s_h*0.56)
            pygame.draw.rect(surface, color_secondary, string_rect)
            # Grip area (where pivot point will be - in left hand)
            grip_rect = pygame.Rect(s_w*0.21, s_h*0.55, s_w*0.06, s_h*0.08)
            pygame.draw.rect(surface, (139, 69, 19), grip_rect)  # Brown grip

        elif weapon_type == "axe":
            # Double crescent battle axe held in right hand - rotated 90 degrees (vertical orientation)
            # Handle positioned to align with player's hand grip (around 45% down) and extend downward
            handle_rect = pygame.Rect(s_w*0.73, s_h*0.45, s_w*0.06, s_h*0.35)
            pygame.draw.rect(surface, color_secondary, handle_rect)  # Wood handle (brown)
            
            # Create a complete vertical axe head shape (rotated 90 degrees)
            # Axe head positioned above the handle grip point, from 15% to 45% height
            axe_head_points = [
                # Left crescent (top side when rotated)
                (s_w*0.66, s_h*0.15),           # Left top tip  
                (s_w*0.70, s_h*0.20),           # Left top curve
                (s_w*0.72, s_h*0.30),           # Left top base
                (s_w*0.73, s_h*0.40),           # Connection to handle (top left)
                
                # Connect to right side
                (s_w*0.79, s_h*0.40),           # Connection to handle (top right)
                (s_w*0.80, s_h*0.30),           # Right top base
                (s_w*0.82, s_h*0.20),           # Right top curve
                (s_w*0.86, s_h*0.15),           # Right top tip
                
                # Right crescent (bottom side when rotated)
                (s_w*0.86, s_h*0.50),           # Right bottom tip
                (s_w*0.82, s_h*0.45),           # Right bottom curve
                (s_w*0.80, s_h*0.42),           # Right bottom base
                (s_w*0.79, s_h*0.40),           # Connection to handle (bottom right)
                
                # Connect to left side
                (s_w*0.73, s_h*0.40),           # Connection to handle (bottom left)
                (s_w*0.72, s_h*0.42),           # Left bottom base
                (s_w*0.70, s_h*0.45),           # Left bottom curve
                (s_w*0.66, s_h*0.50),           # Left bottom tip
            ]
            pygame.draw.polygon(surface, color_primary, axe_head_points)  # Iron axe head (gray)
            
            # Add wood grain lines on the handle for detail
            dark_wood_color = (101, 67, 33)  # Dark wood for grain
            for i in range(7):
                y = s_h*0.48 + i * s_h*0.04
                pygame.draw.line(surface, dark_wood_color,
                    (s_w*0.74, y), (s_w*0.78, y), 1)

        elif weapon_type == "dagger":
            # Dagger in right hand, smaller than sword
            blade_rect = pygame.Rect(s_w*0.69, s_h*0.35, s_w*0.06, s_h*0.25)
            pygame.draw.rect(surface, color_secondary, blade_rect)
            # Cross guard
            guard_rect = pygame.Rect(s_w*0.67, s_h*0.58, s_w*0.1, s_h*0.02)
            pygame.draw.rect(surface, color_primary, guard_rect)
            # Handle in hand position
            handle_rect = pygame.Rect(s_w*0.69, s_h*0.6, s_w*0.06, s_h*0.15)
            pygame.draw.rect(surface, color_primary, handle_rect)

        # Fallback: if no weapon was drawn, draw a bright rectangle to show something is there
        else:
            fallback_rect = pygame.Rect(s_w*0.6, s_h*0.2, s_w*0.3, s_h*0.6)
            pygame.draw.rect(surface, (255, 0, 255), fallback_rect)  # Bright magenta
            pygame.draw.rect(surface, (255, 255, 255), fallback_rect, 2)  # White border

    def _render_debug_attack_direction(self, player) -> None:
        """
        Render debug visualizations for attack direction calculation.

        Args:
            player: Player entity to debug
        """
        import math

        # Convert player position to screen coordinates
        player_screen_center = self.world_to_screen(player.position)

        # Debug circle at player center (where we think the player is)
        pygame.draw.circle(self.screen, (255, 0, 0), player_screen_center, 8, 2)  # Red circle

        # Get current mouse position
        mouse_pos = pygame.mouse.get_pos()

        # Calculate direction vector manually (simpler approach)
        dx = mouse_pos[0] - player_screen_center[0]
        dy = mouse_pos[1] - player_screen_center[1]

        # Normalize the direction vector
        distance = math.sqrt(dx * dx + dy * dy)
        if distance > 0:
            direction_x = dx / distance
            direction_y = dy / distance
        else:
            direction_x = 1.0  # Default to right
            direction_y = 0.0

        # Draw arrow showing attack direction
        arrow_length = 80
        arrow_end_x = player_screen_center[0] + direction_x * arrow_length
        arrow_end_y = player_screen_center[1] + direction_y * arrow_length

        # Main arrow line (thick and bright)
        pygame.draw.line(self.screen, (0, 255, 0), player_screen_center,
                       (int(arrow_end_x), int(arrow_end_y)), 4)  # Green arrow

        # Arrow head
        angle = math.atan2(direction_y, direction_x)
        arrow_head_length = 20
        arrow_head_angle = 0.5  # radians

        # Left arrow head line
        head_x1 = arrow_end_x - arrow_head_length * math.cos(angle - arrow_head_angle)
        head_y1 = arrow_end_y - arrow_head_length * math.sin(angle - arrow_head_angle)
        pygame.draw.line(self.screen, (0, 255, 0), (int(arrow_end_x), int(arrow_end_y)),
                       (int(head_x1), int(head_y1)), 4)

        # Right arrow head line
        head_x2 = arrow_end_x - arrow_head_length * math.cos(angle + arrow_head_angle)
        head_y2 = arrow_end_y - arrow_head_length * math.sin(angle + arrow_head_angle)
        pygame.draw.line(self.screen, (0, 255, 0), (int(arrow_end_x), int(arrow_end_y)),
                       (int(head_x2), int(head_y2)), 4)

        # Draw a line from player to mouse for reference
        pygame.draw.line(self.screen, (255, 255, 0), player_screen_center, mouse_pos, 1)  # Yellow line

        # Debug text showing values
        direction_text = f"Dir: ({direction_x:.2f}, {direction_y:.2f})"
        mouse_text = f"Mouse: {mouse_pos}"
        player_text = f"Player: {player_screen_center}"
        distance_text = f"Distance: {distance:.1f}"

        # Create text surfaces with background for better visibility
        text_bg_color = (0, 0, 0, 180)  # Semi-transparent black

        texts = [direction_text, mouse_text, player_text, distance_text]
        for i, text in enumerate(texts):
            text_surface = self.small_font.render(text, True, (255, 255, 255))
            text_rect = text_surface.get_rect()
            text_rect.topleft = (10, 10 + i * 20)

            # Draw background rectangle
            bg_rect = text_rect.inflate(4, 2)
            bg_surface = pygame.Surface(bg_rect.size, pygame.SRCALPHA)
            bg_surface.fill(text_bg_color)
            self.screen.blit(bg_surface, bg_rect.topleft)

            # Draw text
            self.screen.blit(text_surface, text_rect.topleft)

    def _render_debug_attack_arc(self, player) -> None:
        """
        Render debug visualization for the attack arc.

        Args:
            player: Player entity to debug
        """
        import math

        # Get player's weapon stats
        weapon_damage = 1
        weapon_range_tiles = 1.0
        damage_arc = 60.0

        if player.main_hand_weapon:
            try:
                from src.game_data import get_item_definition
                from src.game_core.game_logic import get_weapon_stats
                weapon_def = get_item_definition(player.main_hand_weapon)
                if weapon_def and weapon_def.properties:
                    weapon_damage, _, weapon_range_tiles, damage_arc = get_weapon_stats(weapon_def.properties)
            except ImportError:
                pass  # Use defaults

        # Convert weapon range from tiles to pixels
        tile_size = 128  # TODO: Get this from config
        weapon_range_pixels = weapon_range_tiles * tile_size

        # Convert player position to screen coordinates
        player_screen_center = self.world_to_screen(player.position)

        # Get current mouse position
        mouse_pos = pygame.mouse.get_pos()

        # Calculate direction vector
        dx = mouse_pos[0] - player_screen_center[0]
        dy = mouse_pos[1] - player_screen_center[1]

        # Normalize the direction vector
        distance = math.sqrt(dx * dx + dy * dy)
        if distance > 0:
            direction_x = dx / distance
            direction_y = dy / distance
        else:
            direction_x = 1.0  # Default to right
            direction_y = 0.0

        # Calculate attack angle
        attack_angle = math.atan2(direction_y, direction_x)
        half_arc = math.radians(damage_arc / 2.0)

        # Draw attack range circle (semi-transparent)
        range_surface = pygame.Surface((weapon_range_pixels * 2, weapon_range_pixels * 2), pygame.SRCALPHA)
        pygame.draw.circle(range_surface, (255, 255, 0, 50),
                          (int(weapon_range_pixels), int(weapon_range_pixels)),
                          int(weapon_range_pixels))

        # Position the range circle centered on player
        range_rect = range_surface.get_rect(center=player_screen_center)
        self.screen.blit(range_surface, range_rect.topleft)

        # Draw attack arc
        if damage_arc < 360:  # Don't draw arc lines for full circle
            # Calculate arc endpoints
            arc_start_angle = attack_angle - half_arc
            arc_end_angle = attack_angle + half_arc

            # Draw arc boundary lines
            arc_start_x = player_screen_center[0] + weapon_range_pixels * math.cos(arc_start_angle)
            arc_start_y = player_screen_center[1] + weapon_range_pixels * math.sin(arc_start_angle)
            arc_end_x = player_screen_center[0] + weapon_range_pixels * math.cos(arc_end_angle)
            arc_end_y = player_screen_center[1] + weapon_range_pixels * math.sin(arc_end_angle)

            # Draw arc boundary lines
            pygame.draw.line(self.screen, (255, 0, 255), player_screen_center,
                           (int(arc_start_x), int(arc_start_y)), 2)  # Magenta
            pygame.draw.line(self.screen, (255, 0, 255), player_screen_center,
                           (int(arc_end_x), int(arc_end_y)), 2)  # Magenta

        # Draw weapon stats text
        stats_text = f"Weapon: {weapon_damage} dmg, {weapon_range_tiles:.1f} tiles, {damage_arc}° arc"
        text_surface = self.small_font.render(stats_text, True, (255, 255, 255))
        text_rect = text_surface.get_rect()
        text_rect.topleft = (10, 100)  # Position below other debug text

        # Draw background rectangle
        bg_rect = text_rect.inflate(4, 2)
        bg_surface = pygame.Surface(bg_rect.size, pygame.SRCALPHA)
        bg_surface.fill((0, 0, 0, 180))  # Semi-transparent black
        self.screen.blit(bg_surface, bg_rect.topleft)

        # Draw text
        self.screen.blit(text_surface, text_rect.topleft)

    def _render_debug_entity_hitboxes(self, game_state: GameStateData) -> None:
        """
        Render debug hitboxes for all entities.

        Args:
            game_state: Current game state
        """
        # Define default entity sizes (in pixels)
        # TODO: These should be defined in the entity data or asset definitions
        DEFAULT_ENTITY_SIZE = (64, 64)  # Default size for monsters/NPCs

        # Draw player hitbox
        if game_state.player:
            player_screen_pos = self.world_to_screen(game_state.player.position)
            player_size = game_state.player.size
            player_rect = pygame.Rect(player_screen_pos[0], player_screen_pos[1],
                                    player_size[0], player_size[1])
            pygame.draw.rect(self.screen, (0, 255, 0), player_rect, 2)  # Green for player

            # Draw player center point
            center_x = player_screen_pos[0] + player_size[0] // 2
            center_y = player_screen_pos[1] + player_size[1] // 2
            pygame.draw.circle(self.screen, (0, 255, 0), (center_x, center_y), 3)

        # Draw monster hitboxes
        for monster_id, monster in game_state.monsters.items():
            monster_screen_pos = self.world_to_screen(monster.position)

            # For now, use default size for monsters since they don't have size attribute
            # TODO: Add size attribute to Monster class or get from asset data
            monster_size = DEFAULT_ENTITY_SIZE

            # Center the hitbox on the monster position
            hitbox_x = monster_screen_pos[0] - monster_size[0] // 2
            hitbox_y = monster_screen_pos[1] - monster_size[1] // 2

            monster_rect = pygame.Rect(hitbox_x, hitbox_y, monster_size[0], monster_size[1])
            pygame.draw.rect(self.screen, (255, 0, 0), monster_rect, 2)  # Red for monsters

            # Draw monster center point (their actual position)
            pygame.draw.circle(self.screen, (255, 0, 0), monster_screen_pos, 3)

            # Draw monster name above hitbox
            name_text = self.small_font.render(f"{monster.name} ({monster_id})", True, (255, 255, 255))
            name_rect = name_text.get_rect()
            name_rect.centerx = monster_screen_pos[0]
            name_rect.bottom = hitbox_y - 5

            # Draw background for text
            bg_rect = name_rect.inflate(4, 2)
            bg_surface = pygame.Surface(bg_rect.size, pygame.SRCALPHA)
            bg_surface.fill((0, 0, 0, 180))
            self.screen.blit(bg_surface, bg_rect.topleft)
            self.screen.blit(name_text, name_rect.topleft)

        # Draw NPC hitboxes
        for npc_id, npc in game_state.npcs.items():
            npc_screen_pos = self.world_to_screen(npc.position)

            # Use default size for NPCs
            npc_size = DEFAULT_ENTITY_SIZE

            # Center the hitbox on the NPC position
            hitbox_x = npc_screen_pos[0] - npc_size[0] // 2
            hitbox_y = npc_screen_pos[1] - npc_size[1] // 2

            npc_rect = pygame.Rect(hitbox_x, hitbox_y, npc_size[0], npc_size[1])
            pygame.draw.rect(self.screen, (0, 0, 255), npc_rect, 2)  # Blue for NPCs

            # Draw NPC center point
            pygame.draw.circle(self.screen, (0, 0, 255), npc_screen_pos, 3)

    def _render_ui(self, game_state: GameStateData) -> None:
        """Render the user interface with proper player stats widget overlaying the map."""
        if not game_state.player:
            return
        
        player = game_state.player
        
        # Player stats panel - 1.5x bigger with semi-transparent background
        panel_width = int(330 * 1.5)  # 495
        panel_height = int(170 * 1.5)  # 255
        panel_x = 15
        panel_y = 15
        
        # Semi-transparent panel background for overlay effect
        panel_surface = pygame.Surface((panel_width, panel_height), pygame.SRCALPHA)
        pygame.draw.rect(panel_surface, (*self.ui_color, 220), (0, 0, panel_width, panel_height))  # Semi-transparent
        pygame.draw.rect(panel_surface, self.text_color, (0, 0, panel_width, panel_height), 3)  # Thicker border
        self.screen.blit(panel_surface, (panel_x, panel_y))
        
        # Player info with larger margins and spacing
        y_offset = panel_y + 12
        margin = 15
        
        # Create larger fonts (1.5x scale)
        try:
            large_font = pygame.font.Font(None, int(24 * 1.5))  # ~36
            large_small_font = pygame.font.Font(None, int(18 * 1.5))  # ~27
        except:
            large_font = self.font
            large_small_font = self.small_font
        
        # Player name and level
        name_text = large_font.render(f"{player.name}", True, self.text_color)
        level_text = large_font.render(f"Level {player.level}", True, (255, 215, 0))  # Gold color for level
        self.screen.blit(name_text, (panel_x + margin, y_offset))
        self.screen.blit(level_text, (panel_x + panel_width - level_text.get_width() - margin, y_offset))
        y_offset += int(37 * 1.5)  # ~55
        
        # HP Bar
        bar_width = panel_width - (margin * 2)
        bar_height = int(18 * 1.5)  # ~27
        hp_ratio = player.stats.hp / max(player.stats.max_hp, 1)
        
        # HP bar background
        hp_bg_rect = pygame.Rect(panel_x + margin, y_offset, bar_width, bar_height)
        pygame.draw.rect(self.screen, (64, 0, 0), hp_bg_rect)  # Dark red background
        
        # HP bar fill
        hp_fill_width = int(bar_width * hp_ratio)
        if hp_fill_width > 0:
            hp_fill_rect = pygame.Rect(panel_x + margin, y_offset, hp_fill_width, bar_height)
            pygame.draw.rect(self.screen, (220, 20, 20), hp_fill_rect)  # Bright red fill
        
        # HP bar border
        pygame.draw.rect(self.screen, self.text_color, hp_bg_rect, 2)
        
        # HP text centered on bar
        hp_text = f"{player.stats.hp}/{player.stats.max_hp}"
        hp_text_surface = large_small_font.render(hp_text, True, (255, 255, 255))
        hp_text_rect = hp_text_surface.get_rect(center=hp_bg_rect.center)
        self.screen.blit(hp_text_surface, hp_text_rect)
        
        y_offset += int(37 * 1.5)  # ~55
        
        # MP Bar (now a blue bar like HP)
        mp_ratio = player.stats.mp / max(player.stats.max_mp, 1)
        
        # MP bar background
        mp_bg_rect = pygame.Rect(panel_x + margin, y_offset, bar_width, bar_height)
        pygame.draw.rect(self.screen, (0, 0, 64), mp_bg_rect)  # Dark blue background
        
        # MP bar fill
        mp_fill_width = int(bar_width * mp_ratio)
        if mp_fill_width > 0:
            mp_fill_rect = pygame.Rect(panel_x + margin, y_offset, mp_fill_width, bar_height)
            pygame.draw.rect(self.screen, (20, 100, 220), mp_fill_rect)  # Bright blue fill
        
        # MP bar border
        pygame.draw.rect(self.screen, self.text_color, mp_bg_rect, 2)
        
        # MP text centered on bar
        mp_text = f"{player.stats.mp}/{player.stats.max_mp}"
        mp_text_surface = large_small_font.render(mp_text, True, (255, 255, 255))
        mp_text_rect = mp_text_surface.get_rect(center=mp_bg_rect.center)
        self.screen.blit(mp_text_surface, mp_text_rect)
        
        y_offset += int(37 * 1.5)  # ~55
        
        # XP Bar
        # Calculate XP for current level (simple progression: level^2 * 100)
        current_level_xp = (player.level - 1) ** 2 * 100
        next_level_xp = player.level ** 2 * 100
        xp_in_level = player.experience - current_level_xp
        xp_needed = next_level_xp - current_level_xp
        xp_ratio = xp_in_level / max(xp_needed, 1) if xp_needed > 0 else 1.0
        
        # XP bar background
        xp_bg_rect = pygame.Rect(panel_x + margin, y_offset, bar_width, bar_height)
        pygame.draw.rect(self.screen, (0, 32, 0), xp_bg_rect)  # Dark green background
        
        # XP bar fill
        xp_fill_width = int(bar_width * xp_ratio)
        if xp_fill_width > 0:
            xp_fill_rect = pygame.Rect(panel_x + margin, y_offset, xp_fill_width, bar_height)
            pygame.draw.rect(self.screen, (20, 220, 20), xp_fill_rect)  # Bright green fill
        
        # XP bar border
        pygame.draw.rect(self.screen, self.text_color, xp_bg_rect, 2)
        
        # XP text centered on bar
        xp_text = f"{xp_in_level}/{xp_needed}"
        xp_text_surface = large_small_font.render(xp_text, True, (255, 255, 255))
        xp_text_rect = xp_text_surface.get_rect(center=xp_bg_rect.center)
        self.screen.blit(xp_text_surface, xp_text_rect)
        
        # Current level info (bottom right) - also scaled up
        if game_state.current_level_id:
            level_text = large_small_font.render(f"Level: {game_state.current_level_id}", True, self.text_color)
            text_rect = level_text.get_rect()
            self.screen.blit(level_text, (self.screen_width - text_rect.width - 15, self.screen_height - text_rect.height - 15))
        
        # Current level info (bottom right)
        if game_state.current_level_id:
            level_text = self.small_font.render(f"Level: {game_state.current_level_id}", True, self.text_color)
            text_rect = level_text.get_rect()
            self.screen.blit(level_text, (self.screen_width - text_rect.width - 10, self.screen_height - text_rect.height - 10))
    
    def _render_loading_screen(self) -> None:
        """Render a simple loading screen."""
        loading_text = self.font.render("Loading...", True, self.text_color)
        text_rect = loading_text.get_rect(center=(self.screen_width//2, self.screen_height//2))
        self.screen.blit(loading_text, text_rect)

    def _render_debug_overlays(self, game_state: GameStateData) -> None:
        """Render debug overlays like interaction areas."""
        if not self.config.debug_mode and not self.config.show_interaction_areas:
            return

        # Show interaction areas if enabled
        if self.config.show_interaction_areas:
            self._render_interaction_areas(game_state)

    def _render_interaction_areas(self, game_state: GameStateData) -> None:
        """Render debug rectangles showing interaction areas for player and NPCs."""
        if not game_state.player:
            return

        # Player interaction area (3x3 grid around player)
        player_tile_x, player_tile_y = game_state.player.position.to_tile_coords(self.tile_size)

        # Draw player's interaction area (3x3 grid centered on player)
        for dx in [-1, 0, 1]:
            for dy in [-1, 0, 1]:
                interact_tile_x = player_tile_x + dx
                interact_tile_y = player_tile_y + dy

                # Convert to world coordinates
                world_x = interact_tile_x * self.tile_size
                world_y = interact_tile_y * self.tile_size

                # Convert to screen coordinates
                screen_pos = self.world_to_screen((world_x, world_y))

                # Only draw if visible on screen
                if (-self.tile_size <= screen_pos[0] <= self.screen_width and
                    -self.tile_size <= screen_pos[1] <= self.screen_height):

                    # Draw semi-transparent blue rectangle for player interaction area
                    interaction_surface = pygame.Surface((self.tile_size, self.tile_size), pygame.SRCALPHA)
                    if dx == 0 and dy == 0:
                        # Player's own tile - different color
                        interaction_surface.fill((0, 255, 0, 50))  # Green for player tile
                    else:
                        # Interaction tiles
                        interaction_surface.fill((0, 100, 255, 50))  # Blue for interaction area

                    self.screen.blit(interaction_surface, screen_pos)

                    # Draw border
                    pygame.draw.rect(self.screen, (0, 100, 255, 150),
                                   (*screen_pos, self.tile_size, self.tile_size), 2)

        # Draw NPC interaction areas
        for npc in game_state.npcs.values():
            npc_tile_x, npc_tile_y = npc.position.to_tile_coords(self.tile_size)

            # Draw 3x3 grid around each NPC
            for dx in [-1, 0, 1]:
                for dy in [-1, 0, 1]:
                    interact_tile_x = npc_tile_x + dx
                    interact_tile_y = npc_tile_y + dy

                    # Convert to world coordinates
                    world_x = interact_tile_x * self.tile_size
                    world_y = interact_tile_y * self.tile_size

                    # Convert to screen coordinates
                    screen_pos = self.world_to_screen((world_x, world_y))

                    # Only draw if visible on screen
                    if (-self.tile_size <= screen_pos[0] <= self.screen_width and
                        -self.tile_size <= screen_pos[1] <= self.screen_height):

                        # Draw semi-transparent red rectangle for NPC interaction area
                        interaction_surface = pygame.Surface((self.tile_size, self.tile_size), pygame.SRCALPHA)
                        if dx == 0 and dy == 0:
                            # NPC's own tile - different color
                            interaction_surface.fill((255, 255, 0, 50))  # Yellow for NPC tile
                        else:
                            # Interaction tiles
                            interaction_surface.fill((255, 100, 0, 50))  # Orange for NPC interaction area

                        self.screen.blit(interaction_surface, screen_pos)

                        # Draw border
                        pygame.draw.rect(self.screen, (255, 100, 0, 150),
                                       (*screen_pos, self.tile_size, self.tile_size), 2)
