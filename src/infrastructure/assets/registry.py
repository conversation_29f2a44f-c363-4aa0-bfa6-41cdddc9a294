"""
Asset Registry

This module maps asset IDs to their generation functions.
This is the central registry that connects game data to visual assets.
"""

from typing import Dict, Callable, Any, Optional
import pygame
from . import procedural_monsters, procedural_tiles, procedural_items, procedural_npcs


# Type alias for asset generation functions
AssetGenerator = Callable[..., pygame.Surface]

# Central registry mapping asset IDs to generation functions
ASSET_GENERATORS: Dict[str, AssetGenerator] = {
    # Monster assets
    "monster.goblin.grunt.side": procedural_monsters.get_goblin_grunt_sprite,
    "monster.goblin.shaman.side": procedural_monsters.get_goblin_shaman_sprite,
    "monster.goblin.basic.side": procedural_monsters.get_goblin_basic_sprite,
    "monster.drake.green.side": procedural_monsters.get_green_drake_sprite,
    "monster.scorpion.sand.side": procedural_monsters.get_sand_scorpion_sprite,
    "monster.wolf.ice.side": procedural_monsters.get_ice_wolf_sprite,
    "monster.troll.forest.side": procedural_monsters.get_forest_troll_sprite,

    # Animal assets - Peaceful wandering creatures
    "animal.horse.brown.side": procedural_monsters.get_horse_sprite,
    "animal.cow.spotted.side": procedural_monsters.get_cow_sprite,
    "animal.chicken.white.side": procedural_monsters.get_chicken_sprite,
    "animal.deer.brown.side": procedural_monsters.get_deer_sprite,
    "animal.pig.pink.side": procedural_monsters.get_pig_sprite,
    "animal.dog.brown.side": procedural_monsters.get_dog_sprite,
    "animal.wolf.gray.side": procedural_monsters.get_wolf_sprite,
    
    # Tile assets
    "tile.wall.stone": procedural_tiles.get_stone_wall_tile,
    "tile.floor.dirt": procedural_tiles.get_dirt_floor_tile,
    "tile.floor.sand": procedural_tiles.get_sand_floor_tile,
    "tile.floor.grass": procedural_tiles.get_grass_tile,
    "tile.floor.gravel": procedural_tiles.get_gravel_tile,
    "tile.path.cobblestone": procedural_tiles.get_cobblestone_path_tile,
    "tile.path.dirt_road": procedural_tiles.get_dirt_road_tile,
    "tile.water.deep": procedural_tiles.get_water_tile,
    "tile.water.swamp": procedural_tiles.get_swamp_tile,
    "tile.tree.oak": procedural_tiles.get_tree_tile,
    "tile.tree.willow": procedural_tiles.get_willow_tree_tile,
    "tile.door.wooden": procedural_tiles.get_wooden_door_tile,
    "tile.door.wooden.open": procedural_tiles.get_wooden_door_open_tile,
    "tile.bridge.wooden": procedural_tiles.get_wooden_bridge_tile,
    "tile.structure.wood_fence": procedural_tiles.get_wood_fence_tile,
    "tile.gate.wood_fence": procedural_tiles.get_wood_fence_gate_tile,
    "tile.gate.wood_fence.open": procedural_tiles.get_wood_fence_gate_open_tile,
    "tile.structure.wood_walkway": procedural_tiles.get_wood_walkway_tile,
    "tile.mountain.rock": procedural_tiles.get_mountain_tile,
    "tile.hill.grass": procedural_tiles.get_hill_tile,
    "tile.exit.portal": procedural_tiles.get_exit_portal_tile,
    "tile.exit.magic_door": procedural_tiles.get_magic_door_tile,
    "tile.missing_asset": procedural_tiles.get_missing_asset_tile,
    
    # Item assets
    "item.weapon.sword.rusty": procedural_items.get_rusty_sword_sprite,
    "item.weapon.sword.iron": procedural_items.get_iron_sword_sprite,
    "item.weapon.bow.wooden": procedural_items.get_wooden_bow_sprite,
    "item.armor.leather.chest": procedural_items.get_leather_armor_sprite,
    "item.armor.iron.helmet": procedural_items.get_iron_helmet_sprite,
    "item.armor.iron.chest": procedural_items.get_iron_breastplate_sprite,
    "item.armor.iron.legs": procedural_items.get_iron_greaves_sprite,
    "item.armor.iron.boots": procedural_items.get_iron_boots_sprite,
    "item.consumable.potion.health": procedural_items.get_health_potion_sprite,
    "item.consumable.potion.mana": procedural_items.get_mana_potion_sprite,
    "item.consumable.food.bread": procedural_items.get_bread_sprite,
    "item.treasure.coin.gold": procedural_items.get_gold_coin_sprite,
    "item.treasure.gem.ruby": procedural_items.get_ruby_gem_sprite,
    "item.quest.key.ancient": procedural_items.get_ancient_key_sprite,
    
    # Player assets
    "player.hero": procedural_items.get_player_sprite,
    "player.base": procedural_items.get_base_player_sprite,
    
    # UI/Effect assets
    "ui.damage_number": procedural_items.get_damage_number_sprite,
    "effect.attack.slash": procedural_items.get_slash_effect_sprite,

    # Item Icons (for UI display)
    "icon.rusty_sword": procedural_items.get_rusty_sword_icon,
    "icon.iron_sword": procedural_items.get_iron_sword_icon,
    "icon.steel_dagger": procedural_items.get_steel_dagger_icon,
    "icon.wooden_bow": procedural_items.get_wooden_bow_icon,
    "icon.battle_axe": procedural_items.get_battle_axe_icon,
    "icon.wooden_shield": procedural_items.get_wooden_shield_icon,
    "icon.iron_shield": procedural_items.get_iron_shield_icon,
    "icon.cloth_cap": procedural_items.get_cloth_cap_icon,
    "icon.cloth_shirt": procedural_items.get_cloth_shirt_icon,
    "icon.cloth_pants": procedural_items.get_cloth_pants_icon,
    "icon.simple_shoes": procedural_items.get_simple_shoes_icon,
    "icon.leather_cap": procedural_items.get_leather_cap_icon,
    "icon.leather_armor": procedural_items.get_leather_armor_icon,
    "icon.leather_boots": procedural_items.get_leather_boots_icon,
    "icon.iron_helmet": procedural_items.get_iron_helmet_icon,
    "icon.iron_breastplate": procedural_items.get_iron_breastplate_icon,
    "icon.iron_greaves": procedural_items.get_iron_greaves_icon,
    "icon.iron_boots": procedural_items.get_iron_boots_icon,
    "icon.health_potion": procedural_items.get_health_potion_icon,
    "icon.mana_potion": procedural_items.get_mana_potion_icon,
    "icon.bread": procedural_items.get_bread_icon,
    "icon.ale": procedural_items.get_ale_icon,
    "icon.cheese": procedural_items.get_cheese_icon,
    "icon.hot_meal": procedural_items.get_hot_meal_icon,
    "icon.room_key": procedural_items.get_room_key_icon,
    "icon.gold_coin": procedural_items.get_gold_coin_icon,
    "icon.ruby_gem": procedural_items.get_ruby_gem_icon,
    "icon.ancient_key": procedural_items.get_ancient_key_icon,
    "icon.meat": procedural_items.get_meat_icon,
    "icon.leather": procedural_items.get_leather_icon,

    # NPC assets
    "npc.merchant.general": procedural_npcs.get_merchant_sprite,
    "npc.armourer.town": procedural_npcs.get_armourer_sprite,
    "npc.weaponsmith.forge": procedural_npcs.get_weaponsmith_sprite,
    "npc.innkeeper.tavern": procedural_npcs.get_innkeeper_sprite,
    "npc.commoner.citizen": procedural_npcs.get_commoner_sprite,
    "npc.guard.town": procedural_npcs.get_guard_sprite,
    "npc.mayor.formal": procedural_npcs.get_mayor_sprite,

    # NPC icons (for map editor)
    "icon.npc.merchant": procedural_npcs.get_merchant_icon,
    "icon.npc.armourer": procedural_npcs.get_armourer_icon,
    "icon.npc.weaponsmith": procedural_npcs.get_weaponsmith_icon,
    "icon.npc.innkeeper": procedural_npcs.get_innkeeper_icon,
    "icon.npc.commoner": procedural_npcs.get_commoner_icon,
    "icon.npc.guard": procedural_npcs.get_guard_icon,
    "icon.npc.mayor": procedural_npcs.get_mayor_icon,

    # Monster icons (for map editor)
    "icon.monster.goblin_grunt": procedural_monsters.get_goblin_grunt_icon,
    "icon.monster.goblin_shaman": procedural_monsters.get_goblin_shaman_icon,
    "icon.monster.goblin": procedural_monsters.get_goblin_basic_icon,
    "icon.monster.green_drake": procedural_monsters.get_green_drake_icon,
    "icon.monster.sand_scorpion": procedural_monsters.get_sand_scorpion_icon,
    "icon.monster.ice_wolf": procedural_monsters.get_ice_wolf_icon,
    "icon.monster.forest_troll": procedural_monsters.get_forest_troll_icon,
    "icon.monster.wolf": procedural_monsters.get_wolf_icon,

    # Animal icons (for map editor)
    "icon.animal.horse": procedural_monsters.get_horse_icon,
    "icon.animal.cow": procedural_monsters.get_cow_icon,
    "icon.animal.chicken": procedural_monsters.get_chicken_icon,
    "icon.animal.deer": procedural_monsters.get_deer_icon,
    "icon.animal.pig": procedural_monsters.get_pig_icon,
    "icon.animal.dog": procedural_monsters.get_dog_icon,
}


def get_available_asset_ids() -> list[str]:
    """Get a list of all available asset IDs."""
    return list(ASSET_GENERATORS.keys())


def is_asset_registered(asset_id: str) -> bool:
    """Check if an asset ID is registered."""
    return asset_id in ASSET_GENERATORS


def get_asset_generator(asset_id: str) -> AssetGenerator:
    """
    Get the asset generation function for a given asset ID.
    
    Args:
        asset_id: The asset ID to look up
        
    Returns:
        The asset generation function
        
    Raises:
        KeyError: If the asset ID is not registered
    """
    if asset_id not in ASSET_GENERATORS:
        raise KeyError(f"Asset ID '{asset_id}' not found in registry")
    
    return ASSET_GENERATORS[asset_id]


def is_dynamic_player_asset(asset_id: str) -> bool:
    """Check if an asset ID represents a dynamic player asset."""
    return asset_id.startswith("player.equipped.")


def generate_dynamic_player_asset_id(equipment_ids: Dict[str, Optional[str]]) -> str:
    """
    Generate a unique asset ID for a player with specific equipment.

    Args:
        equipment_ids: Dictionary mapping equipment slots to item IDs

    Returns:
        Unique asset ID for this equipment combination
    """
    # Create a sorted string representation of equipment for consistent hashing
    equipment_parts = []
    for slot in ["head_equipment", "chest_equipment", "legs_equipment",
                 "boots_equipment", "main_hand_weapon", "off_hand_equipment"]:
        item_id = equipment_ids.get(slot)
        equipment_parts.append(f"{slot}:{item_id or 'none'}")

    equipment_string = "|".join(equipment_parts)

    # Use hash for shorter asset ID
    equipment_hash = abs(hash(equipment_string)) % 1000000  # 6-digit hash

    return f"player.equipped.{equipment_hash:06d}"


def get_dynamic_player_asset_generator(equipment_ids: Dict[str, Optional[str]]) -> AssetGenerator:
    """
    Get an asset generator function for a player with specific equipment.

    Args:
        equipment_ids: Dictionary mapping equipment slots to item IDs

    Returns:
        Asset generation function for this equipment combination
    """
    def generate_equipped_player(size=(128, 128)):
        return procedural_items.get_equipped_player_sprite(equipment_ids, size)

    return generate_equipped_player
