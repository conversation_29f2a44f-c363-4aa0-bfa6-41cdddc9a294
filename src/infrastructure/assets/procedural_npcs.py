"""
Procedural NPC Asset Generation

This module contains functions to procedurally generate NPC sprites using Pygame.
This is the ONLY place in the codebase where NPC visual assets are created.
"""

import pygame
from typing import <PERSON>ple


def get_merchant_sprite(size: Tuple[int, int] = (64, 64)) -> pygame.Surface:
    """Generate a Merchant NPC sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Color palette
    body_color = (139, 69, 19)      # Brown skin
    shirt_color = (34, 139, 34)     # Green shirt
    pants_color = (101, 67, 33)     # Dark brown pants
    hair_color = (160, 82, 45)      # Brown hair
    bag_color = (101, 67, 33)       # Brown bag
    
    # Head
    head_center = (s_w * 0.5, s_h * 0.25)
    head_radius = int(s_w * 0.12)
    pygame.draw.circle(sprite_surface, body_color, head_center, head_radius)
    
    # Hair
    hair_rect = pygame.Rect(head_center[0] - head_radius, head_center[1] - head_radius, 
                           head_radius * 2, head_radius)
    pygame.draw.ellipse(sprite_surface, hair_color, hair_rect)
    
    # Eyes
    eye_left = (head_center[0] - s_w * 0.03, head_center[1] - s_h * 0.02)
    eye_right = (head_center[0] + s_w * 0.03, head_center[1] - s_h * 0.02)
    pygame.draw.circle(sprite_surface, (255, 255, 255), eye_left, int(s_w * 0.015))
    pygame.draw.circle(sprite_surface, (255, 255, 255), eye_right, int(s_w * 0.015))
    pygame.draw.circle(sprite_surface, (0, 0, 0), eye_left, int(s_w * 0.008))
    pygame.draw.circle(sprite_surface, (0, 0, 0), eye_right, int(s_w * 0.008))
    
    # Body (shirt)
    body_rect = pygame.Rect(s_w * 0.35, s_h * 0.35, s_w * 0.3, s_h * 0.35)
    pygame.draw.rect(sprite_surface, shirt_color, body_rect)
    
    # Arms
    arm_left = pygame.Rect(s_w * 0.25, s_h * 0.4, s_w * 0.1, s_h * 0.25)
    arm_right = pygame.Rect(s_w * 0.65, s_h * 0.4, s_w * 0.1, s_h * 0.25)
    pygame.draw.rect(sprite_surface, body_color, arm_left)
    pygame.draw.rect(sprite_surface, body_color, arm_right)
    
    # Legs (pants)
    leg_left = pygame.Rect(s_w * 0.4, s_h * 0.7, s_w * 0.08, s_h * 0.25)
    leg_right = pygame.Rect(s_w * 0.52, s_h * 0.7, s_w * 0.08, s_h * 0.25)
    pygame.draw.rect(sprite_surface, pants_color, leg_left)
    pygame.draw.rect(sprite_surface, pants_color, leg_right)
    
    # Merchant bag
    bag_rect = pygame.Rect(s_w * 0.7, s_h * 0.45, s_w * 0.15, s_h * 0.2)
    pygame.draw.rect(sprite_surface, bag_color, bag_rect)
    
    return sprite_surface


def get_armourer_sprite(size: Tuple[int, int] = (64, 64)) -> pygame.Surface:
    """Generate an Armourer NPC sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Color palette
    body_color = (139, 69, 19)      # Brown skin
    armor_color = (169, 169, 169)   # Silver armor
    pants_color = (101, 67, 33)     # Dark brown pants
    hair_color = (105, 105, 105)    # Gray hair
    hammer_color = (160, 82, 45)    # Brown hammer handle
    
    # Head
    head_center = (s_w * 0.5, s_h * 0.25)
    head_radius = int(s_w * 0.12)
    pygame.draw.circle(sprite_surface, body_color, head_center, head_radius)
    
    # Hair (graying)
    hair_rect = pygame.Rect(head_center[0] - head_radius, head_center[1] - head_radius, 
                           head_radius * 2, head_radius)
    pygame.draw.ellipse(sprite_surface, hair_color, hair_rect)
    
    # Eyes
    eye_left = (head_center[0] - s_w * 0.03, head_center[1] - s_h * 0.02)
    eye_right = (head_center[0] + s_w * 0.03, head_center[1] - s_h * 0.02)
    pygame.draw.circle(sprite_surface, (255, 255, 255), eye_left, int(s_w * 0.015))
    pygame.draw.circle(sprite_surface, (255, 255, 255), eye_right, int(s_w * 0.015))
    pygame.draw.circle(sprite_surface, (0, 0, 0), eye_left, int(s_w * 0.008))
    pygame.draw.circle(sprite_surface, (0, 0, 0), eye_right, int(s_w * 0.008))
    
    # Body (armor vest)
    body_rect = pygame.Rect(s_w * 0.35, s_h * 0.35, s_w * 0.3, s_h * 0.35)
    pygame.draw.rect(sprite_surface, armor_color, body_rect)
    
    # Arms
    arm_left = pygame.Rect(s_w * 0.25, s_h * 0.4, s_w * 0.1, s_h * 0.25)
    arm_right = pygame.Rect(s_w * 0.65, s_h * 0.4, s_w * 0.1, s_h * 0.25)
    pygame.draw.rect(sprite_surface, body_color, arm_left)
    pygame.draw.rect(sprite_surface, body_color, arm_right)
    
    # Legs (pants)
    leg_left = pygame.Rect(s_w * 0.4, s_h * 0.7, s_w * 0.08, s_h * 0.25)
    leg_right = pygame.Rect(s_w * 0.52, s_h * 0.7, s_w * 0.08, s_h * 0.25)
    pygame.draw.rect(sprite_surface, pants_color, leg_left)
    pygame.draw.rect(sprite_surface, pants_color, leg_right)
    
    # Hammer tool
    hammer_handle = pygame.Rect(s_w * 0.75, s_h * 0.4, s_w * 0.03, s_h * 0.25)
    hammer_head = pygame.Rect(s_w * 0.73, s_h * 0.4, s_w * 0.07, s_h * 0.08)
    pygame.draw.rect(sprite_surface, hammer_color, hammer_handle)
    pygame.draw.rect(sprite_surface, armor_color, hammer_head)
    
    return sprite_surface


def get_weaponsmith_sprite(size: Tuple[int, int] = (64, 64)) -> pygame.Surface:
    """Generate a Weaponsmith NPC sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Color palette
    body_color = (139, 69, 19)      # Brown skin
    apron_color = (101, 67, 33)     # Brown leather apron
    pants_color = (60, 60, 60)      # Dark gray pants
    hair_color = (0, 0, 0)          # Black hair
    sword_color = (192, 192, 192)   # Silver sword
    
    # Head
    head_center = (s_w * 0.5, s_h * 0.25)
    head_radius = int(s_w * 0.12)
    pygame.draw.circle(sprite_surface, body_color, head_center, head_radius)
    
    # Hair
    hair_rect = pygame.Rect(head_center[0] - head_radius, head_center[1] - head_radius, 
                           head_radius * 2, head_radius)
    pygame.draw.ellipse(sprite_surface, hair_color, hair_rect)
    
    # Eyes
    eye_left = (head_center[0] - s_w * 0.03, head_center[1] - s_h * 0.02)
    eye_right = (head_center[0] + s_w * 0.03, head_center[1] - s_h * 0.02)
    pygame.draw.circle(sprite_surface, (255, 255, 255), eye_left, int(s_w * 0.015))
    pygame.draw.circle(sprite_surface, (255, 255, 255), eye_right, int(s_w * 0.015))
    pygame.draw.circle(sprite_surface, (0, 0, 0), eye_left, int(s_w * 0.008))
    pygame.draw.circle(sprite_surface, (0, 0, 0), eye_right, int(s_w * 0.008))
    
    # Body (leather apron)
    body_rect = pygame.Rect(s_w * 0.35, s_h * 0.35, s_w * 0.3, s_h * 0.35)
    pygame.draw.rect(sprite_surface, apron_color, body_rect)
    
    # Arms
    arm_left = pygame.Rect(s_w * 0.25, s_h * 0.4, s_w * 0.1, s_h * 0.25)
    arm_right = pygame.Rect(s_w * 0.65, s_h * 0.4, s_w * 0.1, s_h * 0.25)
    pygame.draw.rect(sprite_surface, body_color, arm_left)
    pygame.draw.rect(sprite_surface, body_color, arm_right)
    
    # Legs (pants)
    leg_left = pygame.Rect(s_w * 0.4, s_h * 0.7, s_w * 0.08, s_h * 0.25)
    leg_right = pygame.Rect(s_w * 0.52, s_h * 0.7, s_w * 0.08, s_h * 0.25)
    pygame.draw.rect(sprite_surface, pants_color, leg_left)
    pygame.draw.rect(sprite_surface, pants_color, leg_right)
    
    # Sword (held or displayed)
    sword_blade = pygame.Rect(s_w * 0.75, s_h * 0.3, s_w * 0.03, s_h * 0.3)
    sword_hilt = pygame.Rect(s_w * 0.73, s_h * 0.6, s_w * 0.07, s_h * 0.05)
    pygame.draw.rect(sprite_surface, sword_color, sword_blade)
    pygame.draw.rect(sprite_surface, (101, 67, 33), sword_hilt)
    
    return sprite_surface


def get_innkeeper_sprite(size: Tuple[int, int] = (64, 64)) -> pygame.Surface:
    """Generate an Innkeeper NPC sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Color palette
    body_color = (139, 69, 19)      # Brown skin
    apron_color = (255, 255, 255)   # White apron
    shirt_color = (34, 139, 34)     # Green shirt
    pants_color = (101, 67, 33)     # Brown pants
    hair_color = (160, 82, 45)      # Brown hair
    
    # Head
    head_center = (s_w * 0.5, s_h * 0.25)
    head_radius = int(s_w * 0.12)
    pygame.draw.circle(sprite_surface, body_color, head_center, head_radius)
    
    # Hair
    hair_rect = pygame.Rect(head_center[0] - head_radius, head_center[1] - head_radius, 
                           head_radius * 2, head_radius)
    pygame.draw.ellipse(sprite_surface, hair_color, hair_rect)
    
    # Eyes (friendly)
    eye_left = (head_center[0] - s_w * 0.03, head_center[1] - s_h * 0.02)
    eye_right = (head_center[0] + s_w * 0.03, head_center[1] - s_h * 0.02)
    pygame.draw.circle(sprite_surface, (255, 255, 255), eye_left, int(s_w * 0.015))
    pygame.draw.circle(sprite_surface, (255, 255, 255), eye_right, int(s_w * 0.015))
    pygame.draw.circle(sprite_surface, (0, 0, 0), eye_left, int(s_w * 0.008))
    pygame.draw.circle(sprite_surface, (0, 0, 0), eye_right, int(s_w * 0.008))
    
    # Smile
    smile_center = (head_center[0], head_center[1] + s_h * 0.03)
    pygame.draw.arc(sprite_surface, (0, 0, 0), 
                   (smile_center[0] - s_w * 0.03, smile_center[1] - s_h * 0.01, 
                    s_w * 0.06, s_h * 0.02), 0, 3.14, 2)
    
    # Body (shirt)
    body_rect = pygame.Rect(s_w * 0.35, s_h * 0.35, s_w * 0.3, s_h * 0.35)
    pygame.draw.rect(sprite_surface, shirt_color, body_rect)
    
    # Apron over shirt
    apron_rect = pygame.Rect(s_w * 0.37, s_h * 0.4, s_w * 0.26, s_h * 0.25)
    pygame.draw.rect(sprite_surface, apron_color, apron_rect)
    
    # Arms
    arm_left = pygame.Rect(s_w * 0.25, s_h * 0.4, s_w * 0.1, s_h * 0.25)
    arm_right = pygame.Rect(s_w * 0.65, s_h * 0.4, s_w * 0.1, s_h * 0.25)
    pygame.draw.rect(sprite_surface, body_color, arm_left)
    pygame.draw.rect(sprite_surface, body_color, arm_right)
    
    # Legs (pants)
    leg_left = pygame.Rect(s_w * 0.4, s_h * 0.7, s_w * 0.08, s_h * 0.25)
    leg_right = pygame.Rect(s_w * 0.52, s_h * 0.7, s_w * 0.08, s_h * 0.25)
    pygame.draw.rect(sprite_surface, pants_color, leg_left)
    pygame.draw.rect(sprite_surface, pants_color, leg_right)
    
    # Mug/tankard in hand
    mug_rect = pygame.Rect(s_w * 0.7, s_h * 0.5, s_w * 0.06, s_h * 0.08)
    pygame.draw.rect(sprite_surface, (160, 82, 45), mug_rect)

    return sprite_surface


def get_commoner_sprite(size: Tuple[int, int] = (64, 64)) -> pygame.Surface:
    """Generate a Commoner NPC sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Color palette (simple civilian clothes)
    body_color = (139, 69, 19)      # Brown skin
    shirt_color = (70, 130, 180)    # Steel blue shirt
    pants_color = (101, 67, 33)     # Brown pants
    hair_color = (160, 82, 45)      # Brown hair

    # Head
    head_center = (s_w * 0.5, s_h * 0.25)
    head_radius = int(s_w * 0.12)
    pygame.draw.circle(sprite_surface, body_color, head_center, head_radius)

    # Hair
    hair_rect = pygame.Rect(head_center[0] - head_radius, head_center[1] - head_radius,
                           head_radius * 2, head_radius)
    pygame.draw.ellipse(sprite_surface, hair_color, hair_rect)

    # Eyes
    eye_left = (head_center[0] - s_w * 0.03, head_center[1] - s_h * 0.02)
    eye_right = (head_center[0] + s_w * 0.03, head_center[1] - s_h * 0.02)
    pygame.draw.circle(sprite_surface, (255, 255, 255), eye_left, int(s_w * 0.015))
    pygame.draw.circle(sprite_surface, (255, 255, 255), eye_right, int(s_w * 0.015))
    pygame.draw.circle(sprite_surface, (0, 0, 0), eye_left, int(s_w * 0.008))
    pygame.draw.circle(sprite_surface, (0, 0, 0), eye_right, int(s_w * 0.008))

    # Body (simple shirt)
    body_rect = pygame.Rect(s_w * 0.35, s_h * 0.35, s_w * 0.3, s_h * 0.35)
    pygame.draw.rect(sprite_surface, shirt_color, body_rect)

    # Arms
    arm_left = pygame.Rect(s_w * 0.25, s_h * 0.4, s_w * 0.1, s_h * 0.25)
    arm_right = pygame.Rect(s_w * 0.65, s_h * 0.4, s_w * 0.1, s_h * 0.25)
    pygame.draw.rect(sprite_surface, body_color, arm_left)
    pygame.draw.rect(sprite_surface, body_color, arm_right)

    # Legs (pants)
    leg_left = pygame.Rect(s_w * 0.4, s_h * 0.7, s_w * 0.08, s_h * 0.25)
    leg_right = pygame.Rect(s_w * 0.52, s_h * 0.7, s_w * 0.08, s_h * 0.25)
    pygame.draw.rect(sprite_surface, pants_color, leg_left)
    pygame.draw.rect(sprite_surface, pants_color, leg_right)

    return sprite_surface


def get_guard_sprite(size: Tuple[int, int] = (64, 64)) -> pygame.Surface:
    """Generate a Guard NPC sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Color palette (guard uniform)
    body_color = (139, 69, 19)      # Brown skin
    armor_color = (169, 169, 169)   # Silver armor
    pants_color = (60, 60, 60)      # Dark gray pants
    hair_color = (101, 67, 33)      # Brown hair
    sword_color = (192, 192, 192)   # Silver sword

    # Head
    head_center = (s_w * 0.5, s_h * 0.25)
    head_radius = int(s_w * 0.12)
    pygame.draw.circle(sprite_surface, body_color, head_center, head_radius)

    # Hair (short, military style)
    hair_rect = pygame.Rect(head_center[0] - head_radius * 0.8, head_center[1] - head_radius,
                           head_radius * 1.6, head_radius * 0.8)
    pygame.draw.ellipse(sprite_surface, hair_color, hair_rect)

    # Eyes (alert)
    eye_left = (head_center[0] - s_w * 0.03, head_center[1] - s_h * 0.02)
    eye_right = (head_center[0] + s_w * 0.03, head_center[1] - s_h * 0.02)
    pygame.draw.circle(sprite_surface, (255, 255, 255), eye_left, int(s_w * 0.015))
    pygame.draw.circle(sprite_surface, (255, 255, 255), eye_right, int(s_w * 0.015))
    pygame.draw.circle(sprite_surface, (0, 0, 0), eye_left, int(s_w * 0.008))
    pygame.draw.circle(sprite_surface, (0, 0, 0), eye_right, int(s_w * 0.008))

    # Body (armor)
    body_rect = pygame.Rect(s_w * 0.35, s_h * 0.35, s_w * 0.3, s_h * 0.35)
    pygame.draw.rect(sprite_surface, armor_color, body_rect)

    # Arms (armored)
    arm_left = pygame.Rect(s_w * 0.25, s_h * 0.4, s_w * 0.1, s_h * 0.25)
    arm_right = pygame.Rect(s_w * 0.65, s_h * 0.4, s_w * 0.1, s_h * 0.25)
    pygame.draw.rect(sprite_surface, armor_color, arm_left)
    pygame.draw.rect(sprite_surface, armor_color, arm_right)

    # Legs (armored pants)
    leg_left = pygame.Rect(s_w * 0.4, s_h * 0.7, s_w * 0.08, s_h * 0.25)
    leg_right = pygame.Rect(s_w * 0.52, s_h * 0.7, s_w * 0.08, s_h * 0.25)
    pygame.draw.rect(sprite_surface, pants_color, leg_left)
    pygame.draw.rect(sprite_surface, pants_color, leg_right)

    # Sword at side
    sword_sheath = pygame.Rect(s_w * 0.2, s_h * 0.5, s_w * 0.03, s_h * 0.2)
    pygame.draw.rect(sprite_surface, (101, 67, 33), sword_sheath)
    sword_hilt = pygame.Rect(s_w * 0.18, s_h * 0.48, s_w * 0.07, s_h * 0.05)
    pygame.draw.rect(sprite_surface, sword_color, sword_hilt)

    # Shield (optional, on back or arm)
    shield_rect = pygame.Rect(s_w * 0.75, s_h * 0.4, s_w * 0.08, s_h * 0.15)
    pygame.draw.ellipse(sprite_surface, (139, 69, 19), shield_rect)

    return sprite_surface


def get_mayor_sprite(size: Tuple[int, int] = (64, 64)) -> pygame.Surface:
    """Generate a Mayor NPC sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Color palette (formal attire)
    body_color = (139, 69, 19)      # Brown skin
    suit_color = (25, 25, 112)      # Midnight blue formal suit
    vest_color = (220, 220, 220)    # Light gray vest
    pants_color = (25, 25, 112)     # Matching blue pants
    hair_color = (105, 105, 105)    # Distinguished gray hair
    chain_color = (255, 215, 0)     # Gold chain of office

    # Head
    head_center = (s_w * 0.5, s_h * 0.25)
    head_radius = int(s_w * 0.12)
    pygame.draw.circle(sprite_surface, body_color, head_center, head_radius)

    # Distinguished gray hair
    hair_rect = pygame.Rect(head_center[0] - head_radius, head_center[1] - head_radius,
                           head_radius * 2, head_radius)
    pygame.draw.ellipse(sprite_surface, hair_color, hair_rect)

    # Body (formal suit)
    body_rect = pygame.Rect(s_w * 0.35, s_h * 0.35, s_w * 0.3, s_h * 0.4)
    pygame.draw.rect(sprite_surface, suit_color, body_rect)

    # Vest
    vest_rect = pygame.Rect(s_w * 0.4, s_h * 0.4, s_w * 0.2, s_h * 0.25)
    pygame.draw.rect(sprite_surface, vest_color, vest_rect)

    # Legs (formal pants)
    left_leg = pygame.Rect(s_w * 0.38, s_h * 0.75, s_w * 0.1, s_h * 0.2)
    right_leg = pygame.Rect(s_w * 0.52, s_h * 0.75, s_w * 0.1, s_h * 0.2)
    pygame.draw.rect(sprite_surface, pants_color, left_leg)
    pygame.draw.rect(sprite_surface, pants_color, right_leg)

    # Arms (formal suit sleeves)
    left_arm = pygame.Rect(s_w * 0.25, s_h * 0.4, s_w * 0.08, s_h * 0.25)
    right_arm = pygame.Rect(s_w * 0.67, s_h * 0.4, s_w * 0.08, s_h * 0.25)
    pygame.draw.rect(sprite_surface, suit_color, left_arm)
    pygame.draw.rect(sprite_surface, suit_color, right_arm)

    # Hands
    left_hand_center = (s_w * 0.29, s_h * 0.67)
    right_hand_center = (s_w * 0.71, s_h * 0.67)
    hand_radius = int(s_w * 0.04)
    pygame.draw.circle(sprite_surface, body_color, left_hand_center, hand_radius)
    pygame.draw.circle(sprite_surface, body_color, right_hand_center, hand_radius)

    # Chain of office (ceremonial gold chain)
    chain_start = (s_w * 0.42, s_h * 0.35)
    chain_end = (s_w * 0.58, s_h * 0.35)
    pygame.draw.line(sprite_surface, chain_color, chain_start, chain_end, 3)

    # Chain pendant
    pendant_center = (s_w * 0.5, s_h * 0.42)
    pendant_radius = int(s_w * 0.03)
    pygame.draw.circle(sprite_surface, chain_color, pendant_center, pendant_radius)

    # Formal shoes
    left_shoe = pygame.Rect(s_w * 0.36, s_h * 0.95, s_w * 0.12, s_h * 0.05)
    right_shoe = pygame.Rect(s_w * 0.52, s_h * 0.95, s_w * 0.12, s_h * 0.05)
    pygame.draw.rect(sprite_surface, (0, 0, 0), left_shoe)  # Black formal shoes
    pygame.draw.rect(sprite_surface, (0, 0, 0), right_shoe)

    return sprite_surface


# Icon generation functions for map editor
def get_merchant_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a Merchant icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Simple merchant bag icon
    bag_rect = pygame.Rect(s_w * 0.2, s_h * 0.3, s_w * 0.6, s_h * 0.5)
    pygame.draw.rect(icon_surface, (101, 67, 33), bag_rect)

    # Coins
    pygame.draw.circle(icon_surface, (255, 215, 0), (int(s_w * 0.3), int(s_h * 0.4)), int(s_w * 0.08))
    pygame.draw.circle(icon_surface, (255, 215, 0), (int(s_w * 0.5), int(s_h * 0.5)), int(s_w * 0.08))
    pygame.draw.circle(icon_surface, (255, 215, 0), (int(s_w * 0.7), int(s_h * 0.6)), int(s_w * 0.08))

    return icon_surface


def get_armourer_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate an Armourer icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Shield icon
    shield_rect = pygame.Rect(s_w * 0.25, s_h * 0.2, s_w * 0.5, s_h * 0.6)
    pygame.draw.ellipse(icon_surface, (169, 169, 169), shield_rect)

    # Cross on shield
    pygame.draw.line(icon_surface, (255, 255, 255),
                    (s_w * 0.5, s_h * 0.3), (s_w * 0.5, s_h * 0.7), 3)
    pygame.draw.line(icon_surface, (255, 255, 255),
                    (s_w * 0.35, s_h * 0.5), (s_w * 0.65, s_h * 0.5), 3)

    return icon_surface


def get_weaponsmith_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a Weaponsmith icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Sword icon
    blade_rect = pygame.Rect(s_w * 0.45, s_h * 0.1, s_w * 0.1, s_h * 0.6)
    hilt_rect = pygame.Rect(s_w * 0.35, s_h * 0.7, s_w * 0.3, s_h * 0.08)
    pygame.draw.rect(icon_surface, (192, 192, 192), blade_rect)
    pygame.draw.rect(icon_surface, (101, 67, 33), hilt_rect)

    return icon_surface


def get_innkeeper_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate an Innkeeper icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Mug/tankard icon
    mug_rect = pygame.Rect(s_w * 0.3, s_h * 0.3, s_w * 0.4, s_h * 0.5)
    handle_rect = pygame.Rect(s_w * 0.7, s_h * 0.45, s_w * 0.1, s_h * 0.2)
    pygame.draw.rect(icon_surface, (160, 82, 45), mug_rect)
    pygame.draw.rect(icon_surface, (160, 82, 45), handle_rect)

    # Foam on top
    foam_rect = pygame.Rect(s_w * 0.32, s_h * 0.25, s_w * 0.36, s_h * 0.1)
    pygame.draw.ellipse(icon_surface, (255, 255, 255), foam_rect)

    return icon_surface


def get_commoner_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a Commoner icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Simple person silhouette
    head_center = (s_w * 0.5, s_h * 0.3)
    head_radius = int(s_w * 0.15)
    pygame.draw.circle(icon_surface, (70, 130, 180), head_center, head_radius)

    body_rect = pygame.Rect(s_w * 0.4, s_h * 0.45, s_w * 0.2, s_h * 0.35)
    pygame.draw.rect(icon_surface, (70, 130, 180), body_rect)

    return icon_surface


def get_guard_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a Guard icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Helmet icon
    helmet_rect = pygame.Rect(s_w * 0.25, s_h * 0.2, s_w * 0.5, s_h * 0.4)
    pygame.draw.ellipse(icon_surface, (169, 169, 169), helmet_rect)

    # Visor
    visor_rect = pygame.Rect(s_w * 0.3, s_h * 0.35, s_w * 0.4, s_h * 0.1)
    pygame.draw.rect(icon_surface, (60, 60, 60), visor_rect)

    # Plume
    plume_points = [(s_w * 0.5, s_h * 0.2), (s_w * 0.45, s_h * 0.1), (s_w * 0.55, s_h * 0.1)]
    pygame.draw.polygon(icon_surface, (255, 0, 0), plume_points)

    return icon_surface


def get_mayor_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a Mayor icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Crown/ceremonial hat icon
    crown_rect = pygame.Rect(s_w * 0.2, s_h * 0.2, s_w * 0.6, s_h * 0.3)
    pygame.draw.rect(icon_surface, (255, 215, 0), crown_rect)  # Gold crown

    # Crown points
    point1 = (s_w * 0.3, s_h * 0.2)
    point2 = (s_w * 0.25, s_h * 0.1)
    point3 = (s_w * 0.35, s_h * 0.1)
    pygame.draw.polygon(icon_surface, (255, 215, 0), [point1, point2, point3])

    point4 = (s_w * 0.5, s_h * 0.2)
    point5 = (s_w * 0.45, s_h * 0.05)
    point6 = (s_w * 0.55, s_h * 0.05)
    pygame.draw.polygon(icon_surface, (255, 215, 0), [point4, point5, point6])

    point7 = (s_w * 0.7, s_h * 0.2)
    point8 = (s_w * 0.65, s_h * 0.1)
    point9 = (s_w * 0.75, s_h * 0.1)
    pygame.draw.polygon(icon_surface, (255, 215, 0), [point7, point8, point9])

    # Ceremonial sash
    sash_rect = pygame.Rect(s_w * 0.1, s_h * 0.6, s_w * 0.8, s_h * 0.15)
    pygame.draw.rect(icon_surface, (25, 25, 112), sash_rect)  # Blue sash

    return icon_surface
