"""
Procedural Monster Asset Generation

This module contains functions to procedurally generate monster sprites using Pygame.
This is the ONLY place in the codebase where monster visual assets are created.
"""

import pygame
from typing import <PERSON><PERSON>


def get_green_drake_sprite(size: Tuple[int, int] = (64, 64)) -> pygame.Surface:
    """
    Generate a Green Drake sprite procedurally.
    
    This is the detailed implementation from the architectural blueprint.
    Creates a side-view dragon with wings, claws, and proper proportions.
    """
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Color palette
    body_color = (34, 139, 34)      # Forest Green
    wing_color_far = (0, 80, 0)     # Darker Green for far wing
    wing_color_near = (0, 100, 0)   # Slightly lighter for near wing
    underbelly_color = (152, 251, 152)  # Pale Green
    eye_color = (255, 255, 0)       # Yellow
    claw_color = (60, 60, 60)       # Dark Gray

    # Body (main ellipse, side view)
    body_rect = pygame.Rect(s_w*0.2, s_h*0.35, s_w*0.6, s_h*0.35)
    pygame.draw.ellipse(sprite_surface, body_color, body_rect)
    pygame.draw.ellipse(sprite_surface, underbelly_color, 
        (body_rect.left + s_w*0.05, body_rect.centery + s_h*0.05, 
         body_rect.width*0.9, body_rect.height*0.4))

    # Tail (extending from left of body)
    tail_points = [
        (body_rect.left + s_w*0.05, body_rect.centery),
        (body_rect.left - s_w*0.2, body_rect.centery - s_h*0.05),
        (body_rect.left - s_w*0.3, body_rect.centery + s_h*0.05),
        (body_rect.left - s_w*0.2, body_rect.centery + s_h*0.15),
    ]
    pygame.draw.polygon(sprite_surface, body_color, tail_points)

    # Legs (two visible on the near side)
    leg_y_start = body_rect.bottom - s_h*0.05
    # Near front leg
    pygame.draw.rect(sprite_surface, body_color, 
        (body_rect.centerx + s_w*0.1, leg_y_start, s_w*0.08, s_h*0.2))
    pygame.draw.polygon(sprite_surface, claw_color, [
        (body_rect.centerx + s_w*0.1, leg_y_start + s_h*0.2), 
        (body_rect.centerx + s_w*0.18, leg_y_start + s_h*0.2), 
        (body_rect.centerx + s_w*0.14, leg_y_start + s_h*0.25)
    ])
    # Near back leg
    pygame.draw.rect(sprite_surface, body_color, 
        (body_rect.centerx - s_w*0.15, leg_y_start, s_w*0.08, s_h*0.2))
    pygame.draw.polygon(sprite_surface, claw_color, [
        (body_rect.centerx - s_w*0.15, leg_y_start + s_h*0.2), 
        (body_rect.centerx - s_w*0.07, leg_y_start + s_h*0.2), 
        (body_rect.centerx - s_w*0.11, leg_y_start + s_h*0.25)
    ])

    # Far Wing (drawn first, partially obscured)
    fw_p1 = (body_rect.centerx - s_w*0.05, body_rect.top + s_h*0.05)  # Shoulder
    fw_p2 = (fw_p1[0] - s_w*0.2, fw_p1[1] - s_h*0.25)  # Elbow/Mid
    fw_p3 = (fw_p2[0] + s_w*0.1, fw_p2[1] - s_h*0.15)  # Tip
    fw_p4 = (fw_p1[0] + s_w*0.15, body_rect.centery - s_h*0.05)  # Lower connection
    pygame.draw.polygon(sprite_surface, wing_color_far, [fw_p1, fw_p2, fw_p3, fw_p4])

    # Head and Neck (extending from right of body)
    neck_base_x, neck_base_y = body_rect.right - s_w*0.05, body_rect.centery - s_h*0.05
    neck_mid_x, neck_mid_y = neck_base_x + s_w*0.1, neck_base_y - s_h*0.1
    head_x, head_y = neck_mid_x + s_w*0.1, neck_mid_y - s_h*0.05
    
    pygame.draw.line(sprite_surface, body_color, (neck_base_x, neck_base_y), 
                    (neck_mid_x, neck_mid_y), int(s_w*0.1))  # Neck
    pygame.draw.circle(sprite_surface, body_color, (int(head_x), int(head_y)), 
                      int(s_w*0.12))  # Head main
    pygame.draw.polygon(sprite_surface, body_color, [
        (head_x, head_y - s_h*0.05), 
        (head_x + s_w*0.15, head_y), 
        (head_x, head_y + s_h*0.05)
    ])  # Snout
    pygame.draw.circle(sprite_surface, eye_color, 
        (int(head_x + s_w*0.03), int(head_y - s_h*0.02)), int(s_w*0.03))  # Eye

    # Near Wing (drawn last, on top)
    nw_p1 = (body_rect.centerx + s_w*0.05, body_rect.top)  # Shoulder
    nw_p2 = (nw_p1[0] - s_w*0.15, nw_p1[1] - s_h*0.3)   # Elbow/Mid (higher)
    nw_p3 = (nw_p2[0] + s_w*0.2, nw_p2[1] - s_h*0.1)    # Tip (sweeping back)
    nw_p4 = (nw_p1[0] + s_w*0.2, body_rect.centery)     # Lower connection
    pygame.draw.polygon(sprite_surface, wing_color_near, [nw_p1, nw_p2, nw_p3, nw_p4])
    
    return sprite_surface


def get_goblin_grunt_sprite(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a Goblin Grunt sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Colors
    skin_color = (60, 120, 60)      # Green skin
    clothing_color = (101, 67, 33)  # Brown clothing
    weapon_color = (139, 69, 19)    # Brown weapon handle
    blade_color = (192, 192, 192)   # Silver blade
    eye_color = (255, 0, 0)         # Red eyes
    
    # Body (side view)
    body_rect = pygame.Rect(s_w*0.25, s_h*0.4, s_w*0.4, s_h*0.35)
    pygame.draw.ellipse(sprite_surface, skin_color, body_rect)
    
    # Clothing
    cloth_rect = pygame.Rect(s_w*0.28, s_h*0.45, s_w*0.34, s_h*0.25)
    pygame.draw.rect(sprite_surface, clothing_color, cloth_rect)
    
    # Head
    head_x, head_y = body_rect.centerx, body_rect.top - s_h*0.1
    pygame.draw.circle(sprite_surface, skin_color, (int(head_x), int(head_y)), int(s_w*0.15))
    
    # Ears (pointed)
    ear_points = [
        (head_x - s_w*0.1, head_y - s_h*0.05),
        (head_x - s_w*0.2, head_y - s_h*0.15),
        (head_x - s_w*0.05, head_y - s_h*0.1)
    ]
    pygame.draw.polygon(sprite_surface, skin_color, ear_points)
    
    # Eyes
    pygame.draw.circle(sprite_surface, eye_color, 
        (int(head_x + s_w*0.03), int(head_y - s_h*0.02)), 2)
    
    # Arms and weapon
    # Weapon (crude sword)
    weapon_start = (body_rect.right + s_w*0.05, body_rect.centery)
    weapon_end = (weapon_start[0] + s_w*0.2, weapon_start[1] - s_h*0.1)
    pygame.draw.line(sprite_surface, weapon_color, weapon_start, weapon_end, 3)
    pygame.draw.line(sprite_surface, blade_color, weapon_end, 
        (weapon_end[0] + s_w*0.1, weapon_end[1]), 2)
    
    # Legs
    leg_y = body_rect.bottom
    pygame.draw.rect(sprite_surface, skin_color, 
        (body_rect.centerx - s_w*0.05, leg_y, s_w*0.06, s_h*0.15))
    pygame.draw.rect(sprite_surface, skin_color, 
        (body_rect.centerx + s_w*0.02, leg_y, s_w*0.06, s_h*0.15))
    
    return sprite_surface


def get_goblin_shaman_sprite(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a Goblin Shaman sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Colors
    skin_color = (40, 100, 40)      # Darker green skin
    robe_color = (75, 0, 130)       # Purple robe
    staff_color = (139, 69, 19)     # Brown staff
    orb_color = (0, 255, 255)       # Cyan magical orb
    eye_color = (255, 255, 0)       # Yellow eyes
    
    # Body with robe
    body_rect = pygame.Rect(s_w*0.2, s_h*0.35, s_w*0.5, s_h*0.4)
    pygame.draw.ellipse(sprite_surface, robe_color, body_rect)
    
    # Head
    head_x, head_y = body_rect.centerx, body_rect.top - s_h*0.08
    pygame.draw.circle(sprite_surface, skin_color, (int(head_x), int(head_y)), int(s_w*0.12))
    
    # Pointed ears
    ear_points = [
        (head_x - s_w*0.08, head_y - s_h*0.03),
        (head_x - s_w*0.15, head_y - s_h*0.12),
        (head_x - s_w*0.03, head_y - s_h*0.08)
    ]
    pygame.draw.polygon(sprite_surface, skin_color, ear_points)
    
    # Eyes
    pygame.draw.circle(sprite_surface, eye_color, 
        (int(head_x + s_w*0.02), int(head_y - s_h*0.02)), 2)
    
    # Staff
    staff_x = body_rect.right + s_w*0.02
    staff_bottom = body_rect.bottom + s_h*0.1
    staff_top = head_y - s_h*0.1
    pygame.draw.line(sprite_surface, staff_color, 
        (staff_x, staff_bottom), (staff_x, staff_top), 3)
    
    # Magical orb at top of staff
    pygame.draw.circle(sprite_surface, orb_color, (int(staff_x), int(staff_top)), 4)
    
    return sprite_surface


def get_sand_scorpion_sprite(size: Tuple[int, int] = (48, 32)) -> pygame.Surface:
    """Generate a Sand Scorpion sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Colors
    body_color = (194, 154, 108)    # Sandy brown
    claw_color = (160, 120, 80)     # Darker brown
    stinger_color = (80, 40, 20)    # Dark brown
    eye_color = (255, 0, 0)         # Red eyes
    
    # Main body segments
    segment1 = pygame.Rect(s_w*0.2, s_h*0.4, s_w*0.25, s_h*0.3)
    segment2 = pygame.Rect(s_w*0.4, s_h*0.45, s_w*0.2, s_h*0.25)
    segment3 = pygame.Rect(s_w*0.55, s_h*0.5, s_w*0.15, s_h*0.2)
    
    pygame.draw.ellipse(sprite_surface, body_color, segment1)
    pygame.draw.ellipse(sprite_surface, body_color, segment2)
    pygame.draw.ellipse(sprite_surface, body_color, segment3)
    
    # Claws
    claw1_points = [
        (segment1.left - s_w*0.1, segment1.centery - s_h*0.1),
        (segment1.left - s_w*0.15, segment1.centery - s_h*0.15),
        (segment1.left - s_w*0.05, segment1.centery - s_h*0.05)
    ]
    claw2_points = [
        (segment1.left - s_w*0.1, segment1.centery + s_h*0.1),
        (segment1.left - s_w*0.15, segment1.centery + s_h*0.15),
        (segment1.left - s_w*0.05, segment1.centery + s_h*0.05)
    ]
    pygame.draw.polygon(sprite_surface, claw_color, claw1_points)
    pygame.draw.polygon(sprite_surface, claw_color, claw2_points)
    
    # Tail with stinger
    tail_start = (segment3.right, segment3.centery)
    tail_mid = (tail_start[0] + s_w*0.1, tail_start[1] - s_h*0.15)
    tail_end = (tail_mid[0] + s_w*0.05, tail_mid[1] - s_h*0.1)
    
    pygame.draw.line(sprite_surface, body_color, tail_start, tail_mid, 4)
    pygame.draw.line(sprite_surface, body_color, tail_mid, tail_end, 3)
    pygame.draw.circle(sprite_surface, stinger_color, (int(tail_end[0]), int(tail_end[1])), 3)
    
    # Eyes
    pygame.draw.circle(sprite_surface, eye_color, 
        (int(segment1.left + s_w*0.05), int(segment1.top + s_h*0.05)), 2)
    
    # Legs (simple lines)
    for i in range(3):
        leg_x = segment1.centerx + i * s_w*0.08
        leg_top = segment1.bottom
        leg_bottom = leg_top + s_h*0.1
        pygame.draw.line(sprite_surface, body_color, (leg_x, leg_top), (leg_x, leg_bottom), 2)
    
    return sprite_surface


def get_horse_sprite(size: Tuple[int, int] = (48, 40)) -> pygame.Surface:
    """Generate a Wild Horse sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Colors
    body_color = (139, 69, 19)      # Brown
    mane_color = (101, 67, 33)      # Dark brown
    leg_color = (101, 67, 33)       # Dark brown
    eye_color = (0, 0, 0)           # Black
    
    # Body (side view)
    body_rect = pygame.Rect(s_w*0.2, s_h*0.35, s_w*0.5, s_h*0.3)
    pygame.draw.ellipse(sprite_surface, body_color, body_rect)
    
    # Head and neck
    head_x, head_y = body_rect.left - s_w*0.1, body_rect.top - s_h*0.05
    pygame.draw.ellipse(sprite_surface, body_color, 
        (head_x, head_y, s_w*0.2, s_h*0.25))
    
    # Neck connection
    pygame.draw.polygon(sprite_surface, body_color, [
        (body_rect.left, body_rect.top),
        (head_x + s_w*0.15, head_y + s_h*0.2),
        (body_rect.left + s_w*0.1, body_rect.top + s_h*0.1)
    ])
    
    # Mane
    for i in range(5):
        mane_x = head_x + s_w*0.05 + i * s_w*0.03
        mane_y = head_y - s_h*0.05
        pygame.draw.line(sprite_surface, mane_color, 
            (mane_x, mane_y), (mane_x - s_w*0.02, mane_y - s_h*0.08), 2)
    
    # Eye
    pygame.draw.circle(sprite_surface, eye_color, 
        (int(head_x + s_w*0.08), int(head_y + s_h*0.08)), 2)
    
    # Legs
    for i, leg_offset in enumerate([0.05, 0.25, 0.35, 0.55]):
        leg_x = body_rect.left + body_rect.width * leg_offset
        leg_top = body_rect.bottom
        leg_bottom = leg_top + s_h*0.25
        pygame.draw.line(sprite_surface, leg_color, 
            (leg_x, leg_top), (leg_x, leg_bottom), 3)
    
    # Tail
    tail_start = (body_rect.right, body_rect.centery)
    tail_end = (tail_start[0] + s_w*0.15, tail_start[1] + s_h*0.1)
    pygame.draw.line(sprite_surface, mane_color, tail_start, tail_end, 4)
    
    return sprite_surface


def get_cow_sprite(size: Tuple[int, int] = (48, 40)) -> pygame.Surface:
    """Generate a Cow sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Colors
    body_color = (255, 255, 255)    # White
    spot_color = (0, 0, 0)          # Black spots
    udder_color = (255, 192, 203)   # Pink
    eye_color = (0, 0, 0)           # Black
    
    # Body (larger, rounder)
    body_rect = pygame.Rect(s_w*0.15, s_h*0.3, s_w*0.6, s_h*0.4)
    pygame.draw.ellipse(sprite_surface, body_color, body_rect)
    
    # Head
    head_x, head_y = body_rect.left - s_w*0.08, body_rect.top + s_h*0.05
    head_rect = pygame.Rect(head_x, head_y, s_w*0.25, s_h*0.2)
    pygame.draw.ellipse(sprite_surface, body_color, head_rect)
    
    # Spots
    spot_positions = [
        (body_rect.centerx - s_w*0.1, body_rect.top + s_h*0.05),
        (body_rect.centerx + s_w*0.05, body_rect.centery),
        (body_rect.left + s_w*0.05, body_rect.centery + s_h*0.05)
    ]
    for spot_x, spot_y in spot_positions:
        pygame.draw.ellipse(sprite_surface, spot_color, 
            (spot_x, spot_y, s_w*0.08, s_h*0.06))
    
    # Eye
    pygame.draw.circle(sprite_surface, eye_color, 
        (int(head_x + s_w*0.12), int(head_y + s_h*0.08)), 2)
    
    # Udder
    udder_rect = pygame.Rect(body_rect.centerx - s_w*0.05, body_rect.bottom - s_h*0.08, 
        s_w*0.1, s_h*0.08)
    pygame.draw.ellipse(sprite_surface, udder_color, udder_rect)
    
    # Legs
    for i, leg_offset in enumerate([0.1, 0.3, 0.5, 0.7]):
        leg_x = body_rect.left + body_rect.width * leg_offset
        leg_top = body_rect.bottom
        leg_bottom = leg_top + s_h*0.2
        pygame.draw.line(sprite_surface, spot_color, 
            (leg_x, leg_top), (leg_x, leg_bottom), 3)
    
    # Tail
    tail_start = (body_rect.right, body_rect.centery + s_h*0.1)
    tail_end = (tail_start[0] + s_w*0.1, tail_start[1] + s_h*0.15)
    pygame.draw.line(sprite_surface, spot_color, tail_start, tail_end, 2)
    
    return sprite_surface


def get_chicken_sprite(size: Tuple[int, int] = (24, 24)) -> pygame.Surface:
    """Generate a Chicken sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Colors
    body_color = (255, 255, 255)    # White
    comb_color = (255, 0, 0)        # Red comb
    beak_color = (255, 165, 0)      # Orange beak
    eye_color = (0, 0, 0)           # Black
    leg_color = (255, 165, 0)       # Orange legs
    
    # Body (plump and round)
    body_rect = pygame.Rect(s_w*0.2, s_h*0.4, s_w*0.5, s_h*0.4)
    pygame.draw.ellipse(sprite_surface, body_color, body_rect)
    
    # Head
    head_x, head_y = body_rect.left - s_w*0.05, body_rect.top - s_h*0.1
    pygame.draw.circle(sprite_surface, body_color, 
        (int(head_x), int(head_y)), int(s_w*0.12))
    
    # Comb (on top of head)
    comb_points = [
        (head_x - s_w*0.03, head_y - s_h*0.08),
        (head_x, head_y - s_h*0.15),
        (head_x + s_w*0.03, head_y - s_h*0.08)
    ]
    pygame.draw.polygon(sprite_surface, comb_color, comb_points)
    
    # Beak
    beak_points = [
        (head_x - s_w*0.08, head_y),
        (head_x - s_w*0.15, head_y + s_h*0.02),
        (head_x - s_w*0.08, head_y + s_h*0.04)
    ]
    pygame.draw.polygon(sprite_surface, beak_color, beak_points)
    
    # Eye
    pygame.draw.circle(sprite_surface, eye_color, 
        (int(head_x - s_w*0.02), int(head_y - s_h*0.02)), 1)
    
    # Wing outline
    wing_rect = pygame.Rect(body_rect.left + s_w*0.05, body_rect.top + s_w*0.05, 
        s_w*0.25, s_h*0.2)
    pygame.draw.ellipse(sprite_surface, (200, 200, 200), wing_rect, 1)
    
    # Legs
    for i, leg_offset in enumerate([0.3, 0.7]):
        leg_x = body_rect.left + body_rect.width * leg_offset
        leg_top = body_rect.bottom
        leg_bottom = leg_top + s_h*0.15
        pygame.draw.line(sprite_surface, leg_color, 
            (leg_x, leg_top), (leg_x, leg_bottom), 2)
        # Feet (simple claws)
        pygame.draw.line(sprite_surface, leg_color, 
            (leg_x - 2, leg_bottom), (leg_x + 2, leg_bottom), 1)
    
    return sprite_surface


def get_deer_sprite(size: Tuple[int, int] = (40, 48)) -> pygame.Surface:
    """Generate a Deer sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Colors
    body_color = (139, 69, 19)      # Brown
    antler_color = (101, 67, 33)    # Dark brown
    belly_color = (222, 184, 135)   # Light brown
    eye_color = (0, 0, 0)           # Black
    
    # Body (elegant, slender)
    body_rect = pygame.Rect(s_w*0.2, s_h*0.35, s_w*0.5, s_h*0.25)
    pygame.draw.ellipse(sprite_surface, body_color, body_rect)
    
    # Belly marking
    belly_rect = pygame.Rect(body_rect.left + s_w*0.05, body_rect.centery, 
        body_rect.width*0.8, body_rect.height*0.6)
    pygame.draw.ellipse(sprite_surface, belly_color, belly_rect)
    
    # Head and neck
    head_x, head_y = body_rect.left - s_w*0.1, body_rect.top - s_h*0.1
    pygame.draw.ellipse(sprite_surface, body_color, 
        (head_x, head_y, s_w*0.2, s_h*0.3))
    
    # Antlers (branched)
    antler_base_x, antler_base_y = head_x + s_w*0.08, head_y - s_h*0.05
    # Main antler stems
    pygame.draw.line(sprite_surface, antler_color, 
        (antler_base_x, antler_base_y), (antler_base_x - s_w*0.05, antler_base_y - s_h*0.15), 2)
    pygame.draw.line(sprite_surface, antler_color, 
        (antler_base_x + s_w*0.05, antler_base_y), (antler_base_x + s_w*0.1, antler_base_y - s_h*0.12), 2)
    # Antler points
    pygame.draw.line(sprite_surface, antler_color, 
        (antler_base_x - s_w*0.03, antler_base_y - s_h*0.1), (antler_base_x - s_w*0.08, antler_base_y - s_h*0.18), 1)
    pygame.draw.line(sprite_surface, antler_color, 
        (antler_base_x + s_w*0.08, antler_base_y - s_h*0.08), (antler_base_x + s_w*0.12, antler_base_y - s_h*0.15), 1)
    
    # Eye
    pygame.draw.circle(sprite_surface, eye_color, 
        (int(head_x + s_w*0.08), int(head_y + s_h*0.1)), 2)
    
    # Legs (long and slender)
    for i, leg_offset in enumerate([0.1, 0.3, 0.6, 0.8]):
        leg_x = body_rect.left + body_rect.width * leg_offset
        leg_top = body_rect.bottom
        leg_bottom = leg_top + s_h*0.3
        pygame.draw.line(sprite_surface, body_color, 
            (leg_x, leg_top), (leg_x, leg_bottom), 2)
    
    # Tail (small)
    tail_start = (body_rect.right, body_rect.centery + s_h*0.05)
    tail_end = (tail_start[0] + s_w*0.08, tail_start[1] + s_h*0.08)
    pygame.draw.line(sprite_surface, body_color, tail_start, tail_end, 2)
    
    return sprite_surface


def get_pig_sprite(size: Tuple[int, int] = (40, 32)) -> pygame.Surface:
    """Generate a Pig sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Colors
    body_color = (255, 192, 203)    # Pink
    snout_color = (255, 105, 180)   # Hot pink
    eye_color = (0, 0, 0)           # Black
    leg_color = (255, 105, 180)     # Hot pink
    
    # Body (round and plump)
    body_rect = pygame.Rect(s_w*0.15, s_h*0.3, s_w*0.6, s_h*0.4)
    pygame.draw.ellipse(sprite_surface, body_color, body_rect)
    
    # Head
    head_x, head_y = body_rect.left - s_w*0.05, body_rect.top + s_h*0.05
    head_rect = pygame.Rect(head_x, head_y, s_w*0.25, s_h*0.25)
    pygame.draw.ellipse(sprite_surface, body_color, head_rect)
    
    # Snout
    snout_x, snout_y = head_x - s_w*0.08, head_y + s_h*0.1
    pygame.draw.ellipse(sprite_surface, snout_color, 
        (snout_x, snout_y, s_w*0.12, s_h*0.08))
    # Nostrils
    pygame.draw.circle(sprite_surface, (0, 0, 0), 
        (int(snout_x + s_w*0.03), int(snout_y + s_h*0.04)), 1)
    pygame.draw.circle(sprite_surface, (0, 0, 0), 
        (int(snout_x + s_w*0.08), int(snout_y + s_h*0.04)), 1)
    
    # Eye
    pygame.draw.circle(sprite_surface, eye_color, 
        (int(head_x + s_w*0.1), int(head_y + s_h*0.08)), 2)
    
    # Ears (pointed)
    ear_points = [
        (head_x + s_w*0.05, head_y),
        (head_x + s_w*0.02, head_y - s_h*0.08),
        (head_x + s_w*0.12, head_y + s_h*0.02)
    ]
    pygame.draw.polygon(sprite_surface, body_color, ear_points)
    
    # Legs
    for i, leg_offset in enumerate([0.15, 0.35, 0.55, 0.75]):
        leg_x = body_rect.left + body_rect.width * leg_offset
        leg_top = body_rect.bottom
        leg_bottom = leg_top + s_h*0.2
        pygame.draw.line(sprite_surface, leg_color, 
            (leg_x, leg_top), (leg_x, leg_bottom), 3)
    
    # Curly tail
    tail_start = (body_rect.right, body_rect.centery)
    tail_points = [
        tail_start,
        (tail_start[0] + s_w*0.05, tail_start[1] - s_h*0.05),
        (tail_start[0] + s_w*0.08, tail_start[1]),
        (tail_start[0] + s_w*0.06, tail_start[1] + s_h*0.05)
    ]
    for i in range(len(tail_points)-1):
        pygame.draw.line(sprite_surface, body_color, tail_points[i], tail_points[i+1], 2)
    
    return sprite_surface


def get_dog_sprite(size: Tuple[int, int] = (36, 28)) -> pygame.Surface:
    """Generate a Stray Dog sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    body_color = (139, 69, 19)      # Brown
    ear_color = (101, 67, 33)       # Dark brown
    nose_color = (0, 0, 0)          # Black
    eye_color = (0, 0, 0)           # Black
    tongue_color = (255, 192, 203)  # Pink

    # Body
    body_rect = pygame.Rect(s_w*0.2, s_h*0.35, s_w*0.5, s_h*0.3)
    pygame.draw.ellipse(sprite_surface, body_color, body_rect)

    # Head
    head_x, head_y = body_rect.left - s_w*0.08, body_rect.top + s_h*0.02
    head_rect = pygame.Rect(head_x, head_y, s_w*0.25, s_h*0.2)
    pygame.draw.ellipse(sprite_surface, body_color, head_rect)

    # Snout
    snout_x, snout_y = head_x - s_w*0.05, head_y + s_h*0.08
    pygame.draw.ellipse(sprite_surface, body_color,
        (snout_x, snout_y, s_w*0.1, s_h*0.08))

    # Nose
    pygame.draw.circle(sprite_surface, nose_color,
        (int(snout_x + s_w*0.02), int(snout_y + s_h*0.02)), 2)

    # Tongue (hanging out)
    tongue_points = [
        (snout_x, snout_y + s_h*0.06),
        (snout_x - s_w*0.02, snout_y + s_h*0.12),
        (snout_x + s_w*0.02, snout_y + s_h*0.1)
    ]
    pygame.draw.polygon(sprite_surface, tongue_color, tongue_points)

    # Eye
    pygame.draw.circle(sprite_surface, eye_color,
        (int(head_x + s_w*0.08), int(head_y + s_h*0.06)), 2)

    # Ears (floppy)
    ear1_points = [
        (head_x + s_w*0.02, head_y),
        (head_x - s_w*0.02, head_y - s_h*0.05),
        (head_x + s_w*0.08, head_y + s_h*0.02)
    ]
    pygame.draw.polygon(sprite_surface, ear_color, ear1_points)

    # Legs
    for i, leg_offset in enumerate([0.15, 0.35, 0.55, 0.75]):
        leg_x = body_rect.left + body_rect.width * leg_offset
        leg_top = body_rect.bottom
        leg_bottom = leg_top + s_h*0.25
        pygame.draw.line(sprite_surface, body_color,
            (leg_x, leg_top), (leg_x, leg_bottom), 2)

    # Tail (wagging)
    tail_start = (body_rect.right, body_rect.centery)
    tail_end = (tail_start[0] + s_w*0.12, tail_start[1] - s_h*0.1)
    pygame.draw.line(sprite_surface, body_color, tail_start, tail_end, 3)

    return sprite_surface


def get_ice_wolf_sprite(size: Tuple[int, int] = (48, 36)) -> pygame.Surface:
    """Generate an Ice Wolf sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    body_color = (200, 220, 255)    # Icy blue-white
    fur_color = (180, 200, 240)     # Slightly darker blue-white
    eye_color = (100, 150, 255)     # Ice blue
    nose_color = (50, 50, 50)       # Dark gray
    frost_color = (240, 248, 255)   # Almost white frost

    # Body (wolf-like, muscular)
    body_rect = pygame.Rect(s_w*0.2, s_h*0.35, s_w*0.5, s_h*0.3)
    pygame.draw.ellipse(sprite_surface, body_color, body_rect)

    # Head
    head_x, head_y = body_rect.left - s_w*0.1, body_rect.top - s_h*0.05
    head_rect = pygame.Rect(head_x, head_y, s_w*0.3, s_h*0.25)
    pygame.draw.ellipse(sprite_surface, body_color, head_rect)

    # Snout (pointed)
    snout_x, snout_y = head_x - s_w*0.08, head_y + s_h*0.1
    snout_points = [
        (snout_x, snout_y),
        (snout_x - s_w*0.1, snout_y + s_h*0.05),
        (snout_x, snout_y + s_h*0.1)
    ]
    pygame.draw.polygon(sprite_surface, body_color, snout_points)

    # Nose
    pygame.draw.circle(sprite_surface, nose_color,
        (int(snout_x - s_w*0.08), int(snout_y + s_h*0.05)), 2)

    # Eyes (piercing ice blue)
    pygame.draw.circle(sprite_surface, eye_color,
        (int(head_x + s_w*0.08), int(head_y + s_h*0.08)), 3)
    pygame.draw.circle(sprite_surface, (255, 255, 255),
        (int(head_x + s_w*0.08), int(head_y + s_h*0.08)), 1)  # Highlight

    # Ears (pointed, alert)
    ear1_points = [
        (head_x + s_w*0.05, head_y),
        (head_x + s_w*0.02, head_y - s_h*0.1),
        (head_x + s_w*0.12, head_y + s_h*0.02)
    ]
    ear2_points = [
        (head_x + s_w*0.15, head_y),
        (head_x + s_w*0.12, head_y - s_h*0.08),
        (head_x + s_w*0.22, head_y + s_h*0.02)
    ]
    pygame.draw.polygon(sprite_surface, fur_color, ear1_points)
    pygame.draw.polygon(sprite_surface, fur_color, ear2_points)

    # Frost markings on fur
    frost_spots = [
        (body_rect.centerx - s_w*0.05, body_rect.top + s_h*0.05),
        (body_rect.centerx + s_w*0.08, body_rect.centery),
        (head_x + s_w*0.1, head_y + s_h*0.15)
    ]
    for spot_x, spot_y in frost_spots:
        pygame.draw.circle(sprite_surface, frost_color, (int(spot_x), int(spot_y)), 3)

    # Legs (powerful)
    for i, leg_offset in enumerate([0.1, 0.3, 0.6, 0.8]):
        leg_x = body_rect.left + body_rect.width * leg_offset
        leg_top = body_rect.bottom
        leg_bottom = leg_top + s_h*0.25
        pygame.draw.line(sprite_surface, body_color,
            (leg_x, leg_top), (leg_x, leg_bottom), 4)
        # Paws
        pygame.draw.circle(sprite_surface, fur_color, (int(leg_x), int(leg_bottom)), 3)

    # Tail (bushy)
    tail_start = (body_rect.right, body_rect.centery + s_h*0.05)
    tail_end = (tail_start[0] + s_w*0.15, tail_start[1] + s_h*0.1)
    pygame.draw.line(sprite_surface, body_color, tail_start, tail_end, 6)
    # Tail tip frost
    pygame.draw.circle(sprite_surface, frost_color, (int(tail_end[0]), int(tail_end[1])), 4)

    return sprite_surface


def get_forest_troll_sprite(size: Tuple[int, int] = (64, 80)) -> pygame.Surface:
    """Generate a Forest Troll sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    skin_color = (60, 80, 40)       # Dark green-brown
    moss_color = (34, 139, 34)      # Forest green moss
    eye_color = (255, 0, 0)         # Red eyes
    claw_color = (101, 67, 33)      # Dark brown claws
    bark_color = (101, 67, 33)      # Tree bark brown

    # Body (massive, hunched)
    body_rect = pygame.Rect(s_w*0.15, s_h*0.3, s_w*0.6, s_h*0.45)
    pygame.draw.ellipse(sprite_surface, skin_color, body_rect)

    # Moss patches on body
    moss_patches = [
        (body_rect.left + s_w*0.05, body_rect.top + s_h*0.1),
        (body_rect.centerx, body_rect.centery),
        (body_rect.right - s_w*0.1, body_rect.bottom - s_h*0.1)
    ]
    for moss_x, moss_y in moss_patches:
        pygame.draw.ellipse(sprite_surface, moss_color,
            (moss_x, moss_y, s_w*0.12, s_h*0.08))

    # Head (large, brutish)
    head_x, head_y = body_rect.centerx, body_rect.top - s_h*0.15
    pygame.draw.circle(sprite_surface, skin_color, (int(head_x), int(head_y)), int(s_w*0.18))

    # Eyes (small, beady, red)
    pygame.draw.circle(sprite_surface, eye_color,
        (int(head_x - s_w*0.05), int(head_y - s_h*0.02)), 3)
    pygame.draw.circle(sprite_surface, eye_color,
        (int(head_x + s_w*0.05), int(head_y - s_h*0.02)), 3)

    # Mouth (large, with tusks)
    mouth_rect = pygame.Rect(head_x - s_w*0.08, head_y + s_h*0.05, s_w*0.16, s_h*0.06)
    pygame.draw.ellipse(sprite_surface, (0, 0, 0), mouth_rect)
    # Tusks
    tusk_points = [
        (head_x - s_w*0.03, head_y + s_h*0.05),
        (head_x - s_w*0.05, head_y + s_h*0.12),
        (head_x - s_w*0.01, head_y + s_h*0.08)
    ]
    pygame.draw.polygon(sprite_surface, (255, 255, 255), tusk_points)

    # Arms (long, powerful)
    # Left arm
    arm_start = (body_rect.left + s_w*0.05, body_rect.top + s_h*0.1)
    arm_end = (arm_start[0] - s_w*0.15, arm_start[1] + s_h*0.3)
    pygame.draw.line(sprite_surface, skin_color, arm_start, arm_end, int(s_w*0.12))
    # Left hand with claws
    pygame.draw.circle(sprite_surface, skin_color, (int(arm_end[0]), int(arm_end[1])), int(s_w*0.08))
    for i in range(3):
        claw_x = arm_end[0] - s_w*0.05 + i * s_w*0.05
        claw_y = arm_end[1] + s_h*0.05
        pygame.draw.line(sprite_surface, claw_color,
            (claw_x, claw_y), (claw_x, claw_y + s_h*0.08), 2)

    # Right arm
    arm_start = (body_rect.right - s_w*0.05, body_rect.top + s_h*0.1)
    arm_end = (arm_start[0] + s_w*0.15, arm_start[1] + s_h*0.25)
    pygame.draw.line(sprite_surface, skin_color, arm_start, arm_end, int(s_w*0.12))
    # Right hand with claws
    pygame.draw.circle(sprite_surface, skin_color, (int(arm_end[0]), int(arm_end[1])), int(s_w*0.08))
    for i in range(3):
        claw_x = arm_end[0] - s_w*0.05 + i * s_w*0.05
        claw_y = arm_end[1] + s_h*0.05
        pygame.draw.line(sprite_surface, claw_color,
            (claw_x, claw_y), (claw_x, claw_y + s_h*0.08), 2)

    # Legs (thick, tree-trunk like)
    for i, leg_offset in enumerate([0.25, 0.65]):
        leg_x = body_rect.left + body_rect.width * leg_offset
        leg_top = body_rect.bottom - s_h*0.05
        leg_bottom = leg_top + s_h*0.2
        pygame.draw.line(sprite_surface, bark_color,
            (leg_x, leg_top), (leg_x, leg_bottom), int(s_w*0.15))
        # Feet (root-like)
        pygame.draw.ellipse(sprite_surface, bark_color,
            (leg_x - s_w*0.08, leg_bottom, s_w*0.16, s_h*0.08))

    # Moss on head
    pygame.draw.ellipse(sprite_surface, moss_color,
        (head_x - s_w*0.06, head_y - s_h*0.12, s_w*0.12, s_h*0.06))

    return sprite_surface


def get_wolf_sprite(size: Tuple[int, int] = (44, 32)) -> pygame.Surface:
    """Generate a regular Wolf sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    body_color = (105, 105, 105)    # Gray
    fur_color = (80, 80, 80)        # Darker gray
    eye_color = (255, 255, 0)       # Yellow
    nose_color = (0, 0, 0)          # Black
    teeth_color = (255, 255, 255)   # White

    # Body (lean, predatory)
    body_rect = pygame.Rect(s_w*0.2, s_h*0.35, s_w*0.5, s_h*0.3)
    pygame.draw.ellipse(sprite_surface, body_color, body_rect)

    # Head
    head_x, head_y = body_rect.left - s_w*0.08, body_rect.top - s_h*0.02
    head_rect = pygame.Rect(head_x, head_y, s_w*0.28, s_h*0.22)
    pygame.draw.ellipse(sprite_surface, body_color, head_rect)

    # Snout (pointed, aggressive)
    snout_x, snout_y = head_x - s_w*0.06, head_y + s_h*0.08
    snout_points = [
        (snout_x, snout_y),
        (snout_x - s_w*0.08, snout_y + s_h*0.04),
        (snout_x, snout_y + s_h*0.08)
    ]
    pygame.draw.polygon(sprite_surface, body_color, snout_points)

    # Nose
    pygame.draw.circle(sprite_surface, nose_color,
        (int(snout_x - s_w*0.06), int(snout_y + s_h*0.04)), 2)

    # Eyes (piercing yellow)
    pygame.draw.circle(sprite_surface, eye_color,
        (int(head_x + s_w*0.08), int(head_y + s_h*0.06)), 3)
    pygame.draw.circle(sprite_surface, (0, 0, 0),
        (int(head_x + s_w*0.08), int(head_y + s_h*0.06)), 1)  # Pupil

    # Mouth (slightly open, showing teeth)
    mouth_y = snout_y + s_h*0.06
    pygame.draw.line(sprite_surface, (0, 0, 0),
        (snout_x - s_w*0.04, mouth_y), (snout_x + s_w*0.02, mouth_y), 1)
    # Teeth
    for i in range(3):
        tooth_x = snout_x - s_w*0.03 + i * s_w*0.015
        pygame.draw.line(sprite_surface, teeth_color,
            (tooth_x, mouth_y), (tooth_x, mouth_y + s_h*0.02), 1)

    # Ears (pointed, alert)
    ear1_points = [
        (head_x + s_w*0.05, head_y),
        (head_x + s_w*0.02, head_y - s_h*0.08),
        (head_x + s_w*0.12, head_y + s_h*0.02)
    ]
    ear2_points = [
        (head_x + s_w*0.15, head_y),
        (head_x + s_w*0.12, head_y - s_h*0.06),
        (head_x + s_w*0.22, head_y + s_h*0.02)
    ]
    pygame.draw.polygon(sprite_surface, fur_color, ear1_points)
    pygame.draw.polygon(sprite_surface, fur_color, ear2_points)

    # Fur markings
    fur_spots = [
        (body_rect.centerx - s_w*0.08, body_rect.top + s_h*0.05),
        (body_rect.centerx + s_w*0.05, body_rect.centery),
        (head_x + s_w*0.1, head_y + s_h*0.12)
    ]
    for spot_x, spot_y in fur_spots:
        pygame.draw.ellipse(sprite_surface, fur_color,
            (spot_x, spot_y, s_w*0.06, s_h*0.04))

    # Legs (lean, muscular)
    for leg_offset in [0.1, 0.3, 0.6, 0.8]:
        leg_x = body_rect.left + body_rect.width * leg_offset
        leg_top = body_rect.bottom
        leg_bottom = leg_top + s_h*0.25
        pygame.draw.line(sprite_surface, body_color,
            (leg_x, leg_top), (leg_x, leg_bottom), 3)
        # Paws
        pygame.draw.circle(sprite_surface, fur_color, (int(leg_x), int(leg_bottom)), 2)

    # Tail (bushy, slightly raised)
    tail_start = (body_rect.right, body_rect.centery + s_h*0.02)
    tail_end = (tail_start[0] + s_w*0.12, tail_start[1] - s_h*0.05)
    pygame.draw.line(sprite_surface, body_color, tail_start, tail_end, 5)
    # Tail tip
    pygame.draw.circle(sprite_surface, fur_color, (int(tail_end[0]), int(tail_end[1])), 3)

    return sprite_surface


def get_goblin_basic_sprite(size: Tuple[int, int] = (30, 30)) -> pygame.Surface:
    """Generate a basic Goblin sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    skin_color = (80, 140, 80)      # Brighter green skin
    clothing_color = (139, 69, 19)  # Brown clothing
    eye_color = (255, 255, 0)       # Yellow eyes
    teeth_color = (255, 255, 255)   # White teeth

    # Body (side view, smaller than grunt)
    body_rect = pygame.Rect(s_w*0.3, s_h*0.45, s_w*0.35, s_h*0.3)
    pygame.draw.ellipse(sprite_surface, skin_color, body_rect)

    # Simple clothing
    cloth_rect = pygame.Rect(s_w*0.32, s_h*0.5, s_w*0.31, s_h*0.2)
    pygame.draw.rect(sprite_surface, clothing_color, cloth_rect)

    # Head (proportionally larger)
    head_x, head_y = body_rect.centerx, body_rect.top - s_h*0.12
    pygame.draw.circle(sprite_surface, skin_color, (int(head_x), int(head_y)), int(s_w*0.16))

    # Large pointed ears
    ear_points = [
        (head_x - s_w*0.12, head_y - s_h*0.05),
        (head_x - s_w*0.22, head_y - s_h*0.15),
        (head_x - s_w*0.06, head_y - s_h*0.08)
    ]
    pygame.draw.polygon(sprite_surface, skin_color, ear_points)

    # Eyes (large, mischievous)
    pygame.draw.circle(sprite_surface, eye_color,
        (int(head_x + s_w*0.04), int(head_y - s_h*0.02)), 3)
    pygame.draw.circle(sprite_surface, (0, 0, 0),
        (int(head_x + s_w*0.04), int(head_y - s_h*0.02)), 1)  # Pupil

    # Grinning mouth with sharp teeth
    mouth_rect = pygame.Rect(head_x - s_w*0.05, head_y + s_h*0.05, s_w*0.1, s_h*0.03)
    pygame.draw.ellipse(sprite_surface, (0, 0, 0), mouth_rect)
    # Sharp teeth
    for i in range(4):
        tooth_x = head_x - s_w*0.04 + i * s_w*0.02
        pygame.draw.line(sprite_surface, teeth_color,
            (tooth_x, head_y + s_h*0.05), (tooth_x, head_y + s_h*0.07), 1)

    # Arms (thin, wiry)
    # Left arm
    pygame.draw.line(sprite_surface, skin_color,
        (body_rect.left, body_rect.centery),
        (body_rect.left - s_w*0.08, body_rect.centery + s_h*0.1), 3)
    # Right arm
    pygame.draw.line(sprite_surface, skin_color,
        (body_rect.right, body_rect.centery),
        (body_rect.right + s_w*0.08, body_rect.centery + s_h*0.08), 3)

    # Legs (short, stubby)
    leg_y = body_rect.bottom
    pygame.draw.rect(sprite_surface, skin_color,
        (body_rect.centerx - s_w*0.06, leg_y, s_w*0.05, s_h*0.12))
    pygame.draw.rect(sprite_surface, skin_color,
        (body_rect.centerx + s_w*0.02, leg_y, s_w*0.05, s_h*0.12))

    return sprite_surface


# Icon generation functions for map editor
def get_goblin_grunt_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a Goblin Grunt icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Simple goblin head with weapon
    head_color = (60, 120, 60)      # Green skin
    weapon_color = (192, 192, 192)  # Silver blade
    eye_color = (255, 0, 0)         # Red eyes

    # Head
    pygame.draw.circle(icon_surface, head_color, (int(s_w*0.4), int(s_h*0.4)), int(s_w*0.2))

    # Pointed ear
    ear_points = [
        (s_w*0.25, s_h*0.35),
        (s_w*0.15, s_h*0.25),
        (s_w*0.3, s_h*0.3)
    ]
    pygame.draw.polygon(icon_surface, head_color, ear_points)

    # Eye
    pygame.draw.circle(icon_surface, eye_color, (int(s_w*0.42), int(s_h*0.38)), 2)

    # Weapon (sword)
    pygame.draw.line(icon_surface, weapon_color,
        (s_w*0.6, s_h*0.3), (s_w*0.8, s_h*0.1), 3)

    return icon_surface


def get_goblin_shaman_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a Goblin Shaman icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Shaman with staff and orb
    head_color = (40, 100, 40)      # Darker green skin
    staff_color = (139, 69, 19)     # Brown staff
    orb_color = (0, 255, 255)       # Cyan magical orb

    # Head
    pygame.draw.circle(icon_surface, head_color, (int(s_w*0.4), int(s_h*0.4)), int(s_w*0.18))

    # Staff
    pygame.draw.line(icon_surface, staff_color,
        (s_w*0.65, s_h*0.2), (s_w*0.65, s_h*0.8), 4)

    # Magical orb
    pygame.draw.circle(icon_surface, orb_color, (int(s_w*0.65), int(s_h*0.2)), 6)

    return icon_surface


def get_goblin_basic_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a basic Goblin icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Simple goblin head
    head_color = (80, 140, 80)      # Brighter green skin
    eye_color = (255, 255, 0)       # Yellow eyes
    teeth_color = (255, 255, 255)   # White teeth

    # Head
    pygame.draw.circle(icon_surface, head_color, (int(s_w*0.5), int(s_h*0.5)), int(s_w*0.25))

    # Large pointed ears
    ear_points = [
        (s_w*0.3, s_h*0.45),
        (s_w*0.15, s_h*0.3),
        (s_w*0.35, s_h*0.4)
    ]
    pygame.draw.polygon(icon_surface, head_color, ear_points)

    # Eye
    pygame.draw.circle(icon_surface, eye_color, (int(s_w*0.55), int(s_h*0.48)), 3)

    # Grinning mouth
    pygame.draw.arc(icon_surface, teeth_color,
        (s_w*0.45, s_h*0.55, s_w*0.15, s_h*0.1), 0, 3.14, 2)

    return icon_surface


def get_green_drake_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a Green Drake icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Dragon head with wing
    body_color = (34, 139, 34)      # Forest Green
    wing_color = (0, 100, 0)        # Darker green
    eye_color = (255, 255, 0)       # Yellow

    # Dragon head
    head_points = [
        (s_w*0.3, s_h*0.5),
        (s_w*0.1, s_h*0.45),
        (s_w*0.05, s_h*0.55),
        (s_w*0.25, s_h*0.6)
    ]
    pygame.draw.polygon(icon_surface, body_color, head_points)

    # Wing
    wing_points = [
        (s_w*0.4, s_h*0.3),
        (s_w*0.7, s_h*0.2),
        (s_w*0.8, s_h*0.5),
        (s_w*0.5, s_h*0.6)
    ]
    pygame.draw.polygon(icon_surface, wing_color, wing_points)

    # Eye
    pygame.draw.circle(icon_surface, eye_color, (int(s_w*0.2), int(s_h*0.48)), 3)

    return icon_surface


def get_sand_scorpion_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a Sand Scorpion icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Scorpion body with tail and stinger
    body_color = (194, 154, 108)    # Sandy brown
    stinger_color = (80, 40, 20)    # Dark brown

    # Body segments
    pygame.draw.ellipse(icon_surface, body_color,
        (s_w*0.2, s_h*0.4, s_w*0.3, s_h*0.2))
    pygame.draw.ellipse(icon_surface, body_color,
        (s_w*0.45, s_h*0.45, s_w*0.2, s_h*0.15))

    # Claws
    claw_points = [
        (s_w*0.15, s_h*0.35),
        (s_w*0.05, s_h*0.25),
        (s_w*0.1, s_h*0.4)
    ]
    pygame.draw.polygon(icon_surface, body_color, claw_points)

    # Tail with stinger
    pygame.draw.line(icon_surface, body_color,
        (s_w*0.65, s_h*0.5), (s_w*0.85, s_h*0.3), 3)
    pygame.draw.circle(icon_surface, stinger_color, (int(s_w*0.85), int(s_h*0.3)), 3)

    return icon_surface


def get_ice_wolf_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate an Ice Wolf icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Wolf head with ice effects
    body_color = (200, 220, 255)    # Icy blue-white
    eye_color = (100, 150, 255)     # Ice blue
    frost_color = (240, 248, 255)   # Almost white frost

    # Wolf head
    head_points = [
        (s_w*0.3, s_h*0.4),
        (s_w*0.1, s_h*0.45),
        (s_w*0.05, s_h*0.55),
        (s_w*0.25, s_h*0.6),
        (s_w*0.4, s_h*0.5)
    ]
    pygame.draw.polygon(icon_surface, body_color, head_points)

    # Pointed ears
    ear_points = [
        (s_w*0.35, s_h*0.3),
        (s_w*0.3, s_h*0.2),
        (s_w*0.4, s_h*0.35)
    ]
    pygame.draw.polygon(icon_surface, body_color, ear_points)

    # Ice blue eye
    pygame.draw.circle(icon_surface, eye_color, (int(s_w*0.22), int(s_h*0.45)), 3)

    # Frost markings
    pygame.draw.circle(icon_surface, frost_color, (int(s_w*0.6), int(s_h*0.4)), 4)
    pygame.draw.circle(icon_surface, frost_color, (int(s_w*0.7), int(s_h*0.6)), 3)

    return icon_surface


def get_forest_troll_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a Forest Troll icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Troll head with moss
    skin_color = (60, 80, 40)       # Dark green-brown
    moss_color = (34, 139, 34)      # Forest green moss
    eye_color = (255, 0, 0)         # Red eyes

    # Large brutish head
    pygame.draw.circle(icon_surface, skin_color, (int(s_w*0.5), int(s_h*0.5)), int(s_w*0.3))

    # Moss patches
    pygame.draw.ellipse(icon_surface, moss_color,
        (s_w*0.4, s_h*0.3, s_w*0.2, s_h*0.15))
    pygame.draw.ellipse(icon_surface, moss_color,
        (s_w*0.6, s_h*0.6, s_w*0.15, s_h*0.1))

    # Small red eyes
    pygame.draw.circle(icon_surface, eye_color, (int(s_w*0.45), int(s_h*0.45)), 2)
    pygame.draw.circle(icon_surface, eye_color, (int(s_w*0.55), int(s_h*0.45)), 2)

    # Tusk
    tusk_points = [
        (s_w*0.47, s_h*0.55),
        (s_w*0.45, s_h*0.65),
        (s_w*0.49, s_h*0.6)
    ]
    pygame.draw.polygon(icon_surface, (255, 255, 255), tusk_points)

    return icon_surface


def get_wolf_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a Wolf icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Wolf head
    body_color = (105, 105, 105)    # Gray
    eye_color = (255, 255, 0)       # Yellow
    teeth_color = (255, 255, 255)   # White

    # Wolf head
    head_points = [
        (s_w*0.3, s_h*0.4),
        (s_w*0.1, s_h*0.45),
        (s_w*0.05, s_h*0.55),
        (s_w*0.25, s_h*0.6),
        (s_w*0.4, s_h*0.5)
    ]
    pygame.draw.polygon(icon_surface, body_color, head_points)

    # Pointed ears
    ear_points = [
        (s_w*0.35, s_h*0.3),
        (s_w*0.3, s_h*0.2),
        (s_w*0.4, s_h*0.35)
    ]
    pygame.draw.polygon(icon_surface, body_color, ear_points)

    # Yellow eye
    pygame.draw.circle(icon_surface, eye_color, (int(s_w*0.22), int(s_h*0.45)), 3)
    pygame.draw.circle(icon_surface, (0, 0, 0), (int(s_w*0.22), int(s_h*0.45)), 1)  # Pupil

    # Teeth
    pygame.draw.line(icon_surface, teeth_color,
        (s_w*0.15, s_h*0.52), (s_w*0.12, s_h*0.58), 2)

    return icon_surface


# Animal icons (for peaceful creatures)
def get_horse_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a Horse icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Horse head with mane
    body_color = (139, 69, 19)      # Brown
    mane_color = (101, 67, 33)      # Dark brown

    # Horse head
    pygame.draw.ellipse(icon_surface, body_color,
        (s_w*0.2, s_w*0.3, s_w*0.4, s_h*0.5))

    # Mane
    for i in range(4):
        mane_x = s_w*0.25 + i * s_w*0.08
        pygame.draw.line(icon_surface, mane_color,
            (mane_x, s_h*0.25), (mane_x - s_w*0.03, s_h*0.15), 2)

    # Eye
    pygame.draw.circle(icon_surface, (0, 0, 0), (int(s_w*0.35), int(s_h*0.45)), 2)

    return icon_surface


def get_cow_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a Cow icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Cow head with spots
    body_color = (255, 255, 255)    # White
    spot_color = (0, 0, 0)          # Black spots

    # Cow head
    pygame.draw.ellipse(icon_surface, body_color,
        (s_w*0.25, s_h*0.3, s_w*0.5, s_h*0.4))

    # Spots
    pygame.draw.ellipse(icon_surface, spot_color,
        (s_w*0.3, s_h*0.35, s_w*0.15, s_h*0.1))
    pygame.draw.ellipse(icon_surface, spot_color,
        (s_w*0.5, s_h*0.5, s_w*0.12, s_h*0.08))

    # Eye
    pygame.draw.circle(icon_surface, (0, 0, 0), (int(s_w*0.4), int(s_h*0.45)), 2)

    return icon_surface


def get_chicken_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a Chicken icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Chicken with comb
    body_color = (255, 255, 255)    # White
    comb_color = (255, 0, 0)        # Red comb
    beak_color = (255, 165, 0)      # Orange beak

    # Chicken body
    pygame.draw.ellipse(icon_surface, body_color,
        (s_w*0.3, s_h*0.4, s_w*0.4, s_h*0.3))

    # Head
    pygame.draw.circle(icon_surface, body_color, (int(s_w*0.4), int(s_h*0.35)), int(s_w*0.12))

    # Comb
    comb_points = [
        (s_w*0.37, s_h*0.25),
        (s_w*0.4, s_h*0.18),
        (s_w*0.43, s_h*0.25)
    ]
    pygame.draw.polygon(icon_surface, comb_color, comb_points)

    # Beak
    beak_points = [
        (s_w*0.32, s_h*0.35),
        (s_w*0.25, s_h*0.37),
        (s_w*0.32, s_h*0.39)
    ]
    pygame.draw.polygon(icon_surface, beak_color, beak_points)

    return icon_surface


def get_deer_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a Deer icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Deer head with antlers
    body_color = (139, 69, 19)      # Brown
    antler_color = (101, 67, 33)    # Dark brown

    # Deer head
    pygame.draw.ellipse(icon_surface, body_color,
        (s_w*0.3, s_h*0.4, s_w*0.4, s_h*0.3))

    # Antlers
    pygame.draw.line(icon_surface, antler_color,
        (s_w*0.45, s_h*0.3), (s_w*0.4, s_h*0.15), 2)
    pygame.draw.line(icon_surface, antler_color,
        (s_w*0.55, s_h*0.3), (s_w*0.6, s_h*0.15), 2)
    # Antler points
    pygame.draw.line(icon_surface, antler_color,
        (s_w*0.42, s_h*0.2), (s_w*0.38, s_h*0.12), 1)
    pygame.draw.line(icon_surface, antler_color,
        (s_w*0.58, s_h*0.2), (s_w*0.62, s_h*0.12), 1)

    # Eye
    pygame.draw.circle(icon_surface, (0, 0, 0), (int(s_w*0.45), int(s_h*0.48)), 2)

    return icon_surface


def get_pig_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a Pig icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Pink pig with snout
    body_color = (255, 192, 203)    # Pink
    snout_color = (255, 105, 180)   # Hot pink

    # Pig head
    pygame.draw.ellipse(icon_surface, body_color,
        (s_w*0.25, s_h*0.35, s_w*0.5, s_h*0.4))

    # Snout
    pygame.draw.ellipse(icon_surface, snout_color,
        (s_w*0.15, s_h*0.5, s_w*0.2, s_h*0.15))

    # Nostrils
    pygame.draw.circle(icon_surface, (0, 0, 0), (int(s_w*0.2), int(s_h*0.55)), 1)
    pygame.draw.circle(icon_surface, (0, 0, 0), (int(s_w*0.28), int(s_h*0.55)), 1)

    # Eye
    pygame.draw.circle(icon_surface, (0, 0, 0), (int(s_w*0.4), int(s_h*0.48)), 2)

    # Ear
    ear_points = [
        (s_w*0.45, s_h*0.35),
        (s_w*0.42, s_h*0.25),
        (s_w*0.52, s_h*0.37)
    ]
    pygame.draw.polygon(icon_surface, body_color, ear_points)

    return icon_surface


def get_dog_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a Dog icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Dog head with floppy ear
    body_color = (139, 69, 19)      # Brown
    ear_color = (101, 67, 33)       # Dark brown
    tongue_color = (255, 192, 203)  # Pink

    # Dog head
    pygame.draw.ellipse(icon_surface, body_color,
        (s_w*0.3, s_h*0.35, s_w*0.4, s_h*0.3))

    # Snout
    pygame.draw.ellipse(icon_surface, body_color,
        (s_w*0.15, s_h*0.5, s_w*0.2, s_h*0.15))

    # Floppy ear
    ear_points = [
        (s_w*0.32, s_h*0.35),
        (s_w*0.25, s_h*0.25),
        (s_w*0.4, s_h*0.37)
    ]
    pygame.draw.polygon(icon_surface, ear_color, ear_points)

    # Eye
    pygame.draw.circle(icon_surface, (0, 0, 0), (int(s_w*0.42), int(s_h*0.45)), 2)

    # Tongue
    tongue_points = [
        (s_w*0.2, s_h*0.6),
        (s_w*0.18, s_h*0.68),
        (s_w*0.22, s_h*0.65)
    ]
    pygame.draw.polygon(icon_surface, tongue_color, tongue_points)

    return icon_surface
