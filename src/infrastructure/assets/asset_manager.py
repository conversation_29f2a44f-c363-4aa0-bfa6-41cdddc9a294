"""
Asset Manager Implementation

This class implements the IAssetManager interface and manages all game assets.
It handles caching, generation, and retrieval of procedural assets.
"""

import pygame
from typing import Dict, List, Any, Optional
from src.application.interfaces import IAssetManager
from src.infrastructure.logging import get_logger
from .registry import get_asset_generator, is_asset_registered


class AssetManager(IAssetManager):
    """
    Concrete implementation of IAssetManager for Pygame.
    
    This class manages the generation and caching of procedural assets.
    It serves as the bridge between abstract asset IDs and concrete Pygame surfaces.
    """
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self._cache: Dict[str, pygame.Surface] = {}
        self._default_sizes: Dict[str, tuple[int, int]] = {
            # Monster default sizes
            "monster.goblin.grunt.side": (32, 32),
            "monster.goblin.shaman.side": (32, 32),
            "monster.drake.green.side": (64, 64),
            "monster.scorpion.sand.side": (48, 32),
            
            # Tile default sizes
            "tile.wall.stone": (32, 32),
            "tile.floor.dirt": (32, 32),
            "tile.floor.sand": (32, 32),
            "tile.floor.grass": (32, 32),
            "tile.floor.gravel": (32, 32),
            "tile.path.cobblestone": (32, 32),
            "tile.path.dirt_road": (32, 32),
            "tile.water.deep": (32, 32),
            "tile.water.swamp": (32, 32),
            "tile.tree.oak": (32, 32),
            "tile.tree.willow": (32, 32),
            "tile.door.wooden": (32, 32),
            "tile.door.wooden.open": (32, 32),
            "tile.bridge.wooden": (32, 32),
            "tile.structure.wood_fence": (32, 32),
            "tile.structure.wood_walkway": (32, 32),
            "tile.mountain.rock": (32, 32),
            "tile.hill.grass": (32, 32),
            "tile.exit.portal": (32, 32),
            "tile.exit.magic_door": (32, 32),
            "tile.missing_asset": (32, 32),
            
            # Item default sizes
            "item.weapon.sword.rusty": (24, 24),
            "item.weapon.sword.iron": (24, 24),
            "item.weapon.bow.wooden": (16, 24),
            "item.armor.leather.chest": (20, 20),
            "item.armor.iron.helmet": (18, 18),
            "item.consumable.potion.health": (16, 16),
            "item.consumable.potion.mana": (16, 16),
            "item.consumable.food.bread": (16, 12),
            "item.treasure.coin.gold": (12, 12),
            "item.treasure.gem.ruby": (12, 12),
            "item.quest.key.ancient": (16, 20),
            
            # Player and effects
            "player.hero": (128, 128),  # Updated to use base_entity_size
            "player.base": (128, 128),
            "ui.damage_number": (24, 16),
            "effect.attack.slash": (32, 32),
        }
    
    def get_asset(self, asset_id: str, size: Optional[tuple[int, int]] = None) -> pygame.Surface:
        """
        Get an asset by its ID.

        Args:
            asset_id: The asset ID to retrieve
            size: Optional size override. If not provided, uses default size.

        Returns:
            A pygame.Surface containing the asset

        Raises:
            KeyError: If the asset ID is not registered
            RuntimeError: If asset generation fails
        """
        # Import here to avoid circular imports
        from .registry import is_dynamic_player_asset

        # Determine cache key (includes size for uniqueness)
        actual_size = size or self._default_sizes.get(asset_id, (128, 128))
        cache_key = f"{asset_id}_{actual_size[0]}x{actual_size[1]}"

        # Check cache first
        if cache_key in self._cache:
            return self._cache[cache_key]

        # Handle dynamic player assets differently
        if is_dynamic_player_asset(asset_id):
            # Dynamic player assets are not pre-registered, so we can't generate them here
            # They should be generated through get_equipped_player_asset method
            raise KeyError(f"Dynamic player asset '{asset_id}' should be generated through get_equipped_player_asset method")

        # Verify asset is registered
        if not is_asset_registered(asset_id):
            raise KeyError(f"Asset ID '{asset_id}' is not registered")

        try:
            # Generate the asset
            generator = get_asset_generator(asset_id)
            surface = generator(size=actual_size)

            # Cache the result
            self._cache[cache_key] = surface

            return surface

        except Exception as e:
            raise RuntimeError(f"Failed to generate asset '{asset_id}': {e}") from e

    def get_equipped_player_asset(self, equipment_ids: Dict[str, Optional[str]],
                                 size: Optional[tuple[int, int]] = None) -> pygame.Surface:
        """
        Get a player asset with specific equipment combination.

        Args:
            equipment_ids: Dictionary mapping equipment slots to item IDs
            size: Optional size override. If not provided, uses default player size.

        Returns:
            A pygame.Surface containing the equipped player sprite
        """
        # Import here to avoid circular imports
        from .registry import generate_dynamic_player_asset_id, get_dynamic_player_asset_generator

        # Generate unique asset ID for this equipment combination
        dynamic_asset_id = generate_dynamic_player_asset_id(equipment_ids)

        # Determine cache key (includes size for uniqueness)
        actual_size = size or self._default_sizes.get("player.hero", (128, 128))
        cache_key = f"{dynamic_asset_id}_{actual_size[0]}x{actual_size[1]}"

        # Check cache first
        if cache_key in self._cache:
            return self._cache[cache_key]

        try:
            # Generate the equipped player asset
            generator = get_dynamic_player_asset_generator(equipment_ids)
            surface = generator(size=actual_size)

            # Cache the result
            self._cache[cache_key] = surface

            return surface

        except Exception as e:
            raise RuntimeError(f"Failed to generate equipped player asset: {e}") from e

    def preload_assets(self, asset_ids: List[str]) -> None:
        """
        Preload a list of assets for performance.
        
        Args:
            asset_ids: List of asset IDs to preload
        """
        for asset_id in asset_ids:
            try:
                self.get_asset(asset_id)  # This will cache the asset
            except Exception as e:
                self.logger.warning(f"Failed to preload asset '{asset_id}': {e}")
    
    def clear_cache(self) -> None:
        """Clear the asset cache to free memory."""
        self._cache.clear()

    def clear_player_equipment_cache(self) -> None:
        """Clear only player equipment-related cache entries to free memory."""
        keys_to_remove = [key for key in self._cache.keys() if "player.equipped." in key]
        for key in keys_to_remove:
            del self._cache[key]
    
    def get_cache_size(self) -> int:
        """Get the number of cached assets."""
        return len(self._cache)
    
    def get_cache_info(self) -> Dict[str, Any]:
        """Get information about the asset cache."""
        total_memory = 0
        asset_count_by_type = {}
        
        for cache_key, surface in self._cache.items():
            # Estimate memory usage (width * height * 4 bytes per pixel for RGBA)
            surface_memory = surface.get_width() * surface.get_height() * 4
            total_memory += surface_memory
            
            # Count by asset type
            asset_type = cache_key.split('.')[0] if '.' in cache_key else 'unknown'
            asset_count_by_type[asset_type] = asset_count_by_type.get(asset_type, 0) + 1
        
        return {
            "total_assets": len(self._cache),
            "estimated_memory_bytes": total_memory,
            "estimated_memory_mb": total_memory / (1024 * 1024),
            "assets_by_type": asset_count_by_type
        }
    
    def preload_level_assets(self, level_id: str) -> None:
        """
        Preload all assets commonly needed for a specific level.
        
        Args:
            level_id: The level ID to preload assets for
        """
        # Common assets needed for most levels
        common_assets = [
            "player.hero",
            "tile.wall.stone",
            "tile.floor.dirt",
            "tile.exit.portal",
            "item.treasure.coin.gold",
            "item.consumable.potion.health",
            "effect.attack.slash",
        ]
        
        # Level-specific asset sets
        level_assets = {
            "town_caledon": [
                "tile.door.wooden",
                "tile.bridge.wooden",
                "monster.goblin.grunt.side",
            ],
            "desert_oasis": [
                "tile.floor.sand",
                "monster.scorpion.sand.side",
                "tile.water.deep",
            ],
            "forest_depths": [
                "tile.tree.oak",
                "monster.drake.green.side",
                "monster.goblin.shaman.side",
            ],
        }
        
        # Combine common and level-specific assets
        assets_to_load = common_assets + level_assets.get(level_id, [])
        
        self.logger.info(f"Preloading {len(assets_to_load)} assets for level '{level_id}'...")
        self.preload_assets(assets_to_load)
        
        cache_info = self.get_cache_info()
        self.logger.info(f"Asset cache now contains {cache_info['total_assets']} assets "
              f"({cache_info['estimated_memory_mb']:.1f} MB)")
    
    def get_asset_with_fallback(self, asset_id: str, fallback_id: str = "tile.missing_asset") -> pygame.Surface:
        """
        Get an asset with a fallback if the primary asset fails to load.

        Args:
            asset_id: Primary asset ID to try
            fallback_id: Fallback asset ID if primary fails

        Returns:
            The requested asset, or the fallback asset if primary fails
        """
        try:
            return self.get_asset(asset_id)
        except Exception as e:
            self.logger.warning(f"Failed to load asset '{asset_id}', using fallback '{fallback_id}': {e}")
            try:
                return self.get_asset(fallback_id)
            except Exception as fallback_error:
                self.logger.error(f"Even fallback asset '{fallback_id}' failed to load: {fallback_error}")
                # Return a simple colored rectangle as last resort
                surface = pygame.Surface((32, 32))
                surface.fill((255, 0, 255))  # Magenta "missing texture" color
                return surface
