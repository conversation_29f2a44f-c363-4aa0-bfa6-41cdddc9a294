"""
Procedural Tile Asset Generation

This module contains functions to procedurally generate tile sprites using Pygame.
"""

import pygame
from typing import <PERSON><PERSON>


def get_stone_wall_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a stone wall tile procedurally."""
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Colors
    stone_color = (128, 128, 128)       # Gray stone
    mortar_color = (96, 96, 96)         # Dark gray mortar
    highlight_color = (160, 160, 160)   # Light gray highlights
    
    # Fill base
    tile_surface.fill(stone_color)
    
    # Draw stone blocks
    block_w, block_h = s_w // 3, s_h // 3
    for y in range(0, s_h, block_h):
        for x in range(0, s_w, block_w):
            # Add some variation in block positioning
            offset_x = (x // block_w) % 2 * (block_w // 2)
            block_rect = pygame.Rect(x + offset_x, y, block_w - 2, block_h - 2)
            
            # Draw block
            pygame.draw.rect(tile_surface, stone_color, block_rect)
            
            # Add highlight
            pygame.draw.line(tile_surface, highlight_color, 
                           (block_rect.left, block_rect.top), 
                           (block_rect.right, block_rect.top), 1)
            pygame.draw.line(tile_surface, highlight_color, 
                           (block_rect.left, block_rect.top), 
                           (block_rect.left, block_rect.bottom), 1)
            
            # Add mortar lines
            pygame.draw.rect(tile_surface, mortar_color, block_rect, 1)
    
    return tile_surface


def get_dirt_floor_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a dirt floor tile procedurally."""
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Colors
    dirt_color = (139, 69, 19)          # Saddle brown
    dark_dirt_color = (101, 67, 33)     # Dark brown
    light_dirt_color = (160, 82, 45)    # Light brown
    
    # Fill base
    tile_surface.fill(dirt_color)
    
    # Add texture with random spots
    import random
    random.seed(42)  # Consistent randomness
    
    for _ in range(20):
        x = random.randint(0, s_w - 3)
        y = random.randint(0, s_h - 3)
        color = random.choice([dark_dirt_color, light_dirt_color])
        size_var = random.randint(1, 3)
        pygame.draw.circle(tile_surface, color, (x, y), size_var)
    
    return tile_surface


def get_sand_floor_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a sand floor tile procedurally."""
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors - More yellow sand
    sand_color = (238, 203, 173)        # Peach puff (more yellow)
    dark_sand_color = (205, 175, 149)   # Darker yellow sand
    light_sand_color = (255, 228, 196)  # Bisque (light yellow sand)

    # Fill base
    tile_surface.fill(sand_color)

    # Add sand texture
    import random
    random.seed(123)  # Consistent randomness

    for _ in range(30):
        x = random.randint(0, s_w - 2)
        y = random.randint(0, s_h - 2)
        color = random.choice([dark_sand_color, light_sand_color])
        pygame.draw.circle(tile_surface, color, (x, y), 1)

    return tile_surface


def get_water_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a water tile procedurally."""
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Colors
    water_color = (0, 100, 200)         # Blue water
    dark_water_color = (0, 70, 150)     # Darker blue
    light_water_color = (100, 150, 255) # Lighter blue
    
    # Fill base
    tile_surface.fill(water_color)
    
    # Add wave patterns
    import math
    for y in range(0, s_h, 4):
        for x in range(s_w):
            wave_offset = int(math.sin(x * 0.3 + y * 0.2) * 2)
            if (x + wave_offset) % 8 < 4:
                color = light_water_color if (x + y) % 2 == 0 else dark_water_color
                pygame.draw.circle(tile_surface, color, (x, y), 1)
    
    return tile_surface


def get_tree_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a tree tile procedurally."""
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    trunk_color = (101, 67, 33)         # Brown trunk
    leaf_color = (34, 139, 34)          # Green leaves
    dark_leaf_color = (0, 100, 0)       # Dark green
    grass_color = (34, 139, 34)         # Forest green background

    # Fill with grass background
    tile_surface.fill(grass_color)
    
    # Trunk
    trunk_rect = pygame.Rect(s_w*0.4, s_h*0.6, s_w*0.2, s_h*0.4)
    pygame.draw.rect(tile_surface, trunk_color, trunk_rect)
    
    # Leaves (multiple circles for full canopy)
    leaf_centers = [
        (s_w*0.5, s_h*0.3),     # Center
        (s_w*0.35, s_h*0.25),   # Left
        (s_w*0.65, s_h*0.25),   # Right
        (s_w*0.45, s_h*0.15),   # Top left
        (s_w*0.55, s_h*0.15),   # Top right
    ]
    
    for center_x, center_y in leaf_centers:
        pygame.draw.circle(tile_surface, leaf_color, (int(center_x), int(center_y)), int(s_w*0.15))
        # Add some darker spots for depth
        pygame.draw.circle(tile_surface, dark_leaf_color, 
                         (int(center_x - s_w*0.05), int(center_y + s_h*0.05)), int(s_w*0.08))
    
    return tile_surface


def get_wooden_door_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a wooden door tile procedurally (closed state)."""
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    wood_color = (139, 69, 19)          # Brown wood
    dark_wood_color = (101, 67, 33)     # Dark brown
    metal_color = (192, 192, 192)       # Silver metal

    # Door frame
    frame_rect = pygame.Rect(s_w*0.1, s_h*0.1, s_w*0.8, s_h*0.8)
    pygame.draw.rect(tile_surface, dark_wood_color, frame_rect)

    # Door panels
    door_rect = pygame.Rect(s_w*0.15, s_w*0.15, s_w*0.7, s_h*0.7)
    pygame.draw.rect(tile_surface, wood_color, door_rect)

    # Wood grain lines
    for i in range(3):
        y_pos = door_rect.top + (i + 1) * door_rect.height // 4
        pygame.draw.line(tile_surface, dark_wood_color,
                        (door_rect.left + 2, y_pos), (door_rect.right - 2, y_pos), 1)

    # Door handle
    handle_pos = (int(door_rect.right - s_w*0.1), int(door_rect.centery))
    pygame.draw.circle(tile_surface, metal_color, handle_pos, 2)

    return tile_surface


def get_wooden_door_open_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate an open wooden door tile procedurally."""
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    wood_color = (139, 69, 19)          # Brown wood
    dark_wood_color = (101, 67, 33)     # Dark brown
    metal_color = (192, 192, 192)       # Silver metal
    floor_color = (139, 69, 19)         # Dirt floor color (same as dirt tile)

    # Fill with floor color as base (door is open, showing floor)
    tile_surface.fill(floor_color)

    # Add floor texture similar to dirt floor
    import random
    random.seed(42)  # Consistent randomness

    dark_floor_color = (101, 67, 33)     # Dark brown
    light_floor_color = (160, 82, 45)    # Light brown

    for _ in range(15):  # Less texture since door might partially cover
        x = random.randint(0, s_w - 3)
        y = random.randint(0, s_h - 3)
        color = random.choice([dark_floor_color, light_floor_color])
        size_var = random.randint(1, 2)
        pygame.draw.circle(tile_surface, color, (x, y), size_var)

    # Draw the door frame (still visible)
    frame_rect = pygame.Rect(s_w*0.05, s_h*0.05, s_w*0.15, s_h*0.9)
    pygame.draw.rect(tile_surface, dark_wood_color, frame_rect)

    # Draw the opened door (swung to the side, showing edge)
    # Door is now positioned at the left side, showing its edge
    door_edge_rect = pygame.Rect(s_w*0.02, s_h*0.1, s_w*0.08, s_h*0.8)
    pygame.draw.rect(tile_surface, wood_color, door_edge_rect)

    # Add a thin line to show door thickness
    pygame.draw.line(tile_surface, dark_wood_color,
                    (door_edge_rect.right, door_edge_rect.top),
                    (door_edge_rect.right, door_edge_rect.bottom), 1)

    # Door handle (now on the side edge)
    handle_pos = (int(door_edge_rect.right - 1), int(door_edge_rect.centery))
    pygame.draw.circle(tile_surface, metal_color, handle_pos, 1)

    return tile_surface


def get_wooden_bridge_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a wooden bridge tile procedurally."""
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Colors
    wood_color = (139, 69, 19)          # Brown wood
    dark_wood_color = (101, 67, 33)     # Dark brown
    rope_color = (160, 82, 45)          # Rope color
    
    # Bridge planks
    plank_height = s_h // 6
    for i in range(6):
        plank_y = i * plank_height
        plank_rect = pygame.Rect(0, plank_y, s_w, plank_height - 1)
        color = wood_color if i % 2 == 0 else dark_wood_color
        pygame.draw.rect(tile_surface, color, plank_rect)
    
    # Rope rails
    pygame.draw.line(tile_surface, rope_color, (2, 0), (2, s_h), 2)
    pygame.draw.line(tile_surface, rope_color, (s_w - 2, 0), (s_w - 2, s_h), 2)
    
    return tile_surface


def get_mountain_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a mountain tile procedurally."""
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    rock_color = (105, 105, 105)        # Gray rock
    dark_rock_color = (70, 70, 70)      # Dark gray
    snow_color = (255, 255, 255)        # White snow
    grass_color = (34, 139, 34)         # Forest green background

    # Fill with grass background
    tile_surface.fill(grass_color)

    # Mountain shape
    mountain_points = [
        (0, s_h),                       # Bottom left
        (s_w*0.3, s_h*0.4),            # Left peak
        (s_w*0.5, s_h*0.1),            # Main peak
        (s_w*0.7, s_h*0.3),            # Right peak
        (s_w, s_h),                     # Bottom right
    ]
    pygame.draw.polygon(tile_surface, rock_color, mountain_points)
    
    # Add shading
    shadow_points = [
        (s_w*0.5, s_h*0.1),            # Main peak
        (s_w*0.7, s_h*0.3),            # Right peak
        (s_w, s_h),                     # Bottom right
        (s_w*0.6, s_h),                 # Shadow bottom
    ]
    pygame.draw.polygon(tile_surface, dark_rock_color, shadow_points)
    
    # Snow cap
    snow_points = [
        (s_w*0.4, s_h*0.2),            # Left snow
        (s_w*0.5, s_h*0.1),            # Peak
        (s_w*0.6, s_h*0.2),            # Right snow
    ]
    pygame.draw.polygon(tile_surface, snow_color, snow_points)
    
    return tile_surface


def get_exit_portal_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate an exit portal tile procedurally."""
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    portal_color = (138, 43, 226)        # Blue violet
    portal_inner = (75, 0, 130)         # Indigo
    portal_glow = (255, 255, 255)       # White glow
    portal_sparkle = (255, 215, 0)      # Gold sparkles

    # Draw portal base (circular)
    center = (s_w // 2, s_h // 2)
    outer_radius = min(s_w, s_h) // 2 - 2
    inner_radius = outer_radius - 4

    # Outer glow
    pygame.draw.circle(tile_surface, portal_glow, center, outer_radius + 1)

    # Main portal ring
    pygame.draw.circle(tile_surface, portal_color, center, outer_radius)
    pygame.draw.circle(tile_surface, portal_inner, center, inner_radius)

    # Inner swirl effect
    import math
    for angle in range(0, 360, 30):
        rad = math.radians(angle)
        x = center[0] + int(math.cos(rad) * (inner_radius - 2))
        y = center[1] + int(math.sin(rad) * (inner_radius - 2))
        pygame.draw.circle(tile_surface, portal_sparkle, (x, y), 1)

    # Center dot
    pygame.draw.circle(tile_surface, portal_glow, center, 2)

    return tile_surface


def get_grass_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a grass tile procedurally."""
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    grass_color = (34, 139, 34)         # Forest green
    dark_grass_color = (0, 100, 0)      # Dark green
    light_grass_color = (50, 205, 50)   # Lime green

    # Fill base
    tile_surface.fill(grass_color)

    # Add grass texture with random blades
    import random
    random.seed(456)  # Consistent randomness

    # Draw grass blades
    for _ in range(40):
        x = random.randint(0, s_w - 1)
        y = random.randint(0, s_h - 1)
        blade_height = random.randint(2, 6)
        color = random.choice([dark_grass_color, light_grass_color, grass_color])

        # Draw vertical grass blade
        pygame.draw.line(tile_surface, color, (x, y), (x, max(0, y - blade_height)), 1)

    # Add some small patches of different shades
    for _ in range(15):
        x = random.randint(0, s_w - 3)
        y = random.randint(0, s_h - 3)
        color = random.choice([dark_grass_color, light_grass_color])
        pygame.draw.circle(tile_surface, color, (x, y), random.randint(1, 2))

    return tile_surface


def get_gravel_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a gravel tile procedurally."""
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    gravel_color = (128, 128, 128)      # Gray
    dark_gravel_color = (96, 96, 96)    # Dark gray
    light_gravel_color = (160, 160, 160) # Light gray

    # Fill base
    tile_surface.fill(gravel_color)

    # Add gravel stones
    import random
    random.seed(789)  # Consistent randomness

    for _ in range(60):
        x = random.randint(0, s_w - 1)
        y = random.randint(0, s_h - 1)
        stone_size = random.randint(1, 3)
        color = random.choice([dark_gravel_color, light_gravel_color, gravel_color])

        # Draw small stone
        pygame.draw.circle(tile_surface, color, (x, y), stone_size)

    return tile_surface


def get_willow_tree_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a willow tree tile procedurally."""
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    trunk_color = (101, 67, 33)         # Brown trunk
    leaf_color = (144, 238, 144)        # Light green (willow-like)
    dark_leaf_color = (34, 139, 34)     # Darker green

    # Trunk (slightly thinner than oak)
    trunk_rect = pygame.Rect(s_w*0.42, s_h*0.5, s_w*0.16, s_h*0.5)
    pygame.draw.rect(tile_surface, trunk_color, trunk_rect)

    # Willow leaves (drooping effect with elongated shapes)
    leaf_centers = [
        (s_w*0.5, s_h*0.25),    # Center top
        (s_w*0.3, s_h*0.3),     # Left
        (s_w*0.7, s_h*0.3),     # Right
        (s_w*0.4, s_h*0.4),     # Left droop
        (s_w*0.6, s_h*0.4),     # Right droop
        (s_w*0.35, s_h*0.5),    # Left lower droop
        (s_w*0.65, s_h*0.5),    # Right lower droop
    ]

    for i, (center_x, center_y) in enumerate(leaf_centers):
        # Make drooping leaves more elongated
        if i >= 3:  # Drooping leaves
            width = int(s_w*0.12)
            height = int(s_h*0.18)
        else:  # Upper canopy
            width = int(s_w*0.15)
            height = int(s_h*0.12)

        # Draw elliptical leaf clusters
        leaf_rect = pygame.Rect(int(center_x - width/2), int(center_y - height/2), width, height)
        pygame.draw.ellipse(tile_surface, leaf_color, leaf_rect)

        # Add darker spots for depth
        dark_rect = pygame.Rect(int(center_x - width/3), int(center_y - height/3 + 2), width//2, height//2)
        pygame.draw.ellipse(tile_surface, dark_leaf_color, dark_rect)

    return tile_surface


def get_hill_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a hill tile procedurally."""
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    hill_color = (107, 142, 35)         # Olive drab
    dark_hill_color = (85, 107, 47)     # Dark olive green
    grass_color = (34, 139, 34)         # Forest green

    # Fill with grass background
    tile_surface.fill(grass_color)

    # Hill shape (gentler than mountain)
    hill_points = [
        (0, s_h),                       # Bottom left
        (s_w*0.2, s_h*0.6),            # Left slope
        (s_w*0.5, s_h*0.3),            # Hill peak
        (s_w*0.8, s_h*0.6),            # Right slope
        (s_w, s_h),                     # Bottom right
    ]
    pygame.draw.polygon(tile_surface, hill_color, hill_points)

    # Add shading on right side
    shadow_points = [
        (s_w*0.5, s_h*0.3),            # Hill peak
        (s_w*0.8, s_h*0.6),            # Right slope
        (s_w, s_h),                     # Bottom right
        (s_w*0.7, s_h),                 # Shadow bottom
    ]
    pygame.draw.polygon(tile_surface, dark_hill_color, shadow_points)

    # Add grass texture on top
    import random
    random.seed(321)  # Consistent randomness

    for _ in range(20):
        x = random.randint(int(s_w*0.2), int(s_w*0.8))
        y = random.randint(int(s_h*0.3), int(s_h*0.7))
        # Only draw grass if it's on the hill (simple height check)
        if y > s_h*0.3 + (abs(x - s_w*0.5) / (s_w*0.3)) * (s_h*0.3):
            pygame.draw.circle(tile_surface, grass_color, (x, y), 1)

    return tile_surface


def get_swamp_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a swamp tile procedurally."""
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    swamp_water_color = (47, 79, 79)    # Dark slate gray
    mud_color = (101, 67, 33)           # Brown mud
    algae_color = (85, 107, 47)         # Dark olive green
    bubble_color = (105, 105, 105)      # Dim gray

    # Fill base with swamp water
    tile_surface.fill(swamp_water_color)

    # Add mud patches
    import random
    random.seed(654)  # Consistent randomness

    for _ in range(8):
        x = random.randint(0, s_w - 6)
        y = random.randint(0, s_h - 6)
        mud_size = random.randint(3, 8)
        pygame.draw.circle(tile_surface, mud_color, (x, y), mud_size)

    # Add algae patches
    for _ in range(12):
        x = random.randint(0, s_w - 4)
        y = random.randint(0, s_h - 4)
        algae_size = random.randint(2, 5)
        pygame.draw.circle(tile_surface, algae_color, (x, y), algae_size)

    # Add bubbles for swamp effect
    for _ in range(6):
        x = random.randint(2, s_w - 2)
        y = random.randint(2, s_h - 2)
        bubble_size = random.randint(1, 2)
        pygame.draw.circle(tile_surface, bubble_color, (x, y), bubble_size)

    return tile_surface


def get_cobblestone_path_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a cobblestone path tile procedurally."""
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    cobble_color = (169, 169, 169)      # Dark gray
    dark_cobble_color = (105, 105, 105) # Dim gray
    light_cobble_color = (192, 192, 192) # Silver
    mortar_color = (128, 128, 128)      # Gray mortar

    # Fill base with mortar
    tile_surface.fill(mortar_color)

    # Draw cobblestones in a pattern
    import random
    random.seed(987)  # Consistent randomness

    stone_size = s_w // 6
    for y in range(0, s_h, stone_size + 1):
        for x in range(0, s_w, stone_size + 1):
            # Add some variation in positioning
            offset_x = random.randint(-1, 1)
            offset_y = random.randint(-1, 1)

            stone_x = x + offset_x
            stone_y = y + offset_y

            # Ensure stone stays within bounds
            if stone_x >= 0 and stone_y >= 0 and stone_x + stone_size < s_w and stone_y + stone_size < s_h:
                # Choose stone color
                color = random.choice([cobble_color, dark_cobble_color, light_cobble_color])

                # Draw irregular stone shape (circle with some variation)
                stone_radius = stone_size // 2 + random.randint(-1, 1)
                center_x = stone_x + stone_size // 2
                center_y = stone_y + stone_size // 2

                pygame.draw.circle(tile_surface, color, (center_x, center_y), stone_radius)

                # Add highlight for 3D effect
                highlight_color = (min(255, color[0] + 30), min(255, color[1] + 30), min(255, color[2] + 30))
                pygame.draw.circle(tile_surface, highlight_color, (center_x - 1, center_y - 1), stone_radius // 2)

    return tile_surface


def get_dirt_road_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a dirt road tile procedurally."""
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    dirt_color = (160, 82, 45)          # Saddle brown
    dark_dirt_color = (139, 69, 19)     # Saddle brown (darker)
    light_dirt_color = (205, 133, 63)   # Peru (lighter)
    rut_color = (101, 67, 33)           # Dark brown for wheel ruts

    # Fill base
    tile_surface.fill(dirt_color)

    # Add dirt texture
    import random
    random.seed(654)  # Consistent randomness

    for _ in range(25):
        x = random.randint(0, s_w - 2)
        y = random.randint(0, s_h - 2)
        color = random.choice([dark_dirt_color, light_dirt_color])
        size_var = random.randint(1, 2)
        pygame.draw.circle(tile_surface, color, (x, y), size_var)

    # Add wheel ruts (two parallel lines)
    rut_width = 2
    left_rut_x = s_w // 4
    right_rut_x = 3 * s_w // 4

    for y in range(0, s_h, 3):
        # Left rut
        pygame.draw.circle(tile_surface, rut_color, (left_rut_x, y), rut_width)
        # Right rut
        pygame.draw.circle(tile_surface, rut_color, (right_rut_x, y), rut_width)

    return tile_surface


def get_wood_fence_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a wood fence tile procedurally."""
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    wood_color = (139, 69, 19)          # Saddle brown
    dark_wood_color = (101, 67, 33)     # Dark brown
    light_wood_color = (160, 82, 45)    # Light brown
    grass_color = (34, 139, 34)         # Forest green background

    # Fill with grass background instead of transparent
    tile_surface.fill(grass_color)

    # Fence posts (vertical)
    post_width = s_w // 8
    post_height = s_h * 3 // 4

    # Left post
    left_post = pygame.Rect(s_w // 6, s_h // 8, post_width, post_height)
    pygame.draw.rect(tile_surface, wood_color, left_post)
    pygame.draw.rect(tile_surface, dark_wood_color, left_post, 1)

    # Right post
    right_post = pygame.Rect(5 * s_w // 6 - post_width, s_h // 8, post_width, post_height)
    pygame.draw.rect(tile_surface, wood_color, right_post)
    pygame.draw.rect(tile_surface, dark_wood_color, right_post, 1)

    # Horizontal rails
    rail_height = s_h // 12

    # Top rail
    top_rail = pygame.Rect(s_w // 6, s_h // 4, 4 * s_w // 6, rail_height)
    pygame.draw.rect(tile_surface, light_wood_color, top_rail)
    pygame.draw.rect(tile_surface, dark_wood_color, top_rail, 1)

    # Bottom rail
    bottom_rail = pygame.Rect(s_w // 6, s_h * 5 // 8, 4 * s_w // 6, rail_height)
    pygame.draw.rect(tile_surface, light_wood_color, bottom_rail)
    pygame.draw.rect(tile_surface, dark_wood_color, bottom_rail, 1)

    return tile_surface


def get_wood_fence_gate_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a wood fence gate tile procedurally (closed state)."""
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    wood_color = (139, 69, 19)          # Saddle brown
    dark_wood_color = (101, 67, 33)     # Dark brown
    light_wood_color = (160, 82, 45)    # Light brown
    grass_color = (34, 139, 34)         # Forest green background
    metal_color = (192, 192, 192)       # Silver metal for hinges

    # Fill with grass background
    tile_surface.fill(grass_color)

    # Gate posts (vertical) - slightly thicker than fence posts
    post_width = s_w // 6
    post_height = s_h * 3 // 4
    post_y = s_h // 8

    # Left gate post
    left_post = pygame.Rect(s_w // 8, post_y, post_width, post_height)
    pygame.draw.rect(tile_surface, wood_color, left_post)
    pygame.draw.rect(tile_surface, dark_wood_color, left_post, 1)

    # Right gate post
    right_post = pygame.Rect(s_w - s_w // 8 - post_width, post_y, post_width, post_height)
    pygame.draw.rect(tile_surface, wood_color, right_post)
    pygame.draw.rect(tile_surface, dark_wood_color, right_post, 1)

    # Gate panels (closed) - two panels meeting in the middle
    gate_height = s_h // 2
    gate_y = s_h // 4
    panel_width = (s_w - 2 * (s_w // 8 + post_width)) // 2 - 2  # Space between posts, divided by 2

    # Left gate panel
    left_panel = pygame.Rect(s_w // 8 + post_width + 1, gate_y, panel_width, gate_height)
    pygame.draw.rect(tile_surface, light_wood_color, left_panel)
    pygame.draw.rect(tile_surface, dark_wood_color, left_panel, 1)

    # Right gate panel
    right_panel_x = s_w // 2 + 1
    right_panel = pygame.Rect(right_panel_x, gate_y, panel_width, gate_height)
    pygame.draw.rect(tile_surface, light_wood_color, right_panel)
    pygame.draw.rect(tile_surface, dark_wood_color, right_panel, 1)

    # Add vertical slats to gate panels
    slat_width = 2
    for panel_rect in [left_panel, right_panel]:
        for i in range(1, 4):  # 3 slats per panel
            slat_x = panel_rect.left + (panel_rect.width * i // 4)
            slat_rect = pygame.Rect(slat_x, panel_rect.top + 2, slat_width, panel_rect.height - 4)
            pygame.draw.rect(tile_surface, wood_color, slat_rect)

    # Add hinges on left post
    hinge_size = 3
    hinge_y1 = gate_y + 3
    hinge_y2 = gate_y + gate_height - 6
    pygame.draw.circle(tile_surface, metal_color, (left_post.right - 1, hinge_y1), hinge_size)
    pygame.draw.circle(tile_surface, metal_color, (left_post.right - 1, hinge_y2), hinge_size)

    # Add gate handle on right panel
    handle_x = right_panel.left + 3
    handle_y = gate_y + gate_height // 2
    pygame.draw.circle(tile_surface, metal_color, (handle_x, handle_y), 2)

    return tile_surface


def get_wood_fence_gate_open_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate an open wood fence gate tile procedurally."""
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    wood_color = (139, 69, 19)          # Saddle brown
    dark_wood_color = (101, 67, 33)     # Dark brown
    light_wood_color = (160, 82, 45)    # Light brown
    grass_color = (34, 139, 34)         # Forest green background
    metal_color = (192, 192, 192)       # Silver metal for hinges

    # Fill with grass background
    tile_surface.fill(grass_color)

    # Gate posts (vertical) - same as closed gate
    post_width = s_w // 6
    post_height = s_h * 3 // 4
    post_y = s_h // 8

    # Left gate post
    left_post = pygame.Rect(s_w // 8, post_y, post_width, post_height)
    pygame.draw.rect(tile_surface, wood_color, left_post)
    pygame.draw.rect(tile_surface, dark_wood_color, left_post, 1)

    # Right gate post
    right_post = pygame.Rect(s_w - s_w // 8 - post_width, post_y, post_width, post_height)
    pygame.draw.rect(tile_surface, wood_color, right_post)
    pygame.draw.rect(tile_surface, dark_wood_color, right_post, 1)

    # Open gate panels - swung to the sides (showing edges)
    gate_height = s_h // 2
    gate_y = s_h // 4

    # Left gate panel (swung left, showing edge)
    left_panel_edge = pygame.Rect(s_w // 16, gate_y + 5, 3, gate_height - 10)
    pygame.draw.rect(tile_surface, wood_color, left_panel_edge)
    pygame.draw.rect(tile_surface, dark_wood_color, left_panel_edge, 1)

    # Right gate panel (swung right, showing edge)
    right_panel_edge = pygame.Rect(s_w - s_w // 16 - 3, gate_y + 5, 3, gate_height - 10)
    pygame.draw.rect(tile_surface, wood_color, right_panel_edge)
    pygame.draw.rect(tile_surface, dark_wood_color, right_panel_edge, 1)

    # Add hinges on posts (still visible)
    hinge_size = 3
    hinge_y1 = gate_y + 3
    hinge_y2 = gate_y + gate_height - 6
    pygame.draw.circle(tile_surface, metal_color, (left_post.right - 1, hinge_y1), hinge_size)
    pygame.draw.circle(tile_surface, metal_color, (left_post.right - 1, hinge_y2), hinge_size)

    # Add some grass texture in the open area
    import random
    random.seed(456)  # Consistent randomness

    # Add grass details in the open gate area
    for _ in range(15):
        x = random.randint(s_w // 4, 3 * s_w // 4)
        y = random.randint(gate_y, gate_y + gate_height)
        grass_shade = random.choice([grass_color, (50, 150, 50), (20, 120, 20)])
        pygame.draw.circle(tile_surface, grass_shade, (x, y), 1)

    return tile_surface


def get_wood_walkway_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a wood walkway tile procedurally."""
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    wood_color = (139, 69, 19)          # Saddle brown
    dark_wood_color = (101, 67, 33)     # Dark brown
    light_wood_color = (160, 82, 45)    # Light brown

    # Fill base
    tile_surface.fill(wood_color)

    # Draw wooden planks running horizontally
    plank_height = s_h // 5
    for i in range(5):
        plank_y = i * plank_height
        plank_rect = pygame.Rect(0, plank_y, s_w, plank_height - 1)

        # Alternate plank colors for variation
        color = light_wood_color if i % 2 == 0 else wood_color
        pygame.draw.rect(tile_surface, color, plank_rect)

        # Add wood grain lines
        grain_y = plank_y + plank_height // 2
        pygame.draw.line(tile_surface, dark_wood_color, (2, grain_y), (s_w - 2, grain_y), 1)

        # Add plank separation
        if i < 4:  # Don't draw line after last plank
            pygame.draw.line(tile_surface, dark_wood_color,
                           (0, plank_y + plank_height - 1),
                           (s_w, plank_y + plank_height - 1), 1)

    # Add some nail details
    import random
    random.seed(111)  # Consistent randomness

    for i in range(5):
        plank_y = i * plank_height + plank_height // 2
        # Left nail
        nail_x = s_w // 8
        pygame.draw.circle(tile_surface, dark_wood_color, (nail_x, plank_y), 1)
        # Right nail
        nail_x = 7 * s_w // 8
        pygame.draw.circle(tile_surface, dark_wood_color, (nail_x, plank_y), 1)

    return tile_surface


def get_magic_door_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a magical door exit tile."""
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    door_color = (101, 67, 33)        # Dark brown door
    frame_color = (139, 69, 19)       # Lighter brown frame
    magic_color = (138, 43, 226)      # Blue violet magic
    magic_glow = (75, 0, 130)         # Indigo glow
    handle_color = (255, 215, 0)      # Gold handle
    rune_color = (255, 255, 255)      # White runes

    # Draw door frame
    frame_thickness = max(2, s_w // 16)
    pygame.draw.rect(tile_surface, frame_color, (0, 0, s_w, s_h))

    # Draw door interior
    door_rect = pygame.Rect(frame_thickness, frame_thickness,
                           s_w - 2 * frame_thickness, s_h - 2 * frame_thickness)
    pygame.draw.rect(tile_surface, door_color, door_rect)

    # Add wood grain texture
    grain_lines = 5
    for i in range(grain_lines):
        y_pos = door_rect.y + (i + 1) * door_rect.height // (grain_lines + 1)
        start_x = door_rect.x + 2
        end_x = door_rect.right - 2
        pygame.draw.line(tile_surface, (80, 50, 25), (start_x, y_pos), (end_x, y_pos), 1)

    # Draw magical glow around the door
    glow_rect = pygame.Rect(door_rect.x - 1, door_rect.y - 1,
                           door_rect.width + 2, door_rect.height + 2)
    pygame.draw.rect(tile_surface, magic_glow, glow_rect, 2)

    # Add magical sparkles/runes
    if s_w >= 24:
        # Draw mystical runes on the door
        rune_positions = [
            (door_rect.centerx - 4, door_rect.y + door_rect.height // 4),
            (door_rect.centerx + 4, door_rect.y + door_rect.height // 4),
            (door_rect.centerx, door_rect.y + 3 * door_rect.height // 4)
        ]

        for x, y in rune_positions:
            # Draw simple rune symbols
            pygame.draw.circle(tile_surface, rune_color, (x, y), 2)
            pygame.draw.line(tile_surface, rune_color, (x-2, y), (x+2, y), 1)
            pygame.draw.line(tile_surface, rune_color, (x, y-2), (x, y+2), 1)

    # Draw door handle
    handle_x = door_rect.right - door_rect.width // 4
    handle_y = door_rect.centery
    pygame.draw.circle(tile_surface, handle_color, (handle_x, handle_y), max(2, s_w // 16))

    # Add magical energy effect
    if s_w >= 32:
        # Draw swirling magical energy
        center_x, center_y = door_rect.centerx, door_rect.centery
        for i in range(3):
            angle_offset = i * 120  # 120 degrees apart
            import math
            for j in range(8):
                angle = (angle_offset + j * 45) * math.pi / 180
                radius = 8 + j
                x = center_x + int(radius * math.cos(angle))
                y = center_y + int(radius * math.sin(angle))
                if door_rect.collidepoint(x, y):
                    alpha = 255 - j * 30
                    magic_surface = pygame.Surface((2, 2), pygame.SRCALPHA)
                    magic_surface.fill((*magic_color, max(50, alpha)))
                    tile_surface.blit(magic_surface, (x-1, y-1))

    return tile_surface


def get_missing_asset_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a 'missing asset' placeholder tile."""
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    bg_color = (255, 0, 255)      # Magenta background
    text_color = (255, 255, 255)  # White text
    border_color = (0, 0, 0)      # Black border

    # Fill background
    tile_surface.fill(bg_color)

    # Draw border
    pygame.draw.rect(tile_surface, border_color, (0, 0, s_w, s_h), 2)

    # Draw diagonal lines to make it more obvious
    pygame.draw.line(tile_surface, border_color, (0, 0), (s_w, s_h), 2)
    pygame.draw.line(tile_surface, border_color, (s_w, 0), (0, s_h), 2)

    # Add text if tile is large enough
    if s_w >= 32 and s_h >= 32:
        # Create font (use default pygame font)
        font = pygame.font.Font(None, min(s_w // 4, 12))

        # Render "MISSING" text
        text_surface = font.render("MISSING", True, text_color)
        text_rect = text_surface.get_rect(center=(s_w // 2, s_h // 2 - 4))
        tile_surface.blit(text_surface, text_rect)

        # Render "ASSET" text
        text_surface2 = font.render("ASSET", True, text_color)
        text_rect2 = text_surface2.get_rect(center=(s_w // 2, s_h // 2 + 4))
        tile_surface.blit(text_surface2, text_rect2)

    return tile_surface
