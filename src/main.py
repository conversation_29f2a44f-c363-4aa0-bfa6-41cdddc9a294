"""
Main entry point for the game.

This module initializes the game systems and starts the main game loop.
"""

import pygame
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from src.infrastructure import (
    PygameEventBus, AssetManager, LevelRepository, 
    PygameAudioPlayer, JsonSaveGameRepository, setup_logging, get_logger
)
from src.infrastructure.repositories import MapParser
from src.application import (
    MovePlayerUseCase, PlayerAttackUseCase, BuildLevelUseCase,
    InteractWithNPCUseCase, BuyFromNPCUseCase, SellToNPCUseCase,
    UpdateWanderingEntitiesUseCase, TileInteractionUseCase
)
from src.game_core.quest_manager import GlobalQuestManager
from src.presentation import GameEngine
from src.game_core.config import initialize_config, get_config


def main():
    """Main entry point for the game."""
    # Initialize configuration - look in project root, not src directory
    config_path = Path(__file__).parent.parent / "game_config.yaml"
    initialize_config(str(config_path))
    
    # Set up logging based on config
    config = get_config()
    setup_logging(verbose=config.verbose_logging)
    
    # Get logger for this module
    logger = get_logger(__name__)
    logger.info("Starting Treebeard's Revenge...")
    
    # Initialize Pygame
    pygame.init()
    
    try:
        # Setup paths
        base_legend_path = Path(__file__).parent / "game_data" / "base_legend.yaml"
        levels_path = Path(__file__).parent / "levels"
        saves_path = Path(__file__).parent.parent / "saves"  # Save outside src
        audio_music_path = Path(__file__).parent.parent / "assets" / "audio" / "music"
        audio_sounds_path = Path(__file__).parent.parent / "assets" / "audio" / "sounds"
        
        # Initialize infrastructure components
        event_bus = PygameEventBus()
        asset_manager = AssetManager()
        level_repository = LevelRepository(str(levels_path), str(base_legend_path))
        audio_player = PygameAudioPlayer(
            sound_directory=str(audio_sounds_path),
            music_directory=str(audio_music_path)
        )

        # Initialize quest manager
        quest_manager = GlobalQuestManager(event_bus)
        save_repository = JsonSaveGameRepository(str(saves_path))
        
        # Initialize use cases
        move_player_use_case = MovePlayerUseCase(
            event_bus,
            tile_size=config.rendering.tile_size,
            move_speed=config.movement.player_move_speed * config.rendering.tile_size  # Convert moves/sec to pixels/sec
        )
        attack_use_case = PlayerAttackUseCase(event_bus)
        build_level_use_case = BuildLevelUseCase(event_bus)

        # Initialize NPC use cases
        interact_npc_use_case = InteractWithNPCUseCase(event_bus)
        buy_from_npc_use_case = BuyFromNPCUseCase(event_bus)
        sell_to_npc_use_case = SellToNPCUseCase(event_bus)

        # Initialize wander system use case
        update_wandering_entities_use_case = UpdateWanderingEntitiesUseCase(event_bus)

        # Initialize tile interaction use case
        map_parser = MapParser(str(base_legend_path))
        tile_interaction_use_case = TileInteractionUseCase(event_bus, map_parser)

        # Initialize game engine
        game_engine = GameEngine(
            event_bus=event_bus,
            asset_manager=asset_manager,
            level_repository=level_repository,
            move_player_use_case=move_player_use_case,
            attack_use_case=attack_use_case,
            build_level_use_case=build_level_use_case,
            interact_npc_use_case=interact_npc_use_case,
            buy_from_npc_use_case=buy_from_npc_use_case,
            sell_to_npc_use_case=sell_to_npc_use_case,
            update_wandering_entities_use_case=update_wandering_entities_use_case,
            tile_interaction_use_case=tile_interaction_use_case,
            audio_player=audio_player,
            save_repository=save_repository,
            quest_manager=quest_manager
        )
        
        # Start the game
        game_engine.run()
        
    except Exception as e:
        logger.error(f"Error starting game: {e}")
        import traceback
        traceback.print_exc()
    
    finally:    
        pygame.quit()
        sys.exit()


if __name__ == "__main__":
    main()
