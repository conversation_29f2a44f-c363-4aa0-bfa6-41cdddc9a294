"""
Inventory and Character Sheet UI Controller

This module manages the inventory and character sheet UI state and interactions.
"""

import pygame
from typing import Optional, Dict, Any, Tuple, List
from dataclasses import dataclass
from enum import Enum

from src.application.interfaces import GameStateData, IEventBus, IAssetManager
from src.application.use_cases import (
    EquipItemUseCase, UnequipItemUseCase, GetInventoryUseCase,
    RearrangeInventoryUseCase, GetPlayerVisualDataUseCase, GetPlayerStatsUseCase
)
from src.game_core.config import get_config
from .tooltip import TooltipRenderer, TooltipData
from src.editor.ui_config import get_font, get_element_size, get_spacing, scale_value, FONT_LARGE, FONT_NORMAL, FONT_SMALL


class DragState(Enum):
    """States for drag and drop operations."""
    NONE = "none"
    DRAGGING_INVENTORY_ITEM = "dragging_inventory_item"
    DRAGGING_EQUIPMENT_ITEM = "dragging_equipment_item"


@dataclass
class DragData:
    """Data for current drag operation."""
    item_id: str
    source_type: str  # "inventory" or "equipment"
    source_index: Optional[int] = None  # For inventory slots
    source_slot: Optional[str] = None   # For equipment slots
    drag_surface: Optional[pygame.Surface] = None
    offset: Tuple[int, int] = (0, 0)


@dataclass
class UILayout:
    """UI layout configuration that adapts to screen size using game config."""
    # These will be calculated based on screen size and config
    character_panel_pos: Tuple[int, int] = (0, 0)
    character_panel_size: Tuple[int, int] = (0, 0)
    inventory_panel_pos: Tuple[int, int] = (0, 0)
    inventory_panel_size: Tuple[int, int] = (0, 0)
    stats_panel_pos: Tuple[int, int] = (0, 0)
    stats_panel_size: Tuple[int, int] = (0, 0)

    # Equipment slots within character panel
    equipment_slots: Dict[str, Dict[str, Any]] = None

    # Inventory grid configuration - from config
    inventory_grid_size: Tuple[int, int] = (2, 5)  # 2x5 = 10 slots
    inventory_slot_size: Tuple[int, int] = (160, 160)  # Large slots from config
    inventory_slot_padding: int = 20  # From config

    def calculate_layout(self, screen_width: int, screen_height: int):
        """Calculate responsive layout based on screen size and config."""
        config = get_config().inventory_ui

        # Use config values for margins and spacing
        margin = config.panel_margin
        spacing = config.panel_spacing
        usable_width = screen_width - (margin * 2)
        usable_height = int(screen_height * (config.panel_height_percent / 100))

        # Distribute width based on config percentages
        char_width = int(usable_width * (config.character_panel_width_percent / 100))
        inv_width = int(usable_width * (config.inventory_panel_width_percent / 100))
        stats_width = int(usable_width * (config.stats_panel_width_percent / 100))

        # Calculate positions
        self.character_panel_pos = (margin, margin)
        self.character_panel_size = (char_width, usable_height)

        self.inventory_panel_pos = (margin + char_width + spacing, margin)
        self.inventory_panel_size = (inv_width, usable_height)

        self.stats_panel_pos = (margin + char_width + inv_width + spacing * 2, margin)
        self.stats_panel_size = (stats_width, usable_height)

        # Set inventory configuration from config
        self.inventory_slot_size = (config.inventory_slot_size, config.inventory_slot_size)
        self.inventory_slot_padding = config.inventory_slot_padding

        # Calculate grid size for exactly the configured number of slots
        self._calculate_inventory_grid_from_config(config)

        # Recalculate equipment slots for the new character panel size
        self._calculate_equipment_slots(config)

    def _calculate_inventory_grid_from_config(self, config):
        """Calculate inventory grid size based on config."""
        # For 10 slots, we want a 2x5 grid (2 columns, 5 rows)
        max_slots = config.inventory_max_slots

        # Calculate optimal grid dimensions
        if max_slots == 10:
            self.inventory_grid_size = (2, 5)  # 2 columns, 5 rows
        elif max_slots <= 12:
            self.inventory_grid_size = (3, 4)  # 3 columns, 4 rows
        elif max_slots <= 16:
            self.inventory_grid_size = (4, 4)  # 4 columns, 4 rows
        elif max_slots <= 20:
            self.inventory_grid_size = (4, 5)  # 4 columns, 5 rows
        else:
            # For larger inventories, calculate square-ish grid
            cols = int(max_slots ** 0.5)
            rows = (max_slots + cols - 1) // cols  # Ceiling division
            self.inventory_grid_size = (cols, rows)

    def _calculate_equipment_slots(self, config):
        """Calculate equipment slot positions for the character panel."""
        char_width, char_height = self.character_panel_size

        # Use config slot size
        slot_size = config.equipment_slot_size

        # Center the equipment layout in the character panel
        center_x = char_width // 2
        center_y = char_height // 2

        # Equipment layout in a cross pattern with larger slots
        self.equipment_slots = {
            "head_equipment": {
                "position": (center_x - slot_size//2, center_y - slot_size*2),
                "size": (slot_size, slot_size)
            },
            "chest_equipment": {
                "position": (center_x - slot_size//2, center_y - slot_size//2),
                "size": (slot_size, slot_size)
            },
            "legs_equipment": {
                "position": (center_x - slot_size//2, center_y + slot_size),
                "size": (slot_size, slot_size)
            },
            "boots_equipment": {
                "position": (center_x - slot_size//2, center_y + slot_size*2 + slot_size//2),
                "size": (slot_size, slot_size)
            },
            "main_hand_weapon": {
                "position": (center_x - slot_size*2, center_y - slot_size//2),
                "size": (slot_size, slot_size)
            },
            "off_hand_equipment": {
                "position": (center_x + slot_size, center_y - slot_size//2),
                "size": (slot_size, slot_size)
            }
        }



class InventoryUIController:
    """Manages inventory and character sheet UI state and interactions."""
    
    def __init__(self, event_bus: IEventBus, asset_manager: IAssetManager, screen_width: int = 2800, screen_height: int = 1400):
        """
        Initialize the inventory UI controller.

        Args:
            event_bus: Event bus for publishing events
            asset_manager: Asset manager for loading UI assets
            screen_width: Screen width for responsive layout
            screen_height: Screen height for responsive layout
        """
        self.event_bus = event_bus
        self.asset_manager = asset_manager

        # UI state
        self.is_open = False
        self.layout = UILayout()
        self.layout.calculate_layout(screen_width, screen_height)
        
        # Interaction state
        self.hover_slot = None
        self.selected_item = None
        self.drag_state = DragState.NONE
        self.drag_data: Optional[DragData] = None
        
        # Use cases
        self.equip_item_use_case = EquipItemUseCase(event_bus)
        self.unequip_item_use_case = UnequipItemUseCase(event_bus)
        self.get_inventory_use_case = GetInventoryUseCase(event_bus)
        self.rearrange_inventory_use_case = RearrangeInventoryUseCase(event_bus)
        self.get_player_visual_use_case = GetPlayerVisualDataUseCase(event_bus)
        self.get_player_stats_use_case = GetPlayerStatsUseCase(event_bus)
        
        # UI colors from config with modern blue-grey theme
        config = get_config().inventory_ui
        self.colors = {
            "background": (45, 50, 60, 200),       # Modern blue-grey panel background
            "border": (70, 80, 95),                # Blue-grey border
            "text": (240, 245, 250),               # Almost white text
            "highlight": (100, 140, 180, 60),      # Blue highlight
            "slot_empty": (35, 40, 50),            # Dark blue-grey for empty slots
            "slot_hover": (50, 60, 75),            # Lighter blue-grey for hover
            "slot_selected": (60, 120, 180),       # Blue for selection
            "text_shadow": (0, 0, 0),              # Black text shadow
            "stat_positive": (90, 200, 120),       # Green for positive stats
            "stat_negative": (255, 100, 100),      # Red for negative stats
            "health": (220, 80, 80),               # Red for health bars
            "mana": (80, 120, 220),                # Blue for mana bars  
            "progress_bg": (35, 40, 50),           # Dark background for progress bars
        }
        
        # Cached surfaces
        self._cached_panels = {}
        self._cache_dirty = True

        # Track game state changes
        self._last_player_equipment = None
        self._last_inventory_hash = None

        # Tooltip system
        self.tooltip_renderer = TooltipRenderer()
        self.current_tooltip: Optional[TooltipData] = None
        self.tooltip_delay = config.tooltip_delay_ms  # From config
        self.tooltip_timer = 0

        # Double-click detection
        self.last_click_time = 0
        self.last_click_pos = (0, 0)
        self.double_click_threshold = config.double_click_threshold_ms  # From config
        self.pending_double_click = None
    
    def toggle_inventory(self) -> None:
        """Toggle the inventory UI open/close state."""
        self.is_open = not self.is_open
        if not self.is_open:
            # Clear interaction state when closing
            self.hover_slot = None
            self.selected_item = None
            self.drag_state = DragState.NONE
            self.drag_data = None
        self._cache_dirty = True

    def update_layout(self, screen_width: int, screen_height: int) -> None:
        """Update the UI layout for new screen dimensions."""
        self.layout.calculate_layout(screen_width, screen_height)
        self._cache_dirty = True
    
    def handle_mouse_click(self, pos: Tuple[int, int], button: int, game_state: GameStateData) -> GameStateData:
        """
        Handle mouse click events.

        Args:
            pos: Mouse position
            button: Mouse button (1=left, 3=right)
            game_state: Current game state

        Returns:
            Updated game state
        """
        if not self.is_open:
            return game_state

        # Handle drag and drop completion
        if self.drag_state != DragState.NONE and button == 1:
            return self._handle_drop(pos, game_state)

        # Check equipment slot clicks
        equipment_slot = self._get_equipment_slot_at_pos(pos)
        if equipment_slot:
            return self._handle_equipment_slot_click(equipment_slot, button, game_state)

        # Check inventory slot clicks
        inventory_slot = self._get_inventory_slot_at_pos(pos)
        if inventory_slot is not None:
            return self._handle_inventory_slot_click(inventory_slot, button, game_state)

        return game_state

    def handle_mouse_down(self, pos: Tuple[int, int], button: int, game_state: GameStateData) -> None:
        """
        Handle mouse button down events (for starting drag operations).

        Args:
            pos: Mouse position
            button: Mouse button
            game_state: Current game state
        """
        if not self.is_open or button != 1:  # Only left mouse button
            return

        import time
        current_time = time.time() * 1000  # Convert to milliseconds

        # Check for double-click
        time_diff = current_time - self.last_click_time
        pos_diff = abs(pos[0] - self.last_click_pos[0]) + abs(pos[1] - self.last_click_pos[1])

        is_double_click = (time_diff < self.double_click_threshold and pos_diff < 10)

        self.last_click_time = current_time
        self.last_click_pos = pos

        if is_double_click:
            # Handle double-click - but we can't return the updated game state from mouse_down
            # So we'll set a flag and handle it in mouse_up
            self.pending_double_click = pos
            return

        # Start drag operation (but don't complete it yet)
        equipment_slot = self._get_equipment_slot_at_pos(pos)
        if equipment_slot and game_state.player:
            equipped_item = game_state.player.get_equipped_item_in_slot(equipment_slot)
            if equipped_item:
                self._start_equipment_drag(equipped_item, equipment_slot, pos)
                return

        # Check if starting drag from inventory slot
        inventory_slot = self._get_inventory_slot_at_pos(pos)
        if inventory_slot is not None:
            try:
                inventory_data = self.get_inventory_use_case.execute(game_state)
                inventory_items = list(inventory_data["inventory"].items())

                if inventory_slot < len(inventory_items):
                    item_id, quantity = inventory_items[inventory_slot]
                    self._start_inventory_drag(item_id, inventory_slot, pos)
            except Exception as e:
                print(f"Error starting inventory drag: {e}")

    def handle_mouse_up(self, pos: Tuple[int, int], button: int, game_state: GameStateData) -> GameStateData:
        """
        Handle mouse button up events (for completing drag operations).

        Args:
            pos: Mouse position
            button: Mouse button
            game_state: Current game state

        Returns:
            Updated game state
        """
        if not self.is_open or button != 1:
            return game_state

        # Handle pending double-click first
        if self.pending_double_click:
            new_game_state = self._handle_double_click(self.pending_double_click, game_state)
            self.pending_double_click = None
            self._cache_dirty = True  # Force UI refresh
            return new_game_state

        # Complete drag operation if we were dragging
        if self.drag_state != DragState.NONE:
            new_game_state = self._handle_drop(pos, game_state)
            self._end_drag()
            self._cache_dirty = True  # Force UI refresh
            return new_game_state

        # If not dragging, handle single click
        return self._handle_single_click(pos, game_state)

    def _handle_double_click(self, pos: Tuple[int, int], game_state: GameStateData) -> GameStateData:
        """Handle double-click events for quick equip/unequip."""
        # Check equipment slot double-click (unequip)
        equipment_slot = self._get_equipment_slot_at_pos(pos)
        if equipment_slot and game_state.player:
            equipped_item = game_state.player.get_equipped_item_in_slot(equipment_slot)
            if equipped_item:
                try:
                    return self.unequip_item_use_case.execute(game_state, equipment_slot)
                except Exception as e:
                    print(f"Failed to unequip item: {e}")

        # Check inventory slot double-click (equip)
        inventory_slot = self._get_inventory_slot_at_pos(pos)
        if inventory_slot is not None:
            try:
                inventory_data = self.get_inventory_use_case.execute(game_state)
                inventory_items = list(inventory_data["inventory"].items())

                if inventory_slot < len(inventory_items):
                    item_id, quantity = inventory_items[inventory_slot]

                    # Try to equip the item (find appropriate slot)
                    equipment_slot = self._get_item_equipment_slot(item_id)
                    if equipment_slot:
                        return self.equip_item_use_case.execute(game_state, item_id, equipment_slot)
            except Exception as e:
                print(f"Failed to equip item: {e}")

        return game_state

    def _handle_single_click(self, pos: Tuple[int, int], game_state: GameStateData) -> GameStateData:
        """Handle single click events."""
        # For now, single clicks don't do anything special
        # In the future, this could select items or show context menus
        return game_state
    
    def handle_mouse_motion(self, pos: Tuple[int, int], game_state: GameStateData) -> None:
        """
        Handle mouse motion events.

        Args:
            pos: Mouse position
            game_state: Current game state
        """
        if not self.is_open:
            return

        # Update hover state
        old_hover = self.hover_slot

        # Check equipment slots
        equipment_slot = self._get_equipment_slot_at_pos(pos)
        if equipment_slot:
            self.hover_slot = ("equipment", equipment_slot)
        else:
            # Check inventory slots
            inventory_slot = self._get_inventory_slot_at_pos(pos)
            if inventory_slot is not None:
                self.hover_slot = ("inventory", inventory_slot)
            else:
                self.hover_slot = None

        # Update tooltip
        if old_hover != self.hover_slot:
            self._update_tooltip(game_state)
            self._cache_dirty = True

    def update(self, dt: float, game_state: GameStateData) -> None:
        """
        Update the UI state.

        Args:
            dt: Delta time in seconds
            game_state: Current game state
        """
        if not self.is_open:
            return

        # Check if game state has changed and invalidate cache if needed
        self._check_game_state_changes(game_state)

        # Update tooltip timer
        if self.hover_slot and not self.current_tooltip:
            self.tooltip_timer += dt * 1000  # Convert to milliseconds
            if self.tooltip_timer >= self.tooltip_delay:
                self._update_tooltip(game_state)
        elif not self.hover_slot:
            self.tooltip_timer = 0
            self.current_tooltip = None

    def _check_game_state_changes(self, game_state: GameStateData) -> None:
        """Check if the game state has changed and invalidate cache if needed."""
        if not game_state.player:
            return

        # Check if player equipment has changed
        current_equipment = {
            "head_equipment": game_state.player.head_equipment,
            "chest_equipment": game_state.player.chest_equipment,
            "legs_equipment": game_state.player.legs_equipment,
            "boots_equipment": game_state.player.boots_equipment,
            "main_hand_weapon": game_state.player.main_hand_weapon,
            "off_hand_equipment": game_state.player.off_hand_equipment,
        }

        if self._last_player_equipment != current_equipment:
            self._last_player_equipment = current_equipment
            self._cache_dirty = True

        # Check if inventory has changed
        try:
            inventory_data = self.get_inventory_use_case.execute(game_state)
            inventory_hash = hash(str(sorted(inventory_data["inventory"].items())))

            if self._last_inventory_hash != inventory_hash:
                self._last_inventory_hash = inventory_hash
                self._cache_dirty = True
        except Exception:
            # If we can't get inventory data, just mark cache as dirty
            self._cache_dirty = True

    def render(self, screen: pygame.Surface, game_state: GameStateData) -> None:
        """
        Render the inventory UI.

        Args:
            screen: Surface to render to
            game_state: Current game state
        """
        if not self.is_open:
            return


        
        # Render panels
        self._render_character_panel(screen, game_state)
        self._render_inventory_panel(screen, game_state)
        self._render_stats_panel(screen, game_state)
        
        # Render drag preview if dragging
        if self.drag_state != DragState.NONE and self.drag_data:
            self._render_drag_preview(screen)

        # Render tooltip
        if self.current_tooltip:
            mouse_pos = pygame.mouse.get_pos()
            self.tooltip_renderer.render_tooltip(screen, self.current_tooltip, mouse_pos)
    
    def _get_equipment_slot_at_pos(self, pos: Tuple[int, int]) -> Optional[str]:
        """Get equipment slot name at the given position."""
        char_x, char_y = self.layout.character_panel_pos
        
        for slot_name, slot_data in self.layout.equipment_slots.items():
            slot_pos = slot_data["position"]
            slot_size = slot_data["size"]
            
            # Convert to screen coordinates
            screen_x = char_x + slot_pos[0]
            screen_y = char_y + slot_pos[1]
            
            slot_rect = pygame.Rect(screen_x, screen_y, slot_size[0], slot_size[1])
            if slot_rect.collidepoint(pos):
                return slot_name
        
        return None
    
    def _get_inventory_slot_at_pos(self, pos: Tuple[int, int]) -> Optional[int]:
        """Get inventory slot index at the given position."""
        inv_x, inv_y = self.layout.inventory_panel_pos
        grid_start_x = inv_x + 10  # Padding within panel
        grid_start_y = inv_y + 30  # Space for title
        
        cols, rows = self.layout.inventory_grid_size
        slot_size = self.layout.inventory_slot_size[0]
        padding = self.layout.inventory_slot_padding
        
        # Calculate which slot was clicked
        rel_x = pos[0] - grid_start_x
        rel_y = pos[1] - grid_start_y
        
        if rel_x < 0 or rel_y < 0:
            return None
        
        col = rel_x // (slot_size + padding)
        row = rel_y // (slot_size + padding)
        
        if col >= cols or row >= rows:
            return None
        
        # Check if click is within the actual slot (not padding)
        slot_x = col * (slot_size + padding)
        slot_y = row * (slot_size + padding)
        
        if (rel_x >= slot_x and rel_x < slot_x + slot_size and
            rel_y >= slot_y and rel_y < slot_y + slot_size):
            return row * cols + col
        
        return None
    
    def _handle_equipment_slot_click(self, slot_name: str, button: int, game_state: GameStateData) -> GameStateData:
        """Handle clicking on an equipment slot."""
        if button == 1:  # Left click
            if game_state.player:
                equipped_item = game_state.player.get_equipped_item_in_slot(slot_name)
                if equipped_item:
                    # Unequip item
                    try:
                        return self.unequip_item_use_case.execute(game_state, slot_name)
                    except Exception as e:
                        print(f"Failed to unequip item: {e}")
        
        return game_state
    
    def _handle_inventory_slot_click(self, slot_index: int, button: int, game_state: GameStateData) -> GameStateData:
        """Handle clicking on an inventory slot."""
        if button == 1:  # Left click
            # Get inventory data
            try:
                inventory_data = self.get_inventory_use_case.execute(game_state)
                inventory_items = list(inventory_data["inventory"].items())
                
                if slot_index < len(inventory_items):
                    item_id, quantity = inventory_items[slot_index]
                    
                    # Try to equip the item (find appropriate slot)
                    equipment_slot = self._get_item_equipment_slot(item_id)
                    if equipment_slot:
                        try:
                            return self.equip_item_use_case.execute(game_state, item_id, equipment_slot)
                        except Exception as e:
                            print(f"Failed to equip item: {e}")
            except Exception as e:
                print(f"Failed to get inventory: {e}")
        
        return game_state
    
    def _get_item_equipment_slot(self, item_id: str) -> Optional[str]:
        """Get the equipment slot for an item."""
        try:
            from src.game_data.items import ITEMS
            if item_id in ITEMS:
                slot = ITEMS[item_id].properties.get("slot")
                # Map item slot names to player equipment slot names
                slot_mapping = {
                    "head": "head_equipment",
                    "chest": "chest_equipment",
                    "legs": "legs_equipment",
                    "boots": "boots_equipment",
                    "main_hand": "main_hand_weapon",
                    "off_hand": "off_hand_equipment"
                }
                return slot_mapping.get(slot, slot)
        except ImportError:
            pass
        return None

    def _render_character_panel(self, screen: pygame.Surface, game_state: GameStateData) -> None:
        """Render the character sheet panel."""
        panel_rect = pygame.Rect(*self.layout.character_panel_pos, *self.layout.character_panel_size)

        # Panel background
        panel_surface = pygame.Surface(self.layout.character_panel_size, pygame.SRCALPHA)
        panel_surface.fill(self.colors["background"])
        pygame.draw.rect(panel_surface, self.colors["border"],
                        pygame.Rect(0, 0, *self.layout.character_panel_size), 2)

        # Title - use centralized UI font configuration
        font = get_font(FONT_LARGE)
        title_text = font.render("Character", True, self.colors["text"])
        panel_surface.blit(title_text, (20, 20))

        # Equipment slots
        if game_state.player:
            equipment = game_state.player.get_equipment_dict()

            for slot_name, slot_data in self.layout.equipment_slots.items():
                slot_pos = slot_data["position"]
                slot_size = slot_data["size"]
                slot_rect = pygame.Rect(*slot_pos, *slot_size)

                # Slot background
                slot_color = self.colors["slot_empty"]
                if self.hover_slot and self.hover_slot[0] == "equipment" and self.hover_slot[1] == slot_name:
                    slot_color = self.colors["slot_hover"]

                pygame.draw.rect(panel_surface, slot_color, slot_rect)
                pygame.draw.rect(panel_surface, self.colors["border"], slot_rect, 1)

                # Equipment icon
                equipped_item = equipment.get(slot_name)
                if equipped_item:
                    try:
                        icon_surface = self.asset_manager.get_asset(f"icon.{equipped_item}", slot_size)
                        panel_surface.blit(icon_surface, slot_pos)
                    except Exception:
                        # Fallback: draw colored rectangle using theme colors
                        pygame.draw.rect(panel_surface, self.colors["slot_empty"], slot_rect)

        screen.blit(panel_surface, self.layout.character_panel_pos)

    def _render_inventory_panel(self, screen: pygame.Surface, game_state: GameStateData) -> None:
        """Render the inventory panel."""
        panel_rect = pygame.Rect(*self.layout.inventory_panel_pos, *self.layout.inventory_panel_size)

        # Panel background
        panel_surface = pygame.Surface(self.layout.inventory_panel_size, pygame.SRCALPHA)
        panel_surface.fill(self.colors["background"])
        pygame.draw.rect(panel_surface, self.colors["border"],
                        pygame.Rect(0, 0, *self.layout.inventory_panel_size), 2)

        # Title - use centralized UI font configuration
        font = get_font(FONT_LARGE)
        title_text = font.render("Inventory", True, self.colors["text"])
        panel_surface.blit(title_text, (20, 20))

        # Inventory grid
        try:
            inventory_data = self.get_inventory_use_case.execute(game_state)
            inventory_items = list(inventory_data["inventory"].items())

            grid_start_x, grid_start_y = 20, 60  # 2x larger spacing
            cols, rows = self.layout.inventory_grid_size
            slot_size = self.layout.inventory_slot_size[0]
            padding = self.layout.inventory_slot_padding

            for row in range(rows):
                for col in range(cols):
                    slot_index = row * cols + col
                    slot_x = grid_start_x + col * (slot_size + padding)
                    slot_y = grid_start_y + row * (slot_size + padding)
                    slot_rect = pygame.Rect(slot_x, slot_y, slot_size, slot_size)

                    # Slot background
                    slot_color = self.colors["slot_empty"]
                    if self.hover_slot and self.hover_slot[0] == "inventory" and self.hover_slot[1] == slot_index:
                        slot_color = self.colors["slot_hover"]

                    pygame.draw.rect(panel_surface, slot_color, slot_rect)
                    pygame.draw.rect(panel_surface, self.colors["border"], slot_rect, 1)

                    # Item icon and quantity
                    if slot_index < len(inventory_items):
                        item_id, quantity = inventory_items[slot_index]

                        # Item icon
                        try:
                            icon_surface = self.asset_manager.get_asset(f"icon.{item_id}", (slot_size, slot_size))
                            panel_surface.blit(icon_surface, (slot_x, slot_y))
                        except Exception:
                            # Fallback: draw colored rectangle using theme colors
                            pygame.draw.rect(panel_surface, self.colors["slot_empty"], slot_rect)

                        # Quantity text
                        if quantity > 1:
                            font_quantity = get_font(FONT_NORMAL)  # Use centralized font system with larger size
                            qty_text = font_quantity.render(str(quantity), True, self.colors["text"])
                            text_pos = (slot_x + slot_size - qty_text.get_width() - 2,
                                       slot_y + slot_size - qty_text.get_height() - 2)
                            panel_surface.blit(qty_text, text_pos)

        except Exception as e:
            print(f"Error rendering inventory: {e}")

        screen.blit(panel_surface, self.layout.inventory_panel_pos)

    def _render_stats_panel(self, screen: pygame.Surface, game_state: GameStateData) -> None:
        """Render the stats panel."""
        panel_rect = pygame.Rect(*self.layout.stats_panel_pos, *self.layout.stats_panel_size)

        # Panel background
        panel_surface = pygame.Surface(self.layout.stats_panel_size, pygame.SRCALPHA)
        panel_surface.fill(self.colors["background"])
        pygame.draw.rect(panel_surface, self.colors["border"],
                        pygame.Rect(0, 0, *self.layout.stats_panel_size), 2)

        # Title - use centralized UI font configuration
        font = get_font(FONT_LARGE)
        title_text = font.render("Stats", True, self.colors["text"])
        panel_surface.blit(title_text, (20, 20))

        if game_state.player:
            try:
                # Get player stats with equipment bonuses
                stats_data = self.get_player_stats_use_case.execute(game_state)

                # Use centralized UI font configuration for stats
                font_small = get_font(FONT_NORMAL)
                font_tiny = get_font(FONT_SMALL)
                y_offset = 40
                line_height = 25

                # Basic info
                name_text = font_small.render(f"Name: {stats_data['name']}", True, self.colors["text"])
                panel_surface.blit(name_text, (10, y_offset))
                y_offset += line_height

                level_text = font_small.render(f"Level: {stats_data['level']}", True, self.colors["text"])
                panel_surface.blit(level_text, (10, y_offset))
                y_offset += line_height

                exp_text = font_small.render(f"Exp: {stats_data['experience']}", True, self.colors["text"])
                panel_surface.blit(exp_text, (10, y_offset))
                y_offset += line_height * 2

                # Health and Mana bars (use total stats)
                total_stats = stats_data["total_stats"]
                self._render_stat_bar(panel_surface, "HP", total_stats["hp"], total_stats["max_hp"],
                                    (10, y_offset), (180, 20), self.colors["health"])
                y_offset += 30

                self._render_stat_bar(panel_surface, "MP", total_stats["mp"], total_stats["max_mp"],
                                    (10, y_offset), (180, 20), self.colors["mana"])
                y_offset += 40

                # Core stats with equipment bonuses
                base_stats = stats_data["base_stats"]
                equipment_bonuses = stats_data["equipment_bonuses"]

                # Strength
                strength_bonus = equipment_bonuses.get("strength_bonus", 0)
                strength_text = f"Strength: {base_stats['strength']}"
                if strength_bonus != 0:
                    strength_text += f" (+{strength_bonus})" if strength_bonus > 0 else f" ({strength_bonus})"
                text_surface = font_small.render(strength_text, True, self.colors["text"])
                panel_surface.blit(text_surface, (10, y_offset))
                y_offset += line_height

                # Defense
                defense_bonus = equipment_bonuses.get("defense_bonus", 0)
                defense_text = f"Defense: {base_stats['defense']}"
                if defense_bonus != 0:
                    defense_text += f" (+{defense_bonus})" if defense_bonus > 0 else f" ({defense_bonus})"
                text_surface = font_small.render(defense_text, True, self.colors["text"])
                panel_surface.blit(text_surface, (10, y_offset))
                y_offset += line_height

                # Speed
                speed_bonus = equipment_bonuses.get("speed_bonus", 0)
                speed_text = f"Speed: {base_stats['speed']}"
                if speed_bonus != 0:
                    speed_text += f" (+{speed_bonus})" if speed_bonus > 0 else f" ({speed_bonus})"
                text_surface = font_small.render(speed_text, True, self.colors["text"])
                panel_surface.blit(text_surface, (10, y_offset))
                y_offset += line_height

                # Damage bonus (if any)
                damage_bonus = equipment_bonuses.get("damage_bonus", 0)
                if damage_bonus > 0:
                    damage_text = f"Damage: +{damage_bonus}"
                    text_surface = font_small.render(damage_text, True, self.colors["stat_positive"])
                    panel_surface.blit(text_surface, (10, y_offset))
                    y_offset += line_height

                y_offset += 10

                # Inventory status
                try:
                    inventory_data = self.get_inventory_use_case.execute(game_state)
                    used_slots = inventory_data["space_used"]
                    max_slots = 20  # inventory_data["max_size"]
                    inv_text = font_small.render(f"Inventory: {used_slots}/{max_slots}", True, self.colors["text"])
                    panel_surface.blit(inv_text, (10, y_offset))
                except Exception:
                    pass

            except Exception as e:
                print(f"Error rendering stats panel: {e}")
                # Fallback to basic rendering
                player = game_state.player
                font_small = pygame.font.Font(None, 20)
                y_offset = 40

                name_text = font_small.render(f"Name: {player.name}", True, self.colors["text"])
                panel_surface.blit(name_text, (10, y_offset))

        screen.blit(panel_surface, self.layout.stats_panel_pos)

    def _render_stat_bar(self, surface: pygame.Surface, label: str, current: int, maximum: int,
                        pos: Tuple[int, int], size: Tuple[int, int], color: Tuple[int, int, int]) -> None:
        """Render a stat bar (health/mana)."""
        x, y = pos
        width, height = size

        # Background using theme colors
        bg_rect = pygame.Rect(x, y, width, height)
        pygame.draw.rect(surface, self.colors["progress_bg"], bg_rect)
        pygame.draw.rect(surface, self.colors["border"], bg_rect, 1)

        # Fill bar
        if maximum > 0:
            fill_width = int((current / maximum) * (width - 4))
            fill_rect = pygame.Rect(x + 2, y + 2, fill_width, height - 4)
            pygame.draw.rect(surface, color, fill_rect)

        # Text
        font = get_font(FONT_SMALL)  # Use centralized font system
        text = f"{label}: {current}/{maximum}"
        text_surface = font.render(text, True, self.colors["text"])
        text_x = x + (width - text_surface.get_width()) // 2
        text_y = y + (height - text_surface.get_height()) // 2
        surface.blit(text_surface, (text_x, text_y))

    def _render_drag_preview(self, screen: pygame.Surface) -> None:
        """Render drag preview when dragging items."""
        if not self.drag_data or not self.drag_data.drag_surface:
            return

        mouse_pos = pygame.mouse.get_pos()
        preview_pos = (mouse_pos[0] + self.drag_data.offset[0],
                      mouse_pos[1] + self.drag_data.offset[1])

        # Semi-transparent preview
        preview_surface = self.drag_data.drag_surface.copy()
        preview_surface.set_alpha(180)
        screen.blit(preview_surface, preview_pos)

    def _start_equipment_drag(self, item_id: str, slot_name: str, mouse_pos: Tuple[int, int]) -> None:
        """Start dragging an item from an equipment slot."""
        try:
            # Create drag surface
            drag_surface = self.asset_manager.get_asset(f"icon.{item_id}", (32, 32))

            self.drag_state = DragState.DRAGGING_EQUIPMENT_ITEM
            self.drag_data = DragData(
                item_id=item_id,
                source_type="equipment",
                source_slot=slot_name,
                drag_surface=drag_surface,
                offset=(-16, -16)  # Center the icon on cursor
            )
        except Exception as e:
            print(f"Error starting equipment drag: {e}")

    def _start_inventory_drag(self, item_id: str, slot_index: int, mouse_pos: Tuple[int, int]) -> None:
        """Start dragging an item from an inventory slot."""
        try:
            # Create drag surface
            drag_surface = self.asset_manager.get_asset(f"icon.{item_id}", (32, 32))

            self.drag_state = DragState.DRAGGING_INVENTORY_ITEM
            self.drag_data = DragData(
                item_id=item_id,
                source_type="inventory",
                source_index=slot_index,
                drag_surface=drag_surface,
                offset=(-16, -16)  # Center the icon on cursor
            )
        except Exception as e:
            print(f"Error starting inventory drag: {e}")

    def _handle_drop(self, pos: Tuple[int, int], game_state: GameStateData) -> GameStateData:
        """Handle dropping an item at the given position."""
        if not self.drag_data:
            return game_state

        # Check if dropping on equipment slot
        equipment_slot = self._get_equipment_slot_at_pos(pos)
        if equipment_slot:
            return self._handle_drop_on_equipment(equipment_slot, game_state)

        # Check if dropping on inventory slot
        inventory_slot = self._get_inventory_slot_at_pos(pos)
        if inventory_slot is not None:
            return self._handle_drop_on_inventory(inventory_slot, game_state)

        # Drop outside valid area - cancel drag
        return game_state

    def _handle_drop_on_equipment(self, target_slot: str, game_state: GameStateData) -> GameStateData:
        """Handle dropping an item on an equipment slot."""
        if not self.drag_data:
            return game_state

        item_id = self.drag_data.item_id

        # Validate that item can be equipped in this slot
        item_slot = self._get_item_equipment_slot(item_id)
        if item_slot != target_slot:
            print(f"Item {item_id} cannot be equipped in slot {target_slot}")
            return game_state

        try:
            if self.drag_data.source_type == "inventory":
                # Equipping from inventory
                return self.equip_item_use_case.execute(game_state, item_id, target_slot)

            elif self.drag_data.source_type == "equipment":
                # Moving between equipment slots
                if self.drag_data.source_slot != target_slot:
                    # Unequip from source, then equip to target
                    temp_state = self.unequip_item_use_case.execute(game_state, self.drag_data.source_slot)
                    return self.equip_item_use_case.execute(temp_state, item_id, target_slot)
                else:
                    # Dropping on the same slot - do nothing
                    return game_state

        except Exception as e:
            print(f"Error dropping on equipment: {e}")

        return game_state

    def _handle_drop_on_inventory(self, target_slot: int, game_state: GameStateData) -> GameStateData:
        """Handle dropping an item on an inventory slot."""
        if not self.drag_data:
            return game_state

        try:
            if self.drag_data.source_type == "equipment":
                # Unequipping to inventory
                return self.unequip_item_use_case.execute(game_state, self.drag_data.source_slot)

            elif self.drag_data.source_type == "inventory":
                # Moving within inventory - rearrange items
                source_index = self.drag_data.source_index
                return self.rearrange_inventory_use_case.execute(game_state, source_index, target_slot)

        except Exception as e:
            print(f"Error dropping on inventory: {e}")

        return game_state

    def _end_drag(self) -> None:
        """End the current drag operation."""
        self.drag_state = DragState.NONE
        self.drag_data = None
        self._cache_dirty = True

    def _is_valid_drop_target(self, pos: Tuple[int, int]) -> bool:
        """Check if the position is a valid drop target for the current drag."""
        if not self.drag_data:
            return False

        # Check equipment slots
        equipment_slot = self._get_equipment_slot_at_pos(pos)
        if equipment_slot:
            item_slot = self._get_item_equipment_slot(self.drag_data.item_id)
            return item_slot == equipment_slot

        # Check inventory slots
        inventory_slot = self._get_inventory_slot_at_pos(pos)
        if inventory_slot is not None:
            return True

        return False

    def _update_tooltip(self, game_state: GameStateData) -> None:
        """Update the current tooltip based on hover state."""
        self.current_tooltip = None
        self.tooltip_timer = 0

        if not self.hover_slot:
            return

        hover_type, hover_target = self.hover_slot

        try:
            if hover_type == "equipment" and game_state.player:
                # Show tooltip for equipped item
                equipped_item = game_state.player.get_equipped_item_in_slot(hover_target)
                if equipped_item:
                    self.current_tooltip = self.tooltip_renderer.generate_item_tooltip(equipped_item, game_state)

            elif hover_type == "inventory":
                # Show tooltip for inventory item
                inventory_data = self.get_inventory_use_case.execute(game_state)
                inventory_items = list(inventory_data["inventory"].items())

                if hover_target < len(inventory_items):
                    item_id, quantity = inventory_items[hover_target]
                    self.current_tooltip = self.tooltip_renderer.generate_item_tooltip(item_id, game_state)

        except Exception as e:
            print(f"Error updating tooltip: {e}")
