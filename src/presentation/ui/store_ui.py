"""
Store Interface UI

This module manages the store interface UI for NPC merchant interactions.
"""

import pygame
from typing import Optional, Dict, Any, Tuple, List, Callable
from dataclasses import dataclass
from enum import Enum

from src.application.interfaces import GameStateData
from src.application.use_cases import BuyFromNPCUseCase, SellToNPCUseCase
from src.game_core.config import get_config
from src.editor.ui_config import get_font, get_element_size, get_spacing, scale_value, FONT_LARGE, FONT_NORMAL, FONT_SMALL, get_colors, draw_modern_button
from .tooltip import TooltipRenderer, TooltipData


class StoreMode(Enum):
    """Store interface modes."""
    BUY = "buy"
    SELL = "sell"


@dataclass
class StoreData:
    """Data for store interface."""
    npc_id: str
    npc_name: str
    npc_type: str
    npc_inventory: Dict[str, int]
    player_inventory: Dict[str, int]
    player_gold: int
    is_visible: bool = False


class StoreUIController:
    """Controller for the store interface UI."""
    
    def __init__(self, buy_use_case: BuyFromNPCUseCase, sell_use_case: SellToNPCUseCase):
        """Initialize the store UI controller."""
        self.buy_use_case = buy_use_case
        self.sell_use_case = sell_use_case
        self.store_data: Optional[StoreData] = None
        self.config = get_config()
        
        # UI state
        self.current_mode = StoreMode.BUY
        self.selected_item: Optional[str] = None
        self.selected_quantity = 1
        self.scroll_offset = 0
        
        # UI configuration with modern blue-grey theme
        self.background_color = (45, 50, 60, 200)    # Modern blue-grey background
        self.border_color = (70, 80, 95, 255)        # Blue-grey border
        self.text_color = (240, 245, 250, 255)       # Almost white text
        self.highlight_color = (100, 140, 180, 100)  # Blue highlight
        self.button_color = (60, 120, 180, 255)      # Primary blue button
        self.button_hover_color = (70, 130, 190, 255) # Lighter blue on hover
        
        # Layout settings
        self.padding = scale_value(20)
        self.border_width = scale_value(2)
        self.item_height = scale_value(40)
        self.button_height = scale_value(35)
        
        # Fonts
        self.title_font = get_font(FONT_LARGE)
        self.text_font = get_font(FONT_NORMAL)
        self.small_font = get_font(FONT_SMALL)
        
        # Store dimensions (percentage of screen)
        self.store_width_percent = 0.8
        self.store_height_percent = 0.7
        
        # Tooltip system
        self.tooltip_renderer = TooltipRenderer()
        self.current_tooltip: Optional[TooltipData] = None
        
        # Callbacks
        self.on_transaction_complete: Optional[Callable[[GameStateData], None]] = None
        
    def show_store(self, npc_id: str, npc_name: str, npc_type: str, 
                   npc_inventory: Dict[str, int], player_inventory: Dict[str, int], 
                   player_gold: int) -> None:
        """
        Show the store interface.
        
        Args:
            npc_id: ID of the NPC
            npc_name: Name of the NPC
            npc_type: Type of the NPC
            npc_inventory: NPC's inventory
            player_inventory: Player's inventory
            player_gold: Player's gold amount
        """
        self.store_data = StoreData(
            npc_id=npc_id,
            npc_name=npc_name,
            npc_type=npc_type,
            npc_inventory=npc_inventory,
            player_inventory=player_inventory,
            player_gold=player_gold,
            is_visible=True
        )
        self.current_mode = StoreMode.BUY
        self.selected_item = None
        self.scroll_offset = 0
        
    def hide_store(self) -> None:
        """Hide the store interface."""
        if self.store_data:
            self.store_data.is_visible = False
        self.store_data = None
        self.current_tooltip = None
        
    def is_store_visible(self) -> bool:
        """Check if store is currently visible."""
        return self.store_data is not None and self.store_data.is_visible
        
    def set_transaction_callback(self, callback: Callable[[GameStateData], None]) -> None:
        """Set callback for when transactions complete."""
        self.on_transaction_complete = callback
        
    def handle_event(self, event: pygame.event.Event, game_state: GameStateData) -> bool:
        """
        Handle UI events.
        
        Args:
            event: Pygame event
            game_state: Current game state
            
        Returns:
            True if event was consumed
        """
        if not self.is_store_visible():
            return False
            
        if event.type == pygame.KEYDOWN:
            if event.key == pygame.K_ESCAPE:
                self.hide_store()
                return True
            elif event.key == pygame.K_TAB:
                # Switch between buy/sell modes
                self.current_mode = StoreMode.SELL if self.current_mode == StoreMode.BUY else StoreMode.BUY
                self.selected_item = None
                return True
                
        elif event.type == pygame.MOUSEBUTTONDOWN:
            if event.button == 1:  # Left click
                mouse_pos = pygame.mouse.get_pos()
                return self._handle_click(mouse_pos, game_state)
                
        elif event.type == pygame.MOUSEMOTION:
            mouse_pos = pygame.mouse.get_pos()
            self._handle_mouse_hover(mouse_pos)
            
        return False
        
    def _handle_click(self, mouse_pos: Tuple[int, int], game_state: GameStateData) -> bool:
        """Handle mouse clicks."""
        screen_width, screen_height = pygame.display.get_surface().get_size()
        store_width = int(screen_width * self.store_width_percent)
        store_height = int(screen_height * self.store_height_percent)
        store_x = (screen_width - store_width) // 2
        store_y = (screen_height - store_height) // 2

        store_rect = pygame.Rect(store_x, store_y, store_width, store_height)
        if not store_rect.collidepoint(mouse_pos):
            self.hide_store()
            return True

        # Convert to relative coordinates within the store
        rel_x = mouse_pos[0] - store_x
        rel_y = mouse_pos[1] - store_y

        # Check if click is on mode tabs
        tab_y = self.padding + scale_value(40)  # Approximate title height + spacing
        if self._handle_tab_click(rel_x, rel_y, tab_y):
            return True

        # Check if click is on item list
        list_y = tab_y + self.button_height + scale_value(10)
        if self._handle_item_click(rel_x, rel_y, list_y, store_height - list_y - self.padding):
            return True

        # Check if click is on buy/sell button
        if self.selected_item and self._handle_action_button_click(rel_x, rel_y, store_height, game_state):
            return True

        return False
        
    def _handle_tab_click(self, rel_x: int, rel_y: int, tab_y: int) -> bool:
        """Handle clicks on mode tabs."""
        tab_width = scale_value(100)
        tab_spacing = scale_value(10)

        # Check buy tab
        buy_rect = pygame.Rect(self.padding, tab_y, tab_width, self.button_height)
        if buy_rect.collidepoint(rel_x, rel_y):
            self.current_mode = StoreMode.BUY
            self.selected_item = None
            return True

        # Check sell tab
        sell_rect = pygame.Rect(self.padding + tab_width + tab_spacing, tab_y, tab_width, self.button_height)
        if sell_rect.collidepoint(rel_x, rel_y):
            self.current_mode = StoreMode.SELL
            self.selected_item = None
            return True

        return False

    def _handle_item_click(self, rel_x: int, rel_y: int, list_y: int, list_height: int) -> bool:
        """Handle clicks on items in the list."""
        # Get items to display based on current mode
        if self.current_mode == StoreMode.BUY:
            items = self.store_data.npc_inventory
        else:
            items = self.store_data.player_inventory

        # Calculate item positions
        item_y = list_y + scale_value(25)  # Account for title
        item_list = list(items.items())

        for i, (item_id, quantity) in enumerate(item_list):
            item_rect_y = item_y + i * self.item_height

            # Skip if item is not visible
            if item_rect_y + self.item_height > list_y + list_height:
                break
            if item_rect_y < list_y:
                continue

            # Check if click is on this item
            item_rect = pygame.Rect(self.padding, item_rect_y,
                                  400, self.item_height)  # Use fixed width for now
            if item_rect.collidepoint(rel_x, rel_y):
                # Only select the item, don't immediately buy/sell
                self.selected_item = item_id
                return True

        return False

    def _handle_action_button_click(self, rel_x: int, rel_y: int, store_height: int, game_state: GameStateData) -> bool:
        """Handle clicks on the buy/sell action button."""
        if not self.selected_item:
            return False

        # Calculate button position (bottom of store, above instructions)
        button_width = scale_value(200)
        button_height = scale_value(40)
        button_x = self.padding
        button_y = store_height - self.padding - scale_value(30) - button_height  # Above instructions

        button_rect = pygame.Rect(button_x, button_y, button_width, button_height)

        if button_rect.collidepoint(rel_x, rel_y):
            self._handle_item_transaction(self.selected_item, game_state)
            return True

        return False

    def _handle_item_transaction(self, item_id: str, game_state: GameStateData) -> None:
        """Handle buying or selling an item."""
        if not self.store_data:
            return

        try:
            if self.current_mode == StoreMode.BUY:
                # Buy item from NPC
                print(f"Attempting to buy {item_id} from {self.store_data.npc_name}")
                updated_game_state = self.buy_use_case.execute(game_state, self.store_data.npc_id, item_id, 1)

                # Update store data with new player inventory and gold
                self.store_data.player_inventory = updated_game_state.player.inventory
                self.store_data.player_gold = updated_game_state.player.inventory.get("gold_coin", 0)
                # Update NPC inventory if needed
                if self.store_data.npc_id in updated_game_state.npcs:
                    self.store_data.npc_inventory = updated_game_state.npcs[self.store_data.npc_id].inventory

                # Notify game engine of the updated state
                if self.on_transaction_complete:
                    self.on_transaction_complete(updated_game_state)

            else:
                # Sell item to NPC
                print(f"Attempting to sell {item_id} to {self.store_data.npc_name}")
                updated_game_state = self.sell_use_case.execute(game_state, self.store_data.npc_id, item_id, 1)

                # Update store data with new player inventory and gold
                self.store_data.player_inventory = updated_game_state.player.inventory
                self.store_data.player_gold = updated_game_state.player.inventory.get("gold_coin", 0)
                # Update NPC inventory if needed
                if self.store_data.npc_id in updated_game_state.npcs:
                    self.store_data.npc_inventory = updated_game_state.npcs[self.store_data.npc_id].inventory

                # Notify game engine of the updated state
                if self.on_transaction_complete:
                    self.on_transaction_complete(updated_game_state)

        except Exception as e:
            print(f"Transaction failed: {e}")

    def _handle_mouse_hover(self, mouse_pos: Tuple[int, int]) -> None:
        """Handle mouse hover for tooltips."""
        # Implementation would check if mouse is over items and show tooltips
        pass
        
    def update(self, dt: float) -> None:
        """
        Update the store UI.
        
        Args:
            dt: Delta time in seconds
        """
        # Update any animations or state changes
        pass
        
    def render(self, screen: pygame.Surface) -> None:
        """
        Render the store UI.
        
        Args:
            screen: Surface to render to
        """
        if not self.store_data:
            return
            
        screen_width, screen_height = screen.get_size()
        
        # Calculate store dimensions and position
        store_width = int(screen_width * self.store_width_percent)
        store_height = int(screen_height * self.store_height_percent)
        store_x = (screen_width - store_width) // 2
        store_y = (screen_height - store_height) // 2
        
        # Create store surface
        store_surface = pygame.Surface((store_width, store_height), pygame.SRCALPHA)
        
        # Draw background
        pygame.draw.rect(store_surface, self.background_color, (0, 0, store_width, store_height))
        pygame.draw.rect(store_surface, self.border_color, (0, 0, store_width, store_height), self.border_width)
        
        # Draw title
        title_text = f"{self.store_data.npc_name}'s Shop ({self.store_data.npc_type.title()})"
        title_surface = self.title_font.render(title_text, True, self.text_color)
        title_x = (store_width - title_surface.get_width()) // 2
        store_surface.blit(title_surface, (title_x, self.padding))
        
        # Draw mode tabs
        tab_y = self.padding + title_surface.get_height() + scale_value(10)
        self._draw_mode_tabs(store_surface, tab_y, store_width)
        
        # Draw player gold
        gold_text = f"Gold: {self.store_data.player_gold}"
        gold_surface = self.text_font.render(gold_text, True, self.text_color)
        gold_x = store_width - gold_surface.get_width() - self.padding
        store_surface.blit(gold_surface, (gold_x, tab_y))
        
        # Draw item list
        list_y = tab_y + self.button_height + scale_value(10)
        action_button_height = scale_value(40) if self.selected_item else 0
        instruction_height = scale_value(30)
        available_list_height = store_height - list_y - self.padding - action_button_height - instruction_height
        self._draw_item_list(store_surface, list_y, store_width, available_list_height)

        # Draw buy/sell button if item is selected
        if self.selected_item:
            self._draw_action_button(store_surface, store_width, store_height)

        # Draw instructions
        instruction_text = "TAB: Switch Buy/Sell | ESC: Close | Click item to select"
        instruction_surface = self.small_font.render(instruction_text, True, self.text_color)
        instruction_x = self.padding
        instruction_y = store_height - instruction_surface.get_height() - self.padding
        store_surface.blit(instruction_surface, (instruction_x, instruction_y))
        
        # Blit store to screen
        screen.blit(store_surface, (store_x, store_y))
        
        # Render tooltip if any
        if self.current_tooltip:
            mouse_pos = pygame.mouse.get_pos()
            self.tooltip_renderer.render_tooltip(screen, self.current_tooltip, mouse_pos)
            
    def _draw_mode_tabs(self, surface: pygame.Surface, y: int, width: int) -> None:
        """Draw the buy/sell mode tabs using modern button system."""
        from src.editor.ui_config import draw_modern_button
        
        tab_width = scale_value(100)
        tab_spacing = scale_value(10)
        
        # Buy tab
        buy_rect = pygame.Rect(self.padding, y, tab_width, self.button_height)
        is_buy_active = self.current_mode == StoreMode.BUY
        
        # Get color scheme for the button
        colors = get_colors()
        if is_buy_active:
            normal_color = colors['button_primary']
            hover_color = colors['button_primary_hover']
            active_color = colors['button_primary_active']
        else:
            normal_color = colors['button_secondary']
            hover_color = colors['button_secondary_hover']
            active_color = colors['button_secondary_active']
        
        draw_modern_button(
            screen=surface,
            rect=buy_rect,
            normal_color=normal_color,
            hover_color=hover_color,
            active_color=active_color,
            is_hovered=False,  # TODO: Add hover detection
            is_active=is_buy_active
        )
        
        # Render text on button
        font = get_font(FONT_NORMAL)
        text_surface = font.render("Buy", True, colors['text_primary'])
        text_rect = text_surface.get_rect(center=buy_rect.center)
        surface.blit(text_surface, text_rect)
        
        # Sell tab
        sell_rect = pygame.Rect(self.padding + tab_width + tab_spacing, y, tab_width, self.button_height)
        is_sell_active = self.current_mode == StoreMode.SELL
        
        # Get color scheme for the sell button
        if is_sell_active:
            normal_color = colors['button_primary']
            hover_color = colors['button_primary_hover']
            active_color = colors['button_primary_active']
        else:
            normal_color = colors['button_secondary']
            hover_color = colors['button_secondary_hover']
            active_color = colors['button_secondary_active']
        
        draw_modern_button(
            screen=surface,
            rect=sell_rect,
            normal_color=normal_color,
            hover_color=hover_color,
            active_color=active_color,
            is_hovered=False,  # TODO: Add hover detection
            is_active=is_sell_active
        )
        
        # Render text on button
        text_surface = font.render("Sell", True, colors['text_primary'])
        text_rect = text_surface.get_rect(center=sell_rect.center)
        surface.blit(text_surface, text_rect)

    def _draw_action_button(self, surface: pygame.Surface, store_width: int, store_height: int) -> None:
        """Draw the buy/sell action button when an item is selected."""
        if not self.selected_item:
            return

        # Get item info for pricing
        try:
            from src.game_data.items import ITEMS
            if self.selected_item in ITEMS:
                item_def = ITEMS[self.selected_item]
                if self.current_mode == StoreMode.BUY:
                    price = item_def.value
                    button_text = f"Buy {price} Gold"
                else:
                    price = max(1, item_def.value // 2)  # Sell for half price
                    button_text = f"Sell {price} Gold"
            else:
                button_text = "Buy" if self.current_mode == StoreMode.BUY else "Sell"
        except ImportError:
            button_text = "Buy" if self.current_mode == StoreMode.BUY else "Sell"

        # Button positioning
        button_width = scale_value(200)
        button_height = scale_value(40)
        button_x = self.padding
        button_y = store_height - self.padding - scale_value(30) - button_height

        button_rect = pygame.Rect(button_x, button_y, button_width, button_height)

        # Get colors and draw modern button
        colors = get_colors()
        mouse_pos = pygame.mouse.get_pos()
        # Convert mouse position to relative coordinates
        store_x = (pygame.display.get_surface().get_width() - store_width) // 2
        store_y = (pygame.display.get_surface().get_height() - store_height) // 2
        rel_mouse_x = mouse_pos[0] - store_x
        rel_mouse_y = mouse_pos[1] - store_y
        is_hovered = button_rect.collidepoint(rel_mouse_x, rel_mouse_y)

        draw_modern_button(
            screen=surface,
            rect=button_rect,
            normal_color=colors['button_primary'],
            hover_color=colors['button_primary_hover'],
            active_color=colors['button_primary_active'],
            is_hovered=is_hovered,
            is_active=False
        )

        # Render button text
        text_surface = self.text_font.render(button_text, True, colors['text_primary'])
        text_rect = text_surface.get_rect(center=button_rect.center)
        surface.blit(text_surface, text_rect)

    def _draw_item_list(self, surface: pygame.Surface, y: int, width: int, height: int) -> None:
        """Draw the list of items available for buy/sell."""
        # Get items to display based on current mode
        if self.current_mode == StoreMode.BUY:
            items = self.store_data.npc_inventory
            title = "Available Items:"
        else:
            items = self.store_data.player_inventory
            title = "Your Items:"
            
        # Draw section title
        title_surface = self.text_font.render(title, True, self.text_color)
        surface.blit(title_surface, (self.padding, y))
        
        # Draw items as selectable slots
        item_y = y + title_surface.get_height() + scale_value(5)
        colors = get_colors()

        for item_id, quantity in items.items():
            if item_y + self.item_height > y + height:
                break  # Don't draw items that would overflow

            # Get item info
            try:
                from src.game_data.items import ITEMS
                if item_id in ITEMS:
                    item_def = ITEMS[item_id]
                    item_name = item_def.name
                    item_value = item_def.value
                    if self.current_mode == StoreMode.BUY:
                        price_text = f"{item_value}g"
                    else:
                        sell_price = max(1, item_value // 2)
                        price_text = f"{sell_price}g"
                else:
                    item_name = item_id
                    price_text = "?g"
            except ImportError:
                item_name = item_id
                price_text = "?g"

            # Create item slot rectangle
            slot_rect = pygame.Rect(self.padding, item_y, width - self.padding * 2, self.item_height)

            # Determine slot appearance based on selection
            is_selected = self.selected_item == item_id

            # Draw slot background with modern styling
            if is_selected:
                slot_color = colors['button_primary']
                border_color = colors['border_accent']
                border_width = 2
            else:
                slot_color = colors['background_secondary']
                border_color = colors['border_primary']
                border_width = 1

            # Draw slot background
            pygame.draw.rect(surface, slot_color, slot_rect)
            pygame.draw.rect(surface, border_color, slot_rect, border_width)

            # Draw item icon area (left side of slot)
            icon_size = self.item_height - scale_value(8)
            icon_rect = pygame.Rect(slot_rect.x + scale_value(4),
                                  slot_rect.y + scale_value(4),
                                  icon_size, icon_size)
            pygame.draw.rect(surface, colors['background_dark'], icon_rect)
            pygame.draw.rect(surface, border_color, icon_rect, 1)

            # Try to draw item icon (placeholder for now)
            icon_text = item_name[:2].upper()  # First 2 letters as placeholder
            icon_font = self.small_font
            icon_surface = icon_font.render(icon_text, True, colors['text_secondary'])
            icon_text_rect = icon_surface.get_rect(center=icon_rect.center)
            surface.blit(icon_surface, icon_text_rect)

            # Draw item name and details
            text_x = icon_rect.right + scale_value(8)
            text_y = slot_rect.y + scale_value(4)

            # Item name
            name_surface = self.text_font.render(item_name, True, colors['text_primary'])
            surface.blit(name_surface, (text_x, text_y))

            # Quantity and price on second line
            details_y = text_y + name_surface.get_height() + scale_value(2)
            details_text = f"Qty: {quantity} | Price: {price_text}"
            details_surface = self.small_font.render(details_text, True, colors['text_secondary'])
            surface.blit(details_surface, (text_x, details_y))

            item_y += self.item_height + scale_value(2)  # Small gap between items
