"""
Help UI Controller

This module manages the help panel overlay showing game controls and keybindings.
"""

import pygame
from typing import Optional, Dict, Any, Tuple, List, Callable
from dataclasses import dataclass

from src.game_core.config import get_config
from src.editor.ui_config import (
    get_font, get_element_size, get_spacing, scale_value, get_colors,
    FONT_LARGE, FONT_NORMAL, FONT_SMALL
)


@dataclass
class HelpData:
    """Data for help interface."""
    is_visible: bool = False


class HelpUIController:
    """Controller for the help panel interface."""
    
    def __init__(self):
        """Initialize the help UI controller."""
        self.config = get_config()
        
        # UI state
        self.help_data = HelpData()
        
        # UI configuration
        self.colors = get_colors()
        
        # Layout settings
        self.padding = scale_value(30)
        self.line_height = scale_value(25)
        self.section_spacing = scale_value(15)
        
        # Fonts
        self.title_font = get_font(FONT_LARGE)
        self.section_font = get_font(FONT_NORMAL)
        self.text_font = get_font(FONT_NORMAL)
        self.info_font = get_font(FONT_SMALL)
        
        # Help content organized by sections
        self.help_sections = [
            {
                "title": "Movement",
                "items": [
                    ("W, A, S, D", "Move up, left, down, right"),
                    ("Mouse", "Click to attack in direction")
                ]
            },
            {
                "title": "Interaction",
                "items": [
                    ("E", "Interact with NPCs and objects"),
                    ("I", "Open/close inventory"),
                    ("Tab", "Switch between buy/sell in stores")
                ]
            },
            {
                "title": "Game Management",
                "items": [
                    ("F5", "Quick save"),
                    ("F9", "Quick load"),
                    ("Escape", "Pause game / Open settings"),
                    ("F11", "Toggle fullscreen"),
                    ("R", "Reload current level (debug)")
                ]
            },
            {
                "title": "UI Navigation",
                "items": [
                    ("Mouse", "Click buttons and UI elements"),
                    ("Escape", "Close current UI panel"),
                    ("Arrow Keys", "Navigate menus (where applicable)"),
                    ("Enter/Space", "Activate selected button")
                ]
            }
        ]
        
        # Callbacks
        self.on_close: Optional[Callable] = None
    
    def show_help(self) -> None:
        """Show the help interface."""
        self.help_data.is_visible = True
    
    def hide_help(self) -> None:
        """Hide the help interface."""
        self.help_data.is_visible = False
    
    def is_visible(self) -> bool:
        """Check if help interface is visible."""
        return self.help_data.is_visible
    
    def handle_event(self, event: pygame.event.Event) -> bool:
        """
        Handle UI events.
        
        Args:
            event: Pygame event
            
        Returns:
            True if event was consumed
        """
        if not self.is_visible():
            return False
        
        if event.type == pygame.KEYDOWN:
            if event.key == pygame.K_ESCAPE:
                if self.on_close:
                    self.on_close()
                self.hide_help()
                return True
        
        elif event.type == pygame.MOUSEBUTTONDOWN:
            if event.button == 1:  # Left click
                if self.on_close:
                    self.on_close()
                self.hide_help()
                return True
        
        return True  # Consume all events when visible
    
    def render(self, surface: pygame.Surface) -> None:
        """
        Render the help interface.
        
        Args:
            surface: Surface to render on
        """
        if not self.is_visible():
            return
        
        screen_width = self.config.rendering.screen_width
        screen_height = self.config.rendering.screen_height
        
        # Draw semi-transparent overlay
        overlay = pygame.Surface((screen_width, screen_height))
        overlay.set_alpha(180)
        overlay.fill((0, 0, 0))
        surface.blit(overlay, (0, 0))
        
        # Calculate panel dimensions
        panel_width = scale_value(700)
        panel_height = scale_value(500)
        panel_x = (screen_width - panel_width) // 2
        panel_y = (screen_height - panel_height) // 2
        
        # Draw panel background
        panel_rect = pygame.Rect(panel_x, panel_y, panel_width, panel_height)
        pygame.draw.rect(surface, self.colors['background_panel'], panel_rect)
        pygame.draw.rect(surface, self.colors['border'], panel_rect, scale_value(2))
        
        # Draw title
        title_text = "Game Controls & Help"
        title_surface = self.title_font.render(title_text, True, self.colors['text_primary'])
        title_rect = title_surface.get_rect(centerx=panel_x + panel_width // 2, y=panel_y + self.padding)
        surface.blit(title_surface, title_rect)
        
        # Draw help sections
        current_y = panel_y + self.padding + scale_value(50)
        
        for section in self.help_sections:
            # Draw section title
            section_surface = self.section_font.render(section["title"], True, self.colors['text_accent'])
            surface.blit(section_surface, (panel_x + self.padding, current_y))
            current_y += self.line_height + scale_value(5)
            
            # Draw section items
            for key, description in section["items"]:
                # Draw key/control
                key_surface = self.text_font.render(key, True, self.colors['text_primary'])
                surface.blit(key_surface, (panel_x + self.padding + scale_value(20), current_y))
                
                # Draw description
                desc_surface = self.text_font.render(description, True, self.colors['text_secondary'])
                surface.blit(desc_surface, (panel_x + self.padding + scale_value(180), current_y))
                
                current_y += self.line_height
            
            # Add spacing between sections
            current_y += self.section_spacing
        
        # Draw game info section
        current_y += scale_value(10)
        info_title_surface = self.section_font.render("About", True, self.colors['text_accent'])
        surface.blit(info_title_surface, (panel_x + self.padding, current_y))
        current_y += self.line_height + scale_value(5)
        
        game_info = [
            "Treebeard's Revenge - A 2D RPG Adventure",
            "Explore the world, fight monsters, and complete quests!",
            "Visit NPCs to trade items and learn about the world."
        ]
        
        for info_line in game_info:
            info_surface = self.info_font.render(info_line, True, self.colors['text_secondary'])
            surface.blit(info_surface, (panel_x + self.padding + scale_value(20), current_y))
            current_y += scale_value(18)
        
        # Draw close instruction
        close_text = "Press Escape or click anywhere to close"
        close_surface = self.info_font.render(close_text, True, self.colors['text_secondary'])
        close_rect = close_surface.get_rect(centerx=panel_x + panel_width // 2, 
                                          y=panel_y + panel_height - self.padding - scale_value(20))
        surface.blit(close_surface, close_rect)
    
    def toggle_help(self) -> None:
        """Toggle the help panel visibility."""
        if self.is_visible():
            self.hide_help()
        else:
            self.show_help()
