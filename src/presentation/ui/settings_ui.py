"""
Settings UI Controller

This module manages the main settings/menu UI for the game.
"""

import pygame
from typing import Optional, Dict, Any, Tuple, List, Callable
from dataclasses import dataclass
from enum import Enum

from src.application.interfaces import GameStateData, ISaveGameRepository
from src.game_core.config import get_config
from src.editor.ui_config import (
    get_font, get_element_size, get_spacing, scale_value, get_colors,
    FONT_LARGE, FONT_NORMAL, FONT_SMALL, draw_modern_button
)


class GameState(Enum):
    """Game state enumeration."""
    MENU = "menu"
    PLAYING = "playing"
    PAUSED = "paused"


@dataclass
class SettingsData:
    """Data for settings interface."""
    is_visible: bool = False
    current_save_slot: Optional[str] = None
    game_state: GameState = GameState.MENU


class SettingsUIController:
    """Controller for the settings/menu interface UI."""
    
    def __init__(self, save_repository: ISaveGameRepository):
        """Initialize the settings UI controller."""
        self.save_repository = save_repository
        self.config = get_config()
        
        # UI state
        self.settings_data = SettingsData()
        self.selected_button = 0  # For keyboard navigation
        self.show_help = False
        
        # UI configuration with modern blue-grey theme
        self.colors = get_colors()
        
        # Layout settings
        self.padding = scale_value(30)
        self.button_spacing = scale_value(15)
        self.button_width = scale_value(200)
        self.button_height = get_element_size('button_height')
        
        # Fonts
        self.title_font = get_font(FONT_LARGE)
        self.button_font = get_font(FONT_NORMAL)
        self.info_font = get_font(FONT_SMALL)
        
        # Button definitions
        self.buttons = [
            {"text": "New Game", "action": "new_game"},
            {"text": "Load Game", "action": "load_game"},
            {"text": "Save Game", "action": "save_game"},
            {"text": "Toggle Fullscreen", "action": "toggle_fullscreen"},
            {"text": "Help", "action": "help"},
            {"text": "Exit Game", "action": "exit"}
        ]
        
        # Callbacks
        self.on_new_game: Optional[Callable] = None
        self.on_load_game: Optional[Callable[[str], None]] = None
        self.on_save_game: Optional[Callable[[str], None]] = None
        self.on_toggle_fullscreen: Optional[Callable] = None
        self.on_exit_game: Optional[Callable] = None
        
        # Track fullscreen state for button text
        self.is_fullscreen = False
    
    def update_fullscreen_state(self, is_fullscreen: bool) -> None:
        """
        Update the fullscreen state and button text.
        
        Args:
            is_fullscreen: Current fullscreen state
        """
        self.is_fullscreen = is_fullscreen
        # Update the button text
        for button in self.buttons:
            if button["action"] == "toggle_fullscreen":
                button["text"] = "Exit Fullscreen" if is_fullscreen else "Enter Fullscreen"
                break
    
    def _update_buttons_for_game_state(self) -> None:
        """Update button list based on current game state."""
        base_buttons = [
            {"text": "New Game", "action": "new_game"},
            {"text": "Load Game", "action": "load_game"},
        ]
        
        # Add Save Game button if we have an active game
        if self.settings_data.game_state in [GameState.PLAYING, GameState.PAUSED]:
            base_buttons.append({"text": "Save Game", "action": "save_game"})
        
        # Add common buttons
        base_buttons.extend([
            {"text": "Toggle Fullscreen", "action": "toggle_fullscreen"},
            {"text": "Help", "action": "help"}
        ])
        
        # Add Return to Game button if we're in a paused game (right before Exit Game)
        if self.settings_data.game_state == GameState.PAUSED:
            base_buttons.append({"text": "Return to Game", "action": "return_to_game"})
        
        # Add Exit Game button at the end
        base_buttons.append({"text": "Exit Game", "action": "exit"})
        
        self.buttons = base_buttons
        
        # Update fullscreen button text
        for button in self.buttons:
            if button["action"] == "toggle_fullscreen":
                button["text"] = "Exit Fullscreen" if self.is_fullscreen else "Enter Fullscreen"
                break
    
    def show_settings(self, game_state: GameState = GameState.MENU, current_save_slot: Optional[str] = None) -> None:
        """
        Show the settings interface.
        
        Args:
            game_state: Current game state
            current_save_slot: Currently active save slot
        """
        self.settings_data.is_visible = True
        self.settings_data.game_state = game_state
        self.settings_data.current_save_slot = current_save_slot
        self.selected_button = 0
        self.show_help = False
        
        # Update buttons based on game state
        self._update_buttons_for_game_state()
    
    def hide_settings(self) -> None:
        """Hide the settings interface."""
        self.settings_data.is_visible = False
        self.show_help = False
    
    def is_settings_visible(self) -> bool:
        """Check if settings interface is visible."""
        return self.settings_data.is_visible
    
    def handle_event(self, event: pygame.event.Event) -> bool:
        """
        Handle UI events.
        
        Args:
            event: Pygame event
            
        Returns:
            True if event was consumed
        """
        if not self.is_settings_visible():
            return False
        
        if self.show_help:
            return self._handle_help_event(event)
        else:
            return self._handle_main_menu_event(event)
    
    def _handle_main_menu_event(self, event: pygame.event.Event) -> bool:
        """Handle main menu events."""
        if event.type == pygame.KEYDOWN:
            if event.key == pygame.K_ESCAPE:
                if self.settings_data.game_state == GameState.PLAYING:
                    self.hide_settings()
                elif self.settings_data.game_state == GameState.MENU:
                    if self.on_exit_game:
                        self.on_exit_game()
                return True
            
            elif event.key == pygame.K_UP:
                self.selected_button = (self.selected_button - 1) % len(self.buttons)
                return True
            
            elif event.key == pygame.K_DOWN:
                self.selected_button = (self.selected_button + 1) % len(self.buttons)
                return True
            
            elif event.key in (pygame.K_RETURN, pygame.K_SPACE):
                self._execute_button_action(self.selected_button)
                return True
        
        elif event.type == pygame.MOUSEBUTTONDOWN:
            if event.button == 1:  # Left click
                mouse_pos = pygame.mouse.get_pos()
                return self._handle_click(mouse_pos)
        
        return False
    
    def _handle_help_event(self, event: pygame.event.Event) -> bool:
        """Handle help panel events."""
        if event.type == pygame.KEYDOWN:
            if event.key == pygame.K_ESCAPE:
                self.show_help = False
                return True
        elif event.type == pygame.MOUSEBUTTONDOWN:
            if event.button == 1:  # Left click
                self.show_help = False
                return True
        return True  # Consume all events when help is shown
    

    
    def _handle_click(self, mouse_pos: Tuple[int, int]) -> bool:
        """Handle mouse clicks on buttons."""
        screen_width = self.config.rendering.screen_width
        screen_height = self.config.rendering.screen_height
        
        # Calculate button positions
        menu_width = self.button_width + 2 * self.padding
        menu_height = len(self.buttons) * (self.button_height + self.button_spacing) + 2 * self.padding
        menu_x = (screen_width - menu_width) // 2
        menu_y = (screen_height - menu_height) // 2
        
        for i, button in enumerate(self.buttons):
            button_x = menu_x + self.padding
            button_y = menu_y + self.padding + i * (self.button_height + self.button_spacing)
            button_rect = pygame.Rect(button_x, button_y, self.button_width, self.button_height)
            
            if button_rect.collidepoint(mouse_pos):
                self._execute_button_action(i)
                return True
        
        return False
    
    def _execute_button_action(self, button_index: int) -> None:
        """Execute the action for a button."""
        if 0 <= button_index < len(self.buttons):
            action = self.buttons[button_index]["action"]
            
            if action == "new_game":
                if self.on_new_game:
                    self.on_new_game()
                self.hide_settings()
            
            elif action == "return_to_game":
                self.hide_settings()
            
            elif action == "load_game":
                if self.on_load_game:
                    self.on_load_game("load_request")
            
            elif action == "save_game":
                if self.settings_data.current_save_slot and self.on_save_game:
                    self.on_save_game(self.settings_data.current_save_slot)
                else:
                    # Default to slot 1 if no current slot
                    if self.on_save_game:
                        self.on_save_game("slot_1")
                self.hide_settings()
            
            elif action == "help":
                self.show_help = True
            
            elif action == "toggle_fullscreen":
                if self.on_toggle_fullscreen:
                    self.on_toggle_fullscreen()
            
            elif action == "exit":
                if self.on_exit_game:
                    self.on_exit_game()
    

    
    def render(self, surface: pygame.Surface) -> None:
        """
        Render the settings interface.
        
        Args:
            surface: Surface to render on
        """
        if not self.is_settings_visible():
            return
        
        if self.show_help:
            self._render_help_panel(surface)
        else:
            self._render_main_menu(surface)
    
    def _render_main_menu(self, surface: pygame.Surface) -> None:
        """Render the main settings menu."""
        screen_width = self.config.rendering.screen_width
        screen_height = self.config.rendering.screen_height
        
        # Draw semi-transparent overlay
        overlay = pygame.Surface((screen_width, screen_height))
        overlay.set_alpha(180)
        overlay.fill((0, 0, 0))
        surface.blit(overlay, (0, 0))
        
        # Calculate menu dimensions and position
        menu_width = self.button_width + 2 * self.padding
        menu_height = len(self.buttons) * (self.button_height + self.button_spacing) + 2 * self.padding + scale_value(60)
        menu_x = (screen_width - menu_width) // 2
        menu_y = (screen_height - menu_height) // 2
        
        # Draw menu background
        menu_rect = pygame.Rect(menu_x, menu_y, menu_width, menu_height)
        pygame.draw.rect(surface, self.colors['background_panel'], menu_rect)
        pygame.draw.rect(surface, self.colors['border'], menu_rect, scale_value(2))
        
        # Draw title
        title_text = "Treebeard's Revenge"
        title_surface = self.title_font.render(title_text, True, self.colors['text_primary'])
        title_rect = title_surface.get_rect(centerx=menu_x + menu_width // 2, y=menu_y + self.padding)
        surface.blit(title_surface, title_rect)
        
        # Draw buttons
        for i, button in enumerate(self.buttons):
            button_x = menu_x + self.padding
            button_y = menu_y + self.padding + scale_value(40) + i * (self.button_height + self.button_spacing)
            button_rect = pygame.Rect(button_x, button_y, self.button_width, self.button_height)
            
            # Determine button state
            is_selected = (i == self.selected_button)
            is_disabled = self._is_button_disabled(button["action"])
            
            # Draw button
            self._draw_menu_button(surface, button_rect, button["text"], is_selected, is_disabled)
        
        # Draw current save slot info if in game
        if self.settings_data.game_state == GameState.PLAYING and self.settings_data.current_save_slot:
            info_text = f"Current Save: {self.settings_data.current_save_slot}"
            info_surface = self.info_font.render(info_text, True, self.colors['text_secondary'])
            info_rect = info_surface.get_rect(centerx=menu_x + menu_width // 2,
                                            y=menu_y + menu_height - self.padding - scale_value(20))
            surface.blit(info_surface, info_rect)

    def _draw_menu_button(self, surface: pygame.Surface, rect: pygame.Rect, text: str,
                         is_selected: bool, is_disabled: bool) -> None:
        """Draw a menu button with proper styling."""
        # Determine colors based on state
        if is_disabled:
            bg_color = self.colors['button_disabled']
            text_color = self.colors['text_disabled']
        elif is_selected:
            bg_color = self.colors['button_primary_hover']
            text_color = self.colors['text_primary']
        else:
            bg_color = self.colors['button_primary']
            text_color = self.colors['text_primary']

        # Draw button background
        pygame.draw.rect(surface, bg_color, rect)
        pygame.draw.rect(surface, self.colors['border'], rect, scale_value(1))

        # Draw button text
        text_surface = self.button_font.render(text, True, text_color)
        text_rect = text_surface.get_rect(center=rect.center)
        surface.blit(text_surface, text_rect)

    def _is_button_disabled(self, action: str) -> bool:
        """Check if a button should be disabled."""
        if action == "save_game":
            return self.settings_data.game_state == GameState.MENU
        return False

    def _render_help_panel(self, surface: pygame.Surface) -> None:
        """Render the help panel."""
        screen_width = self.config.rendering.screen_width
        screen_height = self.config.rendering.screen_height

        # Draw semi-transparent overlay
        overlay = pygame.Surface((screen_width, screen_height))
        overlay.set_alpha(180)
        overlay.fill((0, 0, 0))
        surface.blit(overlay, (0, 0))

        # Help panel dimensions
        panel_width = scale_value(600)
        panel_height = scale_value(400)
        panel_x = (screen_width - panel_width) // 2
        panel_y = (screen_height - panel_height) // 2

        # Draw panel background
        panel_rect = pygame.Rect(panel_x, panel_y, panel_width, panel_height)
        pygame.draw.rect(surface, self.colors['background_panel'], panel_rect)
        pygame.draw.rect(surface, self.colors['border'], panel_rect, scale_value(2))

        # Draw title
        title_text = "Game Controls"
        title_surface = self.title_font.render(title_text, True, self.colors['text_primary'])
        title_rect = title_surface.get_rect(centerx=panel_x + panel_width // 2, y=panel_y + self.padding)
        surface.blit(title_surface, title_rect)

        # Help content
        help_items = [
            ("Movement:", "WASD keys"),
            ("Inventory:", "I key"),
            ("Interact:", "E key"),
            ("Quick Save:", "F5 key"),
            ("Quick Load:", "F9 key"),
            ("Pause/Settings:", "Escape key"),
            ("Fullscreen:", "F11 key"),
            ("UI Navigation:", "Mouse click, Escape to close")
        ]

        # Draw help items
        y_offset = panel_y + self.padding + scale_value(50)
        line_height = scale_value(25)

        for label, description in help_items:
            # Draw label
            label_surface = self.button_font.render(label, True, self.colors['text_primary'])
            surface.blit(label_surface, (panel_x + self.padding, y_offset))

            # Draw description
            desc_surface = self.button_font.render(description, True, self.colors['text_secondary'])
            surface.blit(desc_surface, (panel_x + self.padding + scale_value(150), y_offset))

            y_offset += line_height

        # Draw close instruction
        close_text = "Press Escape or click to close"
        close_surface = self.info_font.render(close_text, True, self.colors['text_secondary'])
        close_rect = close_surface.get_rect(centerx=panel_x + panel_width // 2,
                                          y=panel_y + panel_height - self.padding - scale_value(20))
        surface.blit(close_surface, close_rect)


