"""
Tooltip System

This module provides tooltip functionality for displaying detailed item information.
"""

import pygame
from typing import List, Optional, Tuple, Dict, Any
from dataclasses import dataclass

from src.application.interfaces import GameStateData
from src.game_core.config import get_config
from src.editor.ui_config import get_font, get_element_size, get_spacing, scale_value, FONT_LARGE, FONT_NORMAL, FONT_SMALL


@dataclass
class TooltipData:
    """Data for a tooltip."""
    title: str
    lines: List[str]
    position: Tuple[int, int]
    max_width: int = 300


class TooltipRenderer:
    """Renders tooltips for items and UI elements."""
    
    def __init__(self):
        """Initialize the tooltip renderer."""
        # Get configuration
        config = get_config().inventory_ui

        self.colors = {
            "background": (35, 40, 50, 240),      # Blue-grey, semi-transparent
            "border": (70, 80, 95),               # Blue-grey border
            "title": (120, 180, 255),             # Light blue for title
            "text": (240, 245, 250),              # Almost white for normal text
            "stat_positive": (90, 200, 120),      # Green for positive stats
            "stat_negative": (255, 100, 100),     # Red for negative stats
            "rarity_common": (180, 190, 200),     # Light grey for common items
            "rarity_uncommon": (90, 200, 120),    # Green for uncommon
            "rarity_rare": (100, 160, 255),       # Blue for rare
            "rarity_epic": (180, 120, 255),       # Purple for epic
            "rarity_legendary": (255, 200, 80),   # Gold for legendary
        }

        # Use centralized UI font configuration
        self.fonts = {
            "title": get_font(FONT_LARGE),
            "text": get_font(FONT_NORMAL),
            "small": get_font(FONT_SMALL),
        }

        # Use configurable spacing and padding with scaling
        self.padding = scale_value(config.tooltip_padding)
        self.line_spacing = scale_value(config.tooltip_line_spacing)
        self.max_width = scale_value(config.tooltip_max_width)
    
    def generate_item_tooltip(self, item_id: str, game_state: GameStateData) -> Optional[TooltipData]:
        """
        Generate tooltip data for an item.
        
        Args:
            item_id: ID of the item
            game_state: Current game state
            
        Returns:
            TooltipData or None if item not found
        """
        try:
            from src.game_data.items import ITEMS
            if item_id not in ITEMS:
                return None
            
            item_def = ITEMS[item_id]
            lines = []
            
            # Item name (title will be handled separately)
            title = item_def.name
            
            # Item type and value
            lines.append(f"Type: {item_def.item_type.value.title()}")
            if item_def.value > 0:
                lines.append(f"Value: {item_def.value} gold")
            
            # Description
            if item_def.description:
                lines.append("")  # Empty line for spacing
                lines.append(item_def.description)
            
            # Item properties and stats
            if item_def.properties:
                lines.append("")  # Empty line for spacing
                self._add_item_properties(lines, item_def.properties)
            
            # Equipment comparison (if applicable)
            if game_state.player and item_def.properties.get("slot"):
                self._add_equipment_comparison(lines, item_id, game_state)
            
            # Usage instructions
            self._add_usage_instructions(lines, item_def)
            
            return TooltipData(
                title=title,
                lines=lines,
                position=(0, 0),  # Will be set when rendering
                max_width=self.max_width
            )
            
        except ImportError:
            return None
    
    def _add_item_properties(self, lines: List[str], properties: Dict[str, Any]) -> None:
        """Add item properties to tooltip lines."""
        # Damage/defense bonuses
        if "damage_bonus" in properties:
            lines.append(f"+{properties['damage_bonus']} Damage")
        
        if "defense_bonus" in properties:
            lines.append(f"+{properties['defense_bonus']} Defense")
        
        # Healing/mana properties
        if "heal_amount" in properties:
            lines.append(f"Restores {properties['heal_amount']} HP")
        
        if "mana_amount" in properties:
            lines.append(f"Restores {properties['mana_amount']} MP")
        
        # Weapon properties
        if "weapon_type" in properties:
            lines.append(f"Weapon Type: {properties['weapon_type'].title()}")
        
        if "attack_speed" in properties:
            lines.append(f"Attack Speed: {properties['attack_speed']}")
        
        # Armor properties
        if "armor_type" in properties:
            lines.append(f"Armor Type: {properties['armor_type'].title()}")
        
        # Durability
        if "durability" in properties:
            lines.append(f"Durability: {properties['durability']}")
        
        # Equipment slot
        if "slot" in properties:
            slot_name = properties["slot"].replace("_", " ").title()
            lines.append(f"Slot: {slot_name}")
    
    def _add_equipment_comparison(self, lines: List[str], item_id: str, game_state: GameStateData) -> None:
        """Add equipment comparison to tooltip lines."""
        try:
            from src.game_data.items import ITEMS
            if item_id not in ITEMS:
                return
            
            item_def = ITEMS[item_id]
            slot = item_def.properties.get("slot")
            if not slot:
                return
            
            # Get currently equipped item in the same slot
            # Map item slot names to player equipment slot names
            slot_mapping = {
                "head": "head_equipment",
                "chest": "chest_equipment",
                "legs": "legs_equipment",
                "boots": "boots_equipment",
                "main_hand": "main_hand_weapon",
                "off_hand": "off_hand_equipment"
            }
            player_slot = slot_mapping.get(slot, slot)
            current_item_id = game_state.player.get_equipped_item_in_slot(player_slot)
            if not current_item_id or current_item_id not in ITEMS:
                return
            
            current_item = ITEMS[current_item_id]
            lines.append("")  # Empty line for spacing
            lines.append(f"Currently Equipped: {current_item.name}")
            
            # Compare stats
            new_damage = item_def.properties.get("damage_bonus", 0)
            current_damage = current_item.properties.get("damage_bonus", 0)
            if new_damage != current_damage:
                diff = new_damage - current_damage
                sign = "+" if diff > 0 else ""
                lines.append(f"Damage: {sign}{diff}")
            
            new_defense = item_def.properties.get("defense_bonus", 0)
            current_defense = current_item.properties.get("defense_bonus", 0)
            if new_defense != current_defense:
                diff = new_defense - current_defense
                sign = "+" if diff > 0 else ""
                lines.append(f"Defense: {sign}{diff}")
                
        except ImportError:
            pass
    
    def _add_usage_instructions(self, lines: List[str], item_def) -> None:
        """Add usage instructions to tooltip lines."""
        lines.append("")  # Empty line for spacing
        
        if item_def.item_type.value == "weapon" or item_def.item_type.value == "armor":
            lines.append("Left-click to equip")
            lines.append("Right-click for options")
        elif item_def.item_type.value == "consumable":
            lines.append("Right-click to use")
            lines.append("Left-click to move")
        else:
            lines.append("Left-click to interact")
    
    def render_tooltip(self, screen: pygame.Surface, tooltip_data: TooltipData, mouse_pos: Tuple[int, int]) -> None:
        """
        Render a tooltip on the screen.
        
        Args:
            screen: Surface to render to
            tooltip_data: Tooltip data to render
            mouse_pos: Current mouse position
        """
        if not tooltip_data.lines:
            return
        
        # Calculate tooltip size
        title_surface = self.fonts["title"].render(tooltip_data.title, True, self.colors["title"])
        title_width = title_surface.get_width()
        title_height = title_surface.get_height()
        
        max_line_width = title_width
        total_height = title_height + self.line_spacing
        
        line_surfaces = []
        for line in tooltip_data.lines:
            if line.strip():  # Non-empty line
                line_surface = self.fonts["text"].render(line, True, self.colors["text"])
                line_surfaces.append(line_surface)
                max_line_width = max(max_line_width, line_surface.get_width())
                total_height += line_surface.get_height() + self.line_spacing
            else:  # Empty line for spacing
                line_surfaces.append(None)
                total_height += self.fonts["text"].get_height() // 2
        
        # Calculate tooltip position (avoid going off screen)
        tooltip_width = min(max_line_width + self.padding * 2, tooltip_data.max_width)
        tooltip_height = total_height + self.padding * 2
        
        screen_width, screen_height = screen.get_size()
        
        # Position tooltip to the right and below mouse, but keep on screen
        tooltip_x = mouse_pos[0] + 15
        tooltip_y = mouse_pos[1] + 15
        
        if tooltip_x + tooltip_width > screen_width:
            tooltip_x = mouse_pos[0] - tooltip_width - 15
        
        if tooltip_y + tooltip_height > screen_height:
            tooltip_y = mouse_pos[1] - tooltip_height - 15
        
        # Ensure tooltip stays on screen
        tooltip_x = max(0, min(tooltip_x, screen_width - tooltip_width))
        tooltip_y = max(0, min(tooltip_y, screen_height - tooltip_height))
        
        # Create tooltip surface
        tooltip_surface = pygame.Surface((tooltip_width, tooltip_height), pygame.SRCALPHA)
        tooltip_surface.fill(self.colors["background"])
        pygame.draw.rect(tooltip_surface, self.colors["border"], 
                        pygame.Rect(0, 0, tooltip_width, tooltip_height), 2)
        
        # Render title
        tooltip_surface.blit(title_surface, (self.padding, self.padding))
        
        # Render lines
        y_offset = self.padding + title_height + self.line_spacing
        for line_surface in line_surfaces:
            if line_surface:
                tooltip_surface.blit(line_surface, (self.padding, y_offset))
                y_offset += line_surface.get_height() + self.line_spacing
            else:
                y_offset += self.fonts["text"].get_height() // 2
        
        # Blit tooltip to screen
        screen.blit(tooltip_surface, (tooltip_x, tooltip_y))
