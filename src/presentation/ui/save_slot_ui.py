"""
Save Slot UI Controller

This module manages the save slot selection UI component.
"""

import pygame
from typing import Optional, Dict, Any, Tuple, List, Callable
from dataclasses import dataclass

from src.application.interfaces import ISaveGameRepository
from src.game_core.config import get_config
from src.editor.ui_config import (
    get_font, get_element_size, get_spacing, scale_value, get_colors,
    FONT_LARGE, FONT_NORMAL, FONT_SMALL
)


@dataclass
class SaveSlotData:
    """Data for save slot interface."""
    is_visible: bool = False
    mode: str = "load"  # "load" or "save"
    selected_slot: Optional[int] = None
    current_save_slot: Optional[str] = None
    selected_slot_data: Optional[Dict[str, Any]] = None


class SaveSlotUIController:
    """Controller for the save slot selection interface."""
    
    def __init__(self, save_repository: ISaveGameRepository):
        """Initialize the save slot UI controller."""
        self.save_repository = save_repository
        self.config = get_config()
        
        # UI state
        self.slot_data = SaveSlotData()
        
        # UI configuration
        self.colors = get_colors()
        
        # Layout settings
        self.padding = scale_value(30)
        self.slot_spacing = scale_value(20)
        self.slot_width = scale_value(350)
        self.slot_height = scale_value(80)
        
        # Fonts
        self.title_font = get_font(FONT_LARGE)
        self.slot_font = get_font(FONT_NORMAL)
        self.info_font = get_font(FONT_SMALL)
        
        # Callbacks
        self.on_slot_selected: Optional[Callable[[str], None]] = None
        self.on_cancel: Optional[Callable] = None
    
    def show_save_slots(self, mode: str = "load", current_save_slot: Optional[str] = None) -> None:
        """
        Show the save slot selection interface.

        Args:
            mode: "load" or "save"
            current_save_slot: Currently active save slot
        """
        self.slot_data.is_visible = True
        self.slot_data.mode = mode
        self.slot_data.current_save_slot = current_save_slot
        self.slot_data.selected_slot = None
        self.slot_data.selected_slot_data = None
    
    def hide_save_slots(self) -> None:
        """Hide the save slot selection interface."""
        self.slot_data.is_visible = False
        self.slot_data.selected_slot = None
        self.slot_data.selected_slot_data = None
    
    def is_visible(self) -> bool:
        """Check if save slot interface is visible."""
        return self.slot_data.is_visible
    
    def handle_event(self, event: pygame.event.Event) -> bool:
        """
        Handle UI events.

        Args:
            event: Pygame event

        Returns:
            True if event was consumed
        """
        if not self.is_visible():
            return False

        if event.type == pygame.KEYDOWN:
            if event.key == pygame.K_ESCAPE:
                if self.on_cancel:
                    self.on_cancel()
                self.hide_save_slots()
                return True
            
            elif event.key == pygame.K_RETURN:
                # Load/Save selected slot
                if self.slot_data.selected_slot:
                    slot_name = f"slot_{self.slot_data.selected_slot}"
                    if self.slot_data.mode == "load":
                        # Check if slot has data before loading
                        if self.slot_data.selected_slot_data:
                            if self.on_slot_selected:
                                self.on_slot_selected(slot_name)
                            self.hide_save_slots()
                    else:  # save mode
                        if self.on_slot_selected:
                            self.on_slot_selected(slot_name)
                        self.hide_save_slots()
                return True

            elif event.key in (pygame.K_1, pygame.K_2, pygame.K_3, pygame.K_4, pygame.K_5,
                              pygame.K_6, pygame.K_7, pygame.K_8, pygame.K_9, pygame.K_0):
                # Handle number key selection
                slot_number = event.key - pygame.K_0
                if slot_number == 0:
                    slot_number = 10

                self._select_slot(slot_number)
                return True
        
        elif event.type == pygame.MOUSEBUTTONDOWN:
            if event.button == 1:  # Left click
                mouse_pos = pygame.mouse.get_pos()

                # Check if Load/Save button was clicked
                if self._is_action_button_clicked(mouse_pos):
                    if self.slot_data.selected_slot:
                        slot_name = f"slot_{self.slot_data.selected_slot}"
                        if self.slot_data.mode == "load":
                            # Check if slot has data before loading
                            if self.slot_data.selected_slot_data:
                                if self.on_slot_selected:
                                    self.on_slot_selected(slot_name)
                                self.hide_save_slots()
                        else:  # save mode
                            if self.on_slot_selected:
                                self.on_slot_selected(slot_name)
                            self.hide_save_slots()
                    return True

                # Check if slot was clicked
                slot_clicked = self._get_clicked_slot(mouse_pos)
                if slot_clicked:
                    self._select_slot(slot_clicked)
                    return True
        
        return True  # Consume all events when visible
    
    def _select_slot(self, slot_number: int) -> None:
        """Select a save slot."""
        if 1 <= slot_number <= 10:
            slot_name = f"slot_{slot_number}"

            # Just select the slot, don't load immediately
            self.slot_data.selected_slot = slot_number

            # Get slot data for display and validation
            slot_info = self.save_repository.get_save_info(slot_name)
            self.slot_data.selected_slot_data = slot_info
    
    def _get_clicked_slot(self, mouse_pos: Tuple[int, int]) -> Optional[int]:
        """Get the slot number that was clicked."""
        screen_width = self.config.rendering.screen_width
        screen_height = self.config.rendering.screen_height
        
        # Calculate panel position
        panel_width = scale_value(800)
        panel_height = scale_value(600)
        panel_x = (screen_width - panel_width) // 2
        panel_y = (screen_height - panel_height) // 2
        
        # Calculate slot grid position
        start_x = panel_x + (panel_width - 2 * self.slot_width - self.slot_spacing) // 2
        start_y = panel_y + self.padding + scale_value(60)
        
        for i in range(10):
            col = i % 2
            row = i // 2
            slot_x = start_x + col * (self.slot_width + self.slot_spacing)
            slot_y = start_y + row * (self.slot_height + self.slot_spacing)
            
            slot_rect = pygame.Rect(slot_x, slot_y, self.slot_width, self.slot_height)
            if slot_rect.collidepoint(mouse_pos):
                return i + 1
        
        return None

    def _is_action_button_clicked(self, mouse_pos: Tuple[int, int]) -> bool:
        """Check if the Load/Save button was clicked."""
        if not self.slot_data.selected_slot:
            return False

        screen_width = self.config.rendering.screen_width
        screen_height = self.config.rendering.screen_height

        # Calculate panel position
        panel_width = scale_value(800)
        panel_height = scale_value(600)
        panel_x = (screen_width - panel_width) // 2
        panel_y = (screen_height - panel_height) // 2

        # Action button position (bottom center)
        button_width = scale_value(150)
        button_height = get_element_size('button_height')
        button_x = panel_x + (panel_width - button_width) // 2
        button_y = panel_y + panel_height - scale_value(80)

        button_rect = pygame.Rect(button_x, button_y, button_width, button_height)
        return button_rect.collidepoint(mouse_pos)
    
    def render(self, surface: pygame.Surface) -> None:
        """
        Render the save slot selection interface.
        
        Args:
            surface: Surface to render on
        """
        if not self.is_visible():
            return
        
        screen_width = self.config.rendering.screen_width
        screen_height = self.config.rendering.screen_height
        
        # Draw semi-transparent overlay
        overlay = pygame.Surface((screen_width, screen_height))
        overlay.set_alpha(180)
        overlay.fill((0, 0, 0))
        surface.blit(overlay, (0, 0))
        
        # Panel dimensions
        panel_width = scale_value(800)
        panel_height = scale_value(600)
        panel_x = (screen_width - panel_width) // 2
        panel_y = (screen_height - panel_height) // 2
        
        # Draw panel background
        panel_rect = pygame.Rect(panel_x, panel_y, panel_width, panel_height)
        pygame.draw.rect(surface, self.colors['background_panel'], panel_rect)
        pygame.draw.rect(surface, self.colors['border'], panel_rect, scale_value(2))
        
        # Draw title
        title_text = "Load Game" if self.slot_data.mode == "load" else "Save Game"
        title_surface = self.title_font.render(title_text, True, self.colors['text_primary'])
        title_rect = title_surface.get_rect(centerx=panel_x + panel_width // 2, y=panel_y + self.padding)
        surface.blit(title_surface, title_rect)
        
        # Get save slot data
        save_slots = self.save_repository.list_save_slots()
        
        # Draw save slots (2x5 grid)
        start_x = panel_x + (panel_width - 2 * self.slot_width - self.slot_spacing) // 2
        start_y = panel_y + self.padding + scale_value(60)
        
        for i in range(10):
            slot_name = f"slot_{i + 1}"
            slot_data = save_slots.get(slot_name)
            
            # Calculate position
            col = i % 2
            row = i // 2
            slot_x = start_x + col * (self.slot_width + self.slot_spacing)
            slot_y = start_y + row * (self.slot_height + self.slot_spacing)
            
            # Check if this is the current save slot
            is_current = (self.slot_data.current_save_slot == slot_name)

            # Check if this slot is selected
            is_selected = (self.slot_data.selected_slot == i + 1)

            # Draw slot
            self._draw_save_slot(surface, slot_x, slot_y, self.slot_width, self.slot_height,
                               i + 1, slot_data, is_current, is_selected)
        
        # Draw Load/Save button if a slot is selected
        if self.slot_data.selected_slot:
            self._draw_action_button(surface, panel_x, panel_y, panel_width, panel_height)

        # Draw instructions
        if self.slot_data.mode == "load":
            if self.slot_data.selected_slot:
                instruction_text = f"Selected Slot {self.slot_data.selected_slot} • Click Load Game or press Enter"
            else:
                instruction_text = "Click on a save slot to select, or press number keys (1-0)"
        else:
            if self.slot_data.selected_slot:
                instruction_text = f"Selected Slot {self.slot_data.selected_slot} • Click Save Game or press Enter"
            else:
                instruction_text = "Click on a save slot to select, or press number keys (1-0)"

        instruction_surface = self.info_font.render(instruction_text, True, self.colors['text_secondary'])
        instruction_rect = instruction_surface.get_rect(centerx=panel_x + panel_width // 2,
                                                      y=panel_y + panel_height - self.padding - scale_value(40))
        surface.blit(instruction_surface, instruction_rect)

        # Draw close instruction
        close_text = "Press Escape to cancel"
        close_surface = self.info_font.render(close_text, True, self.colors['text_secondary'])
        close_rect = close_surface.get_rect(centerx=panel_x + panel_width // 2,
                                          y=panel_y + panel_height - self.padding - scale_value(20))
        surface.blit(close_surface, close_rect)
    
    def _draw_save_slot(self, surface: pygame.Surface, x: int, y: int, width: int, height: int,
                       slot_number: int, slot_data: Optional[Dict[str, Any]], is_current: bool, is_selected: bool = False) -> None:
        """Draw a single save slot."""
        slot_rect = pygame.Rect(x, y, width, height)
        
        # Determine colors based on state
        if is_selected:
            bg_color = self.colors['button_primary_hover']
            border_color = self.colors['border_accent']
            border_width = scale_value(3)
        elif is_current:
            bg_color = self.colors['button_primary']
            border_color = self.colors['border_accent']
            border_width = scale_value(2)
        elif slot_data:
            bg_color = self.colors['button_secondary']
            border_color = self.colors['border']
            border_width = scale_value(1)
        else:
            bg_color = self.colors['button_disabled']
            border_color = self.colors['border']
            border_width = scale_value(1)
        
        # Draw slot background
        pygame.draw.rect(surface, bg_color, slot_rect)
        pygame.draw.rect(surface, border_color, slot_rect, border_width)
        
        # Draw slot number
        number_text = f"Slot {slot_number}"
        if is_selected:
            number_text += " (Selected)"
        elif is_current:
            number_text += " (Current)"

        number_surface = self.slot_font.render(number_text, True, self.colors['text_primary'])
        surface.blit(number_surface, (x + scale_value(10), y + scale_value(5)))
        
        if slot_data:
            # Draw save info
            level_name = slot_data.get('level_name', 'Unknown Level')
            timestamp = slot_data.get('timestamp', '')
            player_level = slot_data.get('player_level', 1)
            player_name = slot_data.get('player_name', 'Unknown')
            
            # Format timestamp
            if timestamp:
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    time_str = dt.strftime('%Y-%m-%d %H:%M')
                except:
                    time_str = timestamp[:16]  # Fallback
            else:
                time_str = "Unknown"
            
            # Draw level name
            level_surface = self.info_font.render(level_name, True, self.colors['text_secondary'])
            surface.blit(level_surface, (x + scale_value(10), y + scale_value(25)))
            
            # Draw player info and timestamp
            info_text = f"{player_name} (Level {player_level}) - {time_str}"
            info_surface = self.info_font.render(info_text, True, self.colors['text_secondary'])
            surface.blit(info_surface, (x + scale_value(10), y + scale_value(45)))
        else:
            # Draw "Empty" text
            empty_surface = self.info_font.render("Empty", True, self.colors['text_disabled'])
            empty_rect = empty_surface.get_rect(center=(x + width // 2, y + height // 2))
            surface.blit(empty_surface, empty_rect)

    def _draw_action_button(self, surface: pygame.Surface, panel_x: int, panel_y: int,
                           panel_width: int, panel_height: int) -> None:
        """Draw the Load/Save action button."""
        # Button dimensions
        button_width = scale_value(150)
        button_height = get_element_size('button_height')
        button_x = panel_x + (panel_width - button_width) // 2
        button_y = panel_y + panel_height - scale_value(80)

        button_rect = pygame.Rect(button_x, button_y, button_width, button_height)

        # Determine if button should be enabled
        can_action = False
        button_text = ""

        if self.slot_data.mode == "load":
            button_text = "Load Game"
            can_action = (self.slot_data.selected_slot_data is not None)
        else:  # save mode
            button_text = "Save Game"
            can_action = True  # Can always save to any slot

        # Determine button colors
        if can_action:
            bg_color = self.colors['button_success']
            text_color = self.colors['text_primary']
        else:
            bg_color = self.colors['button_disabled']
            text_color = self.colors['text_disabled']

        # Draw button
        pygame.draw.rect(surface, bg_color, button_rect)
        pygame.draw.rect(surface, self.colors['border'], button_rect, scale_value(1))

        # Draw button text
        text_surface = self.slot_font.render(button_text, True, text_color)
        text_rect = text_surface.get_rect(center=button_rect.center)
        surface.blit(text_surface, text_rect)
