"""
New Game UI Controller

This module manages the new game creation interface with slot selection and game naming.
"""

import pygame
from typing import Optional, Dict, Any, Tuple, List, Callable
from dataclasses import dataclass

from src.application.interfaces import ISaveGameRepository
from src.game_core.config import get_config
from src.editor.ui_config import (
    get_font, get_element_size, get_spacing, scale_value, get_colors,
    FONT_LARGE, FONT_NORMAL, FONT_SMALL
)


@dataclass
class NewGameData:
    """Data for new game interface."""
    is_visible: bool = False
    selected_slot: Optional[int] = None
    game_name: str = ""
    input_active: bool = False


class NewGameUIController:
    """Controller for the new game creation interface."""
    
    def __init__(self, save_repository: ISaveGameRepository):
        """Initialize the new game UI controller."""
        self.save_repository = save_repository
        self.config = get_config()
        
        # UI state
        self.new_game_data = NewGameData()
        
        # UI configuration
        self.colors = get_colors()
        
        # Layout settings
        self.padding = scale_value(30)
        self.slot_spacing = scale_value(20)
        self.slot_width = scale_value(350)
        self.slot_height = scale_value(80)
        self.input_height = get_element_size('input_height')
        self.button_height = get_element_size('button_height')
        
        # Fonts
        self.title_font = get_font(FONT_LARGE)
        self.slot_font = get_font(FONT_NORMAL)
        self.info_font = get_font(FONT_SMALL)
        self.input_font = get_font(FONT_NORMAL)
        
        # Callbacks
        self.on_new_game_created: Optional[Callable[[str, str], None]] = None  # (slot_name, game_name)
        self.on_cancel: Optional[Callable] = None
    
    def show_new_game(self) -> None:
        """Show the new game creation interface."""
        self.new_game_data.is_visible = True
        self.new_game_data.selected_slot = None
        self.new_game_data.game_name = ""
        self.new_game_data.input_active = False
    
    def hide_new_game(self) -> None:
        """Hide the new game creation interface."""
        self.new_game_data.is_visible = False
        self.new_game_data.selected_slot = None
        self.new_game_data.game_name = ""
        self.new_game_data.input_active = False
    
    def is_visible(self) -> bool:
        """Check if new game interface is visible."""
        return self.new_game_data.is_visible
    
    def handle_event(self, event: pygame.event.Event) -> bool:
        """
        Handle UI events.
        
        Args:
            event: Pygame event
            
        Returns:
            True if event was consumed
        """
        if not self.is_visible():
            return False
        
        if event.type == pygame.KEYDOWN:
            if event.key == pygame.K_ESCAPE:
                if self.on_cancel:
                    self.on_cancel()
                self.hide_new_game()
                return True
            
            elif event.key == pygame.K_RETURN:
                # Create new game if slot selected and name entered
                if self.new_game_data.selected_slot and self.new_game_data.game_name.strip():
                    slot_name = f"slot_{self.new_game_data.selected_slot}"
                    if self.on_new_game_created:
                        self.on_new_game_created(slot_name, self.new_game_data.game_name.strip())
                    self.hide_new_game()
                return True
            
            elif event.key == pygame.K_TAB:
                # Toggle input focus
                self.new_game_data.input_active = not self.new_game_data.input_active
                return True
            
            elif event.key in (pygame.K_1, pygame.K_2, pygame.K_3, pygame.K_4, pygame.K_5,
                              pygame.K_6, pygame.K_7, pygame.K_8, pygame.K_9, pygame.K_0):
                # Handle number key selection
                slot_number = event.key - pygame.K_0
                if slot_number == 0:
                    slot_number = 10
                
                self.new_game_data.selected_slot = slot_number
                return True
            
            elif self.new_game_data.input_active:
                # Handle text input for game name
                if event.key == pygame.K_BACKSPACE:
                    self.new_game_data.game_name = self.new_game_data.game_name[:-1]
                elif len(self.new_game_data.game_name) < 30:  # Limit name length
                    # Only allow printable characters
                    if event.unicode.isprintable() and event.unicode not in ['\t', '\n', '\r']:
                        self.new_game_data.game_name += event.unicode
                return True
        
        elif event.type == pygame.MOUSEBUTTONDOWN:
            if event.button == 1:  # Left click
                mouse_pos = pygame.mouse.get_pos()
                
                # Check if clicked on a slot
                slot_clicked = self._get_clicked_slot(mouse_pos)
                if slot_clicked:
                    self.new_game_data.selected_slot = slot_clicked
                    return True
                
                # Check if clicked on input field
                if self._is_input_clicked(mouse_pos):
                    self.new_game_data.input_active = True
                    return True
                else:
                    self.new_game_data.input_active = False
                
                # Check if clicked on create button
                if self._is_create_button_clicked(mouse_pos):
                    if self.new_game_data.selected_slot and self.new_game_data.game_name.strip():
                        slot_name = f"slot_{self.new_game_data.selected_slot}"
                        if self.on_new_game_created:
                            self.on_new_game_created(slot_name, self.new_game_data.game_name.strip())
                        self.hide_new_game()
                    return True
        
        return True  # Consume all events when visible
    
    def _get_clicked_slot(self, mouse_pos: Tuple[int, int]) -> Optional[int]:
        """Get the slot number that was clicked."""
        screen_width = self.config.rendering.screen_width
        screen_height = self.config.rendering.screen_height
        
        # Calculate panel position
        panel_width = scale_value(800)
        panel_height = scale_value(700)
        panel_x = (screen_width - panel_width) // 2
        panel_y = (screen_height - panel_height) // 2
        
        # Calculate slot grid position
        start_x = panel_x + (panel_width - 2 * self.slot_width - self.slot_spacing) // 2
        start_y = panel_y + self.padding + scale_value(60)
        
        for i in range(10):
            col = i % 2
            row = i // 2
            slot_x = start_x + col * (self.slot_width + self.slot_spacing)
            slot_y = start_y + row * (self.slot_height + self.slot_spacing)
            
            slot_rect = pygame.Rect(slot_x, slot_y, self.slot_width, self.slot_height)
            if slot_rect.collidepoint(mouse_pos):
                return i + 1
        
        return None
    
    def _is_input_clicked(self, mouse_pos: Tuple[int, int]) -> bool:
        """Check if the input field was clicked."""
        screen_width = self.config.rendering.screen_width
        screen_height = self.config.rendering.screen_height
        
        panel_width = scale_value(800)
        panel_height = scale_value(700)
        panel_x = (screen_width - panel_width) // 2
        panel_y = (screen_height - panel_height) // 2
        
        # Input field position
        input_width = scale_value(400)
        input_x = panel_x + (panel_width - input_width) // 2
        input_y = panel_y + panel_height - scale_value(120)
        
        input_rect = pygame.Rect(input_x, input_y, input_width, self.input_height)
        return input_rect.collidepoint(mouse_pos)
    
    def _is_create_button_clicked(self, mouse_pos: Tuple[int, int]) -> bool:
        """Check if the create button was clicked."""
        screen_width = self.config.rendering.screen_width
        screen_height = self.config.rendering.screen_height
        
        panel_width = scale_value(800)
        panel_height = scale_value(700)
        panel_x = (screen_width - panel_width) // 2
        panel_y = (screen_height - panel_height) // 2
        
        # Create button position
        button_width = scale_value(150)
        button_x = panel_x + (panel_width - button_width) // 2
        button_y = panel_y + panel_height - scale_value(60)
        
        button_rect = pygame.Rect(button_x, button_y, button_width, self.button_height)
        return button_rect.collidepoint(mouse_pos)
    
    def render(self, surface: pygame.Surface) -> None:
        """
        Render the new game creation interface.
        
        Args:
            surface: Surface to render on
        """
        if not self.is_visible():
            return
        
        screen_width = self.config.rendering.screen_width
        screen_height = self.config.rendering.screen_height
        
        # Draw semi-transparent overlay
        overlay = pygame.Surface((screen_width, screen_height))
        overlay.set_alpha(180)
        overlay.fill((0, 0, 0))
        surface.blit(overlay, (0, 0))
        
        # Panel dimensions
        panel_width = scale_value(800)
        panel_height = scale_value(700)
        panel_x = (screen_width - panel_width) // 2
        panel_y = (screen_height - panel_height) // 2
        
        # Draw panel background
        panel_rect = pygame.Rect(panel_x, panel_y, panel_width, panel_height)
        pygame.draw.rect(surface, self.colors['background_panel'], panel_rect)
        pygame.draw.rect(surface, self.colors['border'], panel_rect, scale_value(2))
        
        # Draw title
        title_text = "Create New Game"
        title_surface = self.title_font.render(title_text, True, self.colors['text_primary'])
        title_rect = title_surface.get_rect(centerx=panel_x + panel_width // 2, y=panel_y + self.padding)
        surface.blit(title_surface, title_rect)
        
        # Draw instruction
        instruction_text = "Select a save slot and enter a name for your game"
        instruction_surface = self.info_font.render(instruction_text, True, self.colors['text_secondary'])
        instruction_rect = instruction_surface.get_rect(centerx=panel_x + panel_width // 2, 
                                                      y=panel_y + self.padding + scale_value(35))
        surface.blit(instruction_surface, instruction_rect)
        
        # Get save slot data
        save_slots = self.save_repository.list_save_slots()
        
        # Draw save slots (2x5 grid)
        start_x = panel_x + (panel_width - 2 * self.slot_width - self.slot_spacing) // 2
        start_y = panel_y + self.padding + scale_value(60)
        
        for i in range(10):
            slot_name = f"slot_{i + 1}"
            slot_data = save_slots.get(slot_name)
            
            # Calculate position
            col = i % 2
            row = i // 2
            slot_x = start_x + col * (self.slot_width + self.slot_spacing)
            slot_y = start_y + row * (self.slot_height + self.slot_spacing)
            
            # Check if this slot is selected
            is_selected = (self.new_game_data.selected_slot == i + 1)
            
            # Draw slot
            self._draw_save_slot(surface, slot_x, slot_y, self.slot_width, self.slot_height, 
                               i + 1, slot_data, is_selected)
        
        # Draw game name input
        self._draw_name_input(surface, panel_x, panel_y, panel_width, panel_height)
        
        # Draw create button
        self._draw_create_button(surface, panel_x, panel_y, panel_width, panel_height)
        
        # Draw instructions
        controls_text = "Click slot or press number keys (1-0) • Tab to focus name input • Enter to create"
        controls_surface = self.info_font.render(controls_text, True, self.colors['text_secondary'])
        controls_rect = controls_surface.get_rect(centerx=panel_x + panel_width // 2,
                                                y=panel_y + panel_height - self.padding - scale_value(20))
        surface.blit(controls_surface, controls_rect)

    def _draw_save_slot(self, surface: pygame.Surface, x: int, y: int, width: int, height: int,
                       slot_number: int, slot_data: Optional[Dict[str, Any]], is_selected: bool) -> None:
        """Draw a single save slot."""
        slot_rect = pygame.Rect(x, y, width, height)

        # Determine colors based on state
        if is_selected:
            bg_color = self.colors['button_primary_hover']
            border_color = self.colors['border_accent']
            border_width = scale_value(3)
        elif slot_data:
            # Slot has existing data - show as occupied
            bg_color = self.colors['button_danger']  # Red to indicate overwrite
            border_color = self.colors['border']
            border_width = scale_value(1)
        else:
            # Empty slot
            bg_color = self.colors['button_secondary']
            border_color = self.colors['border']
            border_width = scale_value(1)

        # Draw slot background
        pygame.draw.rect(surface, bg_color, slot_rect)
        pygame.draw.rect(surface, border_color, slot_rect, border_width)

        # Draw slot number
        number_text = f"Slot {slot_number}"
        number_surface = self.slot_font.render(number_text, True, self.colors['text_primary'])
        surface.blit(number_surface, (x + scale_value(10), y + scale_value(5)))

        if slot_data:
            # Draw existing save info
            level_name = slot_data.get('level_name', 'Unknown Level')
            timestamp = slot_data.get('timestamp', '')
            player_name = slot_data.get('player_name', 'Unknown')

            # Format timestamp
            if timestamp:
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    time_str = dt.strftime('%Y-%m-%d %H:%M')
                except:
                    time_str = timestamp[:16]  # Fallback
            else:
                time_str = "Unknown"

            # Draw existing save warning
            warning_surface = self.info_font.render("Will overwrite:", True, self.colors['text_primary'])
            surface.blit(warning_surface, (x + scale_value(10), y + scale_value(25)))

            # Draw existing save info
            info_text = f"{player_name} - {time_str}"
            info_surface = self.info_font.render(info_text, True, self.colors['text_secondary'])
            surface.blit(info_surface, (x + scale_value(10), y + scale_value(45)))
        else:
            # Draw "Empty" text
            empty_surface = self.info_font.render("Empty", True, self.colors['text_secondary'])
            empty_rect = empty_surface.get_rect(center=(x + width // 2, y + height // 2))
            surface.blit(empty_surface, empty_rect)

    def _draw_name_input(self, surface: pygame.Surface, panel_x: int, panel_y: int,
                        panel_width: int, panel_height: int) -> None:
        """Draw the game name input field."""
        # Input field dimensions
        input_width = scale_value(400)
        input_x = panel_x + (panel_width - input_width) // 2
        input_y = panel_y + panel_height - scale_value(120)

        # Draw label
        label_text = "Game Name:"
        label_surface = self.slot_font.render(label_text, True, self.colors['text_primary'])
        surface.blit(label_surface, (input_x, input_y - scale_value(25)))

        # Draw input field
        input_rect = pygame.Rect(input_x, input_y, input_width, self.input_height)

        # Determine input field colors
        if self.new_game_data.input_active:
            bg_color = self.colors['input_background']
            border_color = self.colors['input_focus']
            border_width = scale_value(2)
        else:
            bg_color = self.colors['input_background']
            border_color = self.colors['input_border']
            border_width = scale_value(1)

        pygame.draw.rect(surface, bg_color, input_rect)
        pygame.draw.rect(surface, border_color, input_rect, border_width)

        # Draw text
        display_text = self.new_game_data.game_name
        if self.new_game_data.input_active:
            # Add cursor
            display_text += "|"

        if display_text:
            text_surface = self.input_font.render(display_text, True, self.colors['input_text'])
            text_rect = text_surface.get_rect(left=input_x + scale_value(10),
                                            centery=input_y + self.input_height // 2)
            surface.blit(text_surface, text_rect)
        else:
            # Show placeholder
            placeholder_text = "Enter your character name..."
            placeholder_surface = self.input_font.render(placeholder_text, True, self.colors['text_disabled'])
            placeholder_rect = placeholder_surface.get_rect(left=input_x + scale_value(10),
                                                          centery=input_y + self.input_height // 2)
            surface.blit(placeholder_surface, placeholder_rect)

    def _draw_create_button(self, surface: pygame.Surface, panel_x: int, panel_y: int,
                           panel_width: int, panel_height: int) -> None:
        """Draw the create game button."""
        # Button dimensions
        button_width = scale_value(150)
        button_x = panel_x + (panel_width - button_width) // 2
        button_y = panel_y + panel_height - scale_value(60)

        button_rect = pygame.Rect(button_x, button_y, button_width, self.button_height)

        # Determine if button should be enabled
        can_create = (self.new_game_data.selected_slot is not None and
                     self.new_game_data.game_name.strip())

        # Determine button colors
        if can_create:
            bg_color = self.colors['button_success']
            text_color = self.colors['text_primary']
        else:
            bg_color = self.colors['button_disabled']
            text_color = self.colors['text_disabled']

        # Draw button
        pygame.draw.rect(surface, bg_color, button_rect)
        pygame.draw.rect(surface, self.colors['border'], button_rect, scale_value(1))

        # Draw button text
        button_text = "Create Game"
        text_surface = self.slot_font.render(button_text, True, text_color)
        text_rect = text_surface.get_rect(center=button_rect.center)
        surface.blit(text_surface, text_rect)
