"""
Town of Caledon Level Configuration

This module contains level-specific configuration and metadata.
"""

from typing import Dict, Any

LEVEL_CONFIG: Dict[str, Any] = {
    "id": "town_caledon",
    "name": "Town of Caledon", 
    "description": "A peaceful trading town on the edge of the wilderness",
    
    # Audio settings
    "background_music": "town_theme",
    "ambient_sounds": ["birds", "wind"],
    
    # Visual settings
    "weather": "sunny",
    "time_of_day": "afternoon",
    "fog_of_war": False,
    
    # Gameplay settings
    "allow_save": True,
    "respawn_monsters": False,
    "difficulty_modifier": 1.0,
    
    # Connected levels
                                                                                    "exits": {
        "1": {
            "target_map": "RemiMap",
            "spawn_point": "2",
            "tile_asset": "exit_portal"
        },
        "3": {
            "target_map": "CamrynMap",
            "spawn_point": "1",
            "tile_asset": "exit_portal"
        },
        "8": {
            "target_map": "RemiMap",
            "spawn_point": "0",
            "tile_asset": "exit_portal"
        },
        "9": {
            "target_map": "fence_gate_test",
            "spawn_point": "0",
            "tile_asset": "magic_door"
        }
    }
}
