"""
Quest Definitions for Town of Caledon

This module contains quest definitions specific to the Town of Caledon level.
These are pure data definitions with no game logic - all game-specific behavior
is handled by the level's event handlers.
"""

from typing import Dict, Any, List


def get_caledon_quests() -> List[Dict[str, Any]]:
    """
    Get all quest definitions for the Town of Caledon.
    
    Returns:
        List of quest definition dictionaries
    """
    return [
        {
            "id": "goblin_loose_in_caledon",
            "name": "Goblin Problem",
            "description": "Deal with the goblin threat in the town",
            "objectives": ["defeat_goblin"],
            "metadata": {
                "level": "town_caledon",
                "reward_gold": 10,
                "reward_experience": 25,
                "npc_giver": "guard_at_3_7",
                "goblin_spawn_position": {"x": 15, "y": 13},
                "difficulty": "easy"
            }
        }
    ]


# Individual quest definitions for easy access
GOBLIN_LOOSE_IN_CALEDON = {
    "id": "goblin_loose_in_caledon",
    "name": "Goblin Problem", 
    "description": "Deal with the goblin threat in the town",
    "objectives": ["defeat_goblin"],
    "metadata": {
        "level": "town_caledon",
        "reward_gold": 10,
        "reward_experience": 25,
        "npc_giver": "guard_at_3_7",
        "goblin_spawn_position": {"x": 15, "y": 13},
        "difficulty": "easy"
    }
}
