"""
Test Town2 Level Configuration

This module contains level-specific configuration and metadata.
"""

from typing import Dict, Any

LEVEL_CONFIG: Dict[str, Any] = {
    "id": "test_town2",
    "name": "Test Town2",
    "description": "A level created with the map editor",

    # Audio settings
    "background_music": "default_theme",
    "ambient_sounds": [],

    # Visual settings
    "weather": "sunny",
    "time_of_day": "day",
    "fog_of_war": False,

    # Gameplay settings
    "allow_save": True,
    "respawn_monsters": False,
    "difficulty_modifier": 1.0,

    # Connected levels
        "exits": {
        "0": {
            "target_map": "town_caledon",
            "spawn_point": "8"
        }
    }
}

# NPC overrides for this level
npc_overrides = {
    # Override armourer inventory to sell iron armor instead of leather
    "armourer_inventory": {
        "iron_helmet": 5,
        "iron_breastplate": 5,
        "iron_greaves": 5,
        "iron_boots": 5,
        "iron_shield": 5
    },

    # Custom dialog for the armourer in this town
    "armourer_dialog": [
        "Welcome to the finest armory in the realm!",
        "I specialize in iron armor - the best protection money can buy.",
        "Every piece is forged with care and tested in battle."
    ]
}
