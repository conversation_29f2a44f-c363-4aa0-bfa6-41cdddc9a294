"""
RemiMap Level Configuration

This module contains level-specific configuration and metadata.
"""

from typing import Dict, Any

LEVEL_CONFIG: Dict[str, Any] = {
    "id": "RemiMap",
    "name": "<PERSON><PERSON>'s Domain", 
    "description": "A mysterious realm connected to the town",
    
    # Audio settings
    "background_music": "mysterious_theme",
    "ambient_sounds": ["wind", "mystical"],
    
    # Visual settings
    "weather": "sunny",
    "time_of_day": "day",
    "fog_of_war": False,
    
    # Gameplay settings
    "allow_save": True,
    "respawn_monsters": False,
    "difficulty_modifier": 1.2,
    
    # Connected levels - exit back to town_caledon
            "exits": {
        "2": {
            "target_map": "town_caledon",
            "spawn_point": "1"
        },
        "3": {
            "target_map": "CamrynMap",
            "spawn_point": "1"
        }
    }
}
