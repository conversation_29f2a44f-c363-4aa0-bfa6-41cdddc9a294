"""
Camrynmap Level Configuration

This module contains level-specific configuration and metadata.
"""

from typing import Dict, Any

LEVEL_CONFIG: Dict[str, Any] = {
    "id": "<PERSON>rynMap",
    "name": "<PERSON>rynmap",
    "description": "A level created with the map editor",

    # Audio settings
    "background_music": "default_theme",
    "ambient_sounds": [],

    # Visual settings
    "weather": "sunny",
    "time_of_day": "day",
    "fog_of_war": False,

    # Gameplay settings
    "allow_save": True,
    "respawn_monsters": False,
    "difficulty_modifier": 1.0,

    # Connected levels
            "exits": {
        "2": {
            "target_map": "town_caledon",
            "spawn_point": "1",
            "tile_asset": "exit_portal"
        },
        "3": {
            "target_map": "remis  maze",
            "spawn_point": "0",
            "tile_asset": "exit_portal"
        },
        "7": {
            "target_map": "remi-map-island",
            "spawn_point": "0",
            "tile_asset": "magic_door"
        }
    }
}
