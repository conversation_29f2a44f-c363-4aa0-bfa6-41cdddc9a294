"""
Remi-Map-Island Level Configuration

This module contains level-specific configuration and metadata.
"""

from typing import Dict, Any

LEVEL_CONFIG: Dict[str, Any] = {
    "id": "remi-map-island",
    "name": "Remi-Map-Island",
    "description": "A level created with the map editor",

    # Audio settings
    "background_music": "default_theme",
    "ambient_sounds": [],

    # Visual settings
    "weather": "sunny",
    "time_of_day": "day",
    "fog_of_war": False,

    # Gameplay settings
    "allow_save": True,
    "respawn_monsters": False,
    "difficulty_modifier": 1.0,

    # Connected levels
                "exits": {
        "0": {
            "target_map": "CamrynMap",
            "spawn_point": "7",
            "tile_asset": "magic_door"
        }
    }
}
