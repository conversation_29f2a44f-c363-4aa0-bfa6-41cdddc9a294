"""
Town of West Haven Quest Definitions

This module contains quest definitions for the Town of West Haven.
"""

from typing import Dict, Any, List


def get_west_haven_quests() -> List[Dict[str, Any]]:
    """
    Get all quest definitions for the Town of West Haven.
    
    Returns:
        List of quest definition dictionaries
    """
    return [
        {
            "id": "goblin_threat_west_haven",
            "name": "Goblin Threat",
            "description": "The Mayor has reported a goblin spotted in town. Deal with this threat.",
            "objectives": ["defeat_goblin"],
            "metadata": {
                "level": "town_west_haven",
                "reward_gold": 15,
                "reward_experience": 30,
                "npc_giver": "mayor_west_haven",
                "goblin_spawn_position": {"x": 20, "y": 15},
                "difficulty": "easy"
            }
        }
    ]


# Individual quest definitions for easy access
GOBLIN_THREAT_WEST_HAVEN = {
    "id": "goblin_threat_west_haven",
    "name": "Goblin Threat", 
    "description": "The Mayor has reported a goblin spotted in town. Deal with this threat.",
    "objectives": ["defeat_goblin"],
    "metadata": {
        "level": "town_west_haven",
        "reward_gold": 15,
        "reward_experience": 30,
        "npc_giver": "mayor_west_haven",
        "goblin_spawn_position": {"x": 20, "y": 15},
        "difficulty": "easy"
    }
}
