"""
Town of West Haven Level Configuration

This module contains level-specific configuration and metadata.
"""

from typing import Dict, Any

LEVEL_CONFIG: Dict[str, Any] = {
    "id": "town_west_haven",
    "name": "Town of West Haven", 
    "description": "A peaceful starter town with friendly merchants and helpful citizens",
    
    # Audio settings
    "background_music": "town_theme",
    "ambient_sounds": ["birds", "wind"],
    
    # Visual settings
    "weather": "sunny",
    "time_of_day": "morning",
    "fog_of_war": False,
    
    # Gameplay settings
    "allow_save": True,
    "respawn_monsters": False,
    "difficulty_modifier": 1.0,
    
    # Connected levels
    "exits": {
        # Will be added when connecting to other areas
    }
}
