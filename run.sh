#!/bin/bash

# <PERSON><PERSON><PERSON>'s Revenge - Development Runner Script

set -e

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
cd "$SCRIPT_DIR"

# Function to ensure virtual environment exists and is up to date
ensure_venv() {
    echo "Checking virtual environment..."
    
    # Check if uv is installed
    if ! command -v uv &> /dev/null; then
        echo "Installing uv..."
        curl -LsSf https://astral.sh/uv/install.sh | sh
        source $HOME/.cargo/env
    fi
    
    # Check if virtual environment exists
    if [ ! -d ".venv" ]; then
        echo "Creating virtual environment..."
        uv venv
    fi
    
    # Check if dependencies are installed/up to date
    if [ ! -f ".venv/pyvenv.cfg" ] || [ "pyproject.toml" -nt ".venv/pyvenv.cfg" ] || [ "uv.lock" -nt ".venv/pyvenv.cfg" ]; then
        echo "Installing/updating dependencies..."
        uv sync
    fi
    
    # Activate virtual environment
    source .venv/bin/activate
}

case "${1:-help}" in
    setup)
        echo "Setting up development environment..."
        ensure_venv
        echo "Setup complete! Run './run.sh test' to verify installation."
        ;;
    
    test)
        echo "Running test suite..."
        ensure_venv
        uv run pytest
        ;;
    
    test-unit)
        echo "Running unit tests (core and application layers only)..."
        ensure_venv
        uv run pytest tests/unit/
        ;;
    
    test-integration)
        echo "Running integration tests..."
        ensure_venv
        uv run pytest tests/integration/
        ;;
    
    typecheck)
        echo "Running type checks..."
        ensure_venv
        uv run mypy src/
        ;;
    
    run)
        echo "Starting the game..."
        ensure_venv
        cd src && uv run python main.py
        ;;

    editor)
        echo "Starting map editor..."
        ensure_venv
        cd src && uv run python -m editor.main
        ;;

    clean)
        echo "Cleaning up..."
        rm -rf .venv/
        rm -rf .pytest_cache/
        rm -rf htmlcov/
        rm -rf src/**/__pycache__/
        rm -rf tests/**/__pycache__/
        ;;
    
    help|*)
        echo "Treebeard's Revenge - Development Commands"
        echo ""
        echo "Usage: ./run.sh <command>"
        echo ""
        echo "Commands:"
        echo "  setup          Create virtual environment and install dependencies"
        echo "  test           Run all tests with coverage"
        echo "  test-unit      Run only unit tests (core + application layers)"
        echo "  test-integration Run only integration tests"
        echo "  typecheck      Run mypy type checking"
        echo "  run            Start the game"
        echo "  editor         Start the map editor"
        echo "  clean          Remove generated files and virtual environment"
        echo "  help           Show this help message"
        ;;
esac
