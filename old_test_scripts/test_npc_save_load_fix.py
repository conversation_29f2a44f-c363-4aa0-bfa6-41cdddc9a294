#!/usr/bin/env python3
"""
Test script to verify that NPCs are properly preserved during save/load operations.

This script tests the fix for the issue where NPCs were being re-initialized
when loading a saved game, losing their state changes.
"""

import sys
import os
import tempfile
import shutil
from pathlib import Path

# Add the src directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.game_core.config import initialize_config
from src.infrastructure.repositories.json_save_game_repository import JsonSaveGameRepository
from src.application.interfaces import GameStateData
from src.game_core.entities import Player, NPC, Position, Stats


def test_npc_state_preservation():
    """Test that NPC state is preserved during save/load operations."""
    print("Testing NPC state preservation during save/load...")
    
    # Initialize config
    initialize_config('game_config.yaml')
    
    # Create temporary directory for save files
    temp_dir = tempfile.mkdtemp()
    save_repository = JsonSaveGameRepository()
    save_repository.save_directory = Path(temp_dir)
    
    try:
        # Create test player
        player = Player(
            id="test_player",
            name="Test Hero",
            position=Position(100, 100),
            asset_id="player.hero",
            stats=Stats(hp=100, max_hp=100, mp=50, max_mp=50, strength=10, defense=5, speed=8),
            size=(32, 32),
            level=3,
            experience=500,
            inventory={"gold": 100, "bread": 2}
        )
        
        # Create test NPC with initial state
        merchant = NPC(
            id="merchant_town_square",
            name="Friendly Merchant",
            position=Position(300, 200),
            asset_id="npc.merchant.general",
            npc_type="merchant",
            behavior="store",
            dialog=["Welcome to my shop!", "I have the finest goods!"],
            inventory={"health_potion": 10, "bread": 15, "sword": 2},
            properties={"shop_level": 1, "reputation": 50}
        )
        
        # Create initial game state
        initial_game_state = GameStateData(
            player=player,
            monsters={},
            items={},
            npcs={"merchant_town_square": merchant},
            current_level_id="town_square",
            collision_map=[],
            level_tiles=[]
        )
        
        print("✓ Created initial game state with NPC")
        print(f"  - NPC: {merchant.name}")
        print(f"  - Initial inventory: {merchant.inventory}")
        print(f"  - Initial properties: {merchant.properties}")
        
        # Save the initial game
        save_repository.save_game(initial_game_state, "npc_test")
        print("✓ Saved initial game state")
        
        # Simulate NPC state changes (e.g., player bought items)
        # Merchant loses some items and gains gold
        modified_merchant = merchant.remove_item_from_stock("health_potion", 3)
        modified_merchant = modified_merchant.remove_item_from_stock("bread", 5)
        modified_merchant = modified_merchant.add_item_to_stock("gold", 25)
        
        # Update properties (e.g., reputation increased)
        modified_merchant.properties["reputation"] = 75
        modified_merchant.properties["total_sales"] = 8
        
        # Create modified game state
        modified_game_state = GameStateData(
            player=player,
            monsters={},
            items={},
            npcs={"merchant_town_square": modified_merchant},
            current_level_id="town_square",
            collision_map=[],
            level_tiles=[]
        )
        
        print("✓ Modified NPC state")
        print(f"  - Modified inventory: {modified_merchant.inventory}")
        print(f"  - Modified properties: {modified_merchant.properties}")
        
        # Save the modified game
        save_repository.save_game(modified_game_state, "npc_test_modified")
        print("✓ Saved modified game state")
        
        # Load the modified game
        loaded_game_state = save_repository.load_game("npc_test_modified")
        print("✓ Loaded modified game state")
        
        # Verify NPC state is preserved
        assert loaded_game_state is not None, "Failed to load game state"
        assert len(loaded_game_state.npcs) == 1, f"Expected 1 NPC, got {len(loaded_game_state.npcs)}"
        assert "merchant_town_square" in loaded_game_state.npcs, "Merchant NPC not found"
        
        loaded_merchant = loaded_game_state.npcs["merchant_town_square"]
        
        # Check basic properties
        assert loaded_merchant.name == "Friendly Merchant", f"Name mismatch: {loaded_merchant.name}"
        assert loaded_merchant.npc_type == "merchant", f"Type mismatch: {loaded_merchant.npc_type}"
        assert loaded_merchant.behavior == "store", f"Behavior mismatch: {loaded_merchant.behavior}"
        
        # Check position
        assert loaded_merchant.position.x == 300, f"Position X mismatch: {loaded_merchant.position.x}"
        assert loaded_merchant.position.y == 200, f"Position Y mismatch: {loaded_merchant.position.y}"
        
        # Check inventory changes are preserved
        assert loaded_merchant.inventory["health_potion"] == 7, f"Health potion count: {loaded_merchant.inventory['health_potion']}"
        assert loaded_merchant.inventory["bread"] == 10, f"Bread count: {loaded_merchant.inventory['bread']}"
        assert loaded_merchant.inventory["sword"] == 2, f"Sword count: {loaded_merchant.inventory['sword']}"
        assert loaded_merchant.inventory["gold"] == 25, f"Gold count: {loaded_merchant.inventory['gold']}"
        
        # Check properties are preserved
        assert loaded_merchant.properties["reputation"] == 75, f"Reputation: {loaded_merchant.properties['reputation']}"
        assert loaded_merchant.properties["total_sales"] == 8, f"Total sales: {loaded_merchant.properties['total_sales']}"
        assert loaded_merchant.properties["shop_level"] == 1, f"Shop level: {loaded_merchant.properties['shop_level']}"
        
        # Check dialog is preserved
        assert len(loaded_merchant.dialog) == 2, f"Dialog count: {len(loaded_merchant.dialog)}"
        assert "Welcome to my shop!" in loaded_merchant.dialog, "Dialog missing"
        
        print("✓ All NPC state verification passed!")
        print(f"  - Final inventory: {loaded_merchant.inventory}")
        print(f"  - Final properties: {loaded_merchant.properties}")
        
        # Test multiple NPCs
        print("\nTesting multiple NPCs...")
        
        # Add a second NPC
        guard = NPC(
            id="guard_gate",
            name="Town Guard",
            position=Position(500, 100),
            asset_id="npc.guard.town",
            npc_type="guard",
            behavior="dialog",
            dialog=["Keep the peace, citizen.", "Nothing to report."],
            inventory={},
            properties={"patrol_route": "gate", "shift": "day"}
        )
        
        # Create game state with multiple NPCs
        multi_npc_state = GameStateData(
            player=player,
            monsters={},
            items={},
            npcs={
                "merchant_town_square": loaded_merchant,
                "guard_gate": guard
            },
            current_level_id="town_square",
            collision_map=[],
            level_tiles=[]
        )
        
        # Save and load multiple NPCs
        save_repository.save_game(multi_npc_state, "multi_npc_test")
        loaded_multi_state = save_repository.load_game("multi_npc_test")
        
        assert len(loaded_multi_state.npcs) == 2, f"Expected 2 NPCs, got {len(loaded_multi_state.npcs)}"
        assert "merchant_town_square" in loaded_multi_state.npcs, "Merchant missing"
        assert "guard_gate" in loaded_multi_state.npcs, "Guard missing"
        
        loaded_guard = loaded_multi_state.npcs["guard_gate"]
        assert loaded_guard.name == "Town Guard", f"Guard name: {loaded_guard.name}"
        assert loaded_guard.properties["patrol_route"] == "gate", f"Patrol route: {loaded_guard.properties['patrol_route']}"
        
        print("✓ Multiple NPCs test passed!")
        
        return True
        
    finally:
        # Clean up temporary directory
        shutil.rmtree(temp_dir)


def main():
    """Run the NPC save/load test."""
    print("=== NPC Save/Load Fix Test ===\n")
    
    try:
        success = test_npc_state_preservation()
        
        if success:
            print("\n=== All Tests Passed! ===")
            print("✅ NPCs are now properly preserved during save/load operations")
            print("✅ NPC inventory changes are maintained")
            print("✅ NPC properties and dialog are preserved")
            print("✅ Multiple NPCs are handled correctly")
            print("\nThe fix successfully resolves the issue where NPCs were being re-initialized when loading games.")
            return 0
        else:
            print("\n❌ Tests failed!")
            return 1
            
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
