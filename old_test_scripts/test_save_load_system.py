#!/usr/bin/env python3
"""
Test script for the save/load system implementation.

This script tests the basic functionality of the save/load system including:
- Settings UI startup
- Save slot management
- Context-aware escape key handling
- Help panel display
"""

import sys
import os
import pygame
from pathlib import Path

# Add the src directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.game_core.config import get_config
from src.presentation.ui import (
    SettingsUIController, SaveSlotUIController, HelpUIController, 
    GameState, SettingsData, SaveSlotData, HelpData
)
from src.infrastructure.repositories.json_save_game_repository import JsonSaveGameRepository


def test_save_repository():
    """Test the save repository functionality."""
    print("Testing save repository...")
    
    # Create a temporary save repository
    save_repo = JsonSaveGameRepository()
    
    # Test listing save slots
    slots = save_repo.list_save_slots()
    print(f"Found {len(slots)} save slots")
    
    for slot_name, slot_data in slots.items():
        if slot_data:
            print(f"  {slot_name}: {slot_data.get('level_name', 'Unknown')} - {slot_data.get('timestamp', 'No timestamp')}")
        else:
            print(f"  {slot_name}: Empty")
    
    # Test slot name generation
    for i in range(1, 11):
        slot_name = save_repo.get_numbered_slot_name(i)
        print(f"Slot {i} -> {slot_name}")
    
    print("Save repository test completed.\n")


def test_ui_controllers():
    """Test the UI controllers initialization."""
    print("Testing UI controllers...")
    
    # Initialize pygame (required for UI controllers)
    pygame.init()
    pygame.display.set_mode((800, 600))
    
    # Create save repository
    save_repo = JsonSaveGameRepository()
    
    # Test SettingsUIController
    settings_ui = SettingsUIController(save_repo)
    print(f"Settings UI created: {settings_ui is not None}")
    print(f"Settings UI visible: {settings_ui.is_settings_visible()}")
    
    # Test showing settings
    settings_ui.show_settings(GameState.MENU)
    print(f"Settings UI visible after show: {settings_ui.is_settings_visible()}")
    
    # Test SaveSlotUIController
    save_slot_ui = SaveSlotUIController(save_repo)
    print(f"Save slot UI created: {save_slot_ui is not None}")
    print(f"Save slot UI visible: {save_slot_ui.is_visible()}")
    
    # Test showing save slots
    save_slot_ui.show_save_slots("load")
    print(f"Save slot UI visible after show: {save_slot_ui.is_visible()}")
    
    # Test HelpUIController
    help_ui = HelpUIController()
    print(f"Help UI created: {help_ui is not None}")
    print(f"Help UI visible: {help_ui.is_visible()}")
    
    # Test showing help
    help_ui.show_help()
    print(f"Help UI visible after show: {help_ui.is_visible()}")
    
    pygame.quit()
    print("UI controllers test completed.\n")


def test_config():
    """Test the configuration system."""
    print("Testing configuration...")
    
    config = get_config()
    print(f"Config loaded: {config is not None}")
    print(f"Settings UI config: {config.settings_ui is not None}")
    print(f"Show on startup: {config.settings_ui.show_on_startup}")
    print(f"Default save slot: {config.settings_ui.default_save_slot}")
    print(f"UI scaling factor: {config.ui_scaling.scale_factor}")
    
    print("Configuration test completed.\n")


def test_event_handling():
    """Test basic event handling."""
    print("Testing event handling...")
    
    # Initialize pygame
    pygame.init()
    screen = pygame.display.set_mode((800, 600))
    clock = pygame.time.Clock()
    
    # Create UI controllers
    save_repo = JsonSaveGameRepository()
    settings_ui = SettingsUIController(save_repo)
    help_ui = HelpUIController()
    
    # Show settings UI
    settings_ui.show_settings(GameState.MENU)
    
    print("Event handling test started. Press ESC to test escape key, H to show help, Q to quit.")
    
    running = True
    test_duration = 0
    max_test_duration = 20  # 20 seconds max test
    
    while running and test_duration < max_test_duration:
        dt = clock.tick(60) / 1000.0
        test_duration += dt
        
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_q:
                    print("Q pressed - quitting test")
                    running = False
                elif event.key == pygame.K_h:
                    print("H pressed - showing help")
                    help_ui.show_help()
                elif event.key == pygame.K_ESCAPE:
                    print("ESC pressed - testing escape handling")
                    if help_ui.is_visible():
                        print("  Closing help UI")
                        help_ui.hide_help()
                    elif settings_ui.is_settings_visible():
                        print("  Closing settings UI")
                        settings_ui.hide_settings()
                    else:
                        print("  No UI to close")
                
                # Let UI controllers handle events
                settings_ui.handle_event(event)
                help_ui.handle_event(event)
        
        # Clear screen
        screen.fill((50, 50, 50))
        
        # Render UI controllers
        settings_ui.render(screen)
        help_ui.render(screen)
        
        # Update display
        pygame.display.flip()
    
    pygame.quit()
    print("Event handling test completed.\n")


def main():
    """Run all tests."""
    print("=== Save/Load System Test ===\n")
    
    try:
        test_config()
        test_save_repository()
        test_ui_controllers()
        
        # Interactive test (optional)
        response = input("Run interactive event handling test? (y/n): ").lower().strip()
        if response == 'y':
            test_event_handling()
        
        print("=== All Tests Completed Successfully ===")
        
    except Exception as e:
        print(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
