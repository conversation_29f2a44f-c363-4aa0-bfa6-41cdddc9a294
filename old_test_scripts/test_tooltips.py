#!/usr/bin/env python3
"""
Test script to verify asset tooltips work correctly.
"""

import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.editor.ui.asset_panel import AssetTooltipRenderer
import yaml

def test_tooltip_generation():
    """Test tooltip generation for various asset types."""
    
    # Load base legend
    base_legend_path = Path(__file__).parent / "src" / "game_data" / "base_legend.yaml"
    with open(base_legend_path, 'r') as f:
        base_legend = yaml.safe_load(f)
    
    # Initialize tooltip renderer
    renderer = AssetTooltipRenderer()
    
    # Test different asset types
    test_assets = [
        '#',  # Wall tile
        '.',  # Floor tile
        '@',  # Player entity
        'g',  # Monster entity
        '$',  # Item entity
        'C',  # Chest entity
        '0',  # Exit tile
    ]
    
    print("Testing tooltip generation for various assets:")
    print("=" * 50)
    
    for symbol in test_assets:
        if symbol in base_legend:
            definition = base_legend[symbol]
            tooltip_data = renderer.generate_asset_tooltip(symbol, definition)
            
            print(f"\nAsset: {symbol}")
            print(f"Title: {tooltip_data.title}")
            print("Lines:")
            for i, line in enumerate(tooltip_data.lines):
                if line.strip():
                    print(f"  {i+1}: {line}")
                else:
                    print(f"  {i+1}: [empty line]")
        else:
            print(f"\nAsset {symbol} not found in base legend")
    
    print("\n" + "=" * 50)
    print("Tooltip generation test completed successfully!")

if __name__ == "__main__":
    test_tooltip_generation()
