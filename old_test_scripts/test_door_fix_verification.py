#!/usr/bin/env python3
"""Test to verify the door interaction fix works for both opening and closing doors."""

from unittest.mock import Mock
from src.application.use_cases import TileInteractionUseCase
from src.game_core.entities import GameStateData, Player, Position, Stats
from src.infrastructure.map_parser import <PERSON><PERSON><PERSON><PERSON>


def test_door_interaction_fix():
    """Test that doors can be both opened and closed."""
    # Create mock event bus
    event_bus = Mock()
    
    # Create map parser with base legend
    map_parser = MapParser()
    
    # Create tile interaction use case
    tile_interaction = TileInteractionUseCase(event_bus, map_parser)
    
    # Create initial game state with a closed door
    level_tiles = [
        ["tile.floor.grass", "tile.floor.grass", "tile.floor.grass"],
        ["tile.floor.grass", "tile.door.wooden", "tile.floor.grass"],
        ["tile.floor.grass", "tile.floor.grass", "tile.floor.grass"]
    ]
    
    collision_map = [
        [False, False, False],
        [False, True, False],   # Door is initially solid
        [False, False, False]
    ]
    
    player = Player(
        id="player1",
        name="Hero", 
        position=Position(128, 128),
        asset_id="player.hero",
        stats=Stats(hp=100, max_hp=100, mp=50, max_mp=50,
                   strength=10, defense=5, speed=8),
        size=(32, 32),
        level=1,
        experience=0
    )
    
    game_state = GameStateData(
        player=player,
        monsters={},
        items={},
        npcs={},
        current_level_id="test_level",
        collision_map=collision_map,
        level_tiles=level_tiles,
        tile_states={}
    )
    
    # Test 1: Open the closed door
    print("Test 1: Opening closed door...")
    result_state = tile_interaction.execute(game_state, 1, 1)  # Door at position (1,1)
    
    # Verify door is now open
    assert result_state.level_tiles[1][1] == "tile.door.wooden.open", f"Expected open door, got {result_state.level_tiles[1][1]}"
    assert result_state.collision_map[1][1] == False, "Open door should not be solid"
    assert "1,1" in result_state.tile_states, "Door should have state entry"
    assert result_state.tile_states["1,1"]["is_open"] == True, "Door state should be open"
    print("✓ Door opened successfully")
    
    # Test 2: Close the open door
    print("Test 2: Closing open door...")
    result_state2 = tile_interaction.execute(result_state, 1, 1)  # Same door position
    
    # Verify door is now closed
    assert result_state2.level_tiles[1][1] == "tile.door.wooden", f"Expected closed door, got {result_state2.level_tiles[1][1]}"
    assert result_state2.collision_map[1][1] == True, "Closed door should be solid"
    assert result_state2.tile_states["1,1"]["is_open"] == False, "Door state should be closed"
    print("✓ Door closed successfully")
    
    print("All tests passed! Door interaction fix is working correctly.")


if __name__ == "__main__":
    test_door_interaction_fix()
