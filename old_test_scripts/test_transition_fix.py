#!/usr/bin/env python3
"""
Test script to verify transition number change fix.
"""

import sys
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent))

def test_transition_dialog_fix():
    """Test that the transition dialog correctly uses the updated transition number."""
    
    # Simulate a dialog configuration
    transition_config = {
        'exit_number': '2',  # User changed to 2
        'target_map': 'RemiMap',
        'spawn_point': '1'
    }
    
    # Simulate the old context
    context = {
        'transition_number': '0',  # Originally was 0
        'level_id': 'town_caledon',
        'position': (10, 5)
    }
    
    old_transition_number = context['transition_number']
    new_transition_number = transition_config['exit_number']
    
    print(f"Testing transition number change:")
    print(f"  Old number: {old_transition_number}")
    print(f"  New number: {new_transition_number}")
    print(f"  Number changed: {old_transition_number != new_transition_number}")
    
    # Test level config update logic
    existing_transitions = {
        '1': {
            'target_map': 'RemiMap',
            'spawn_point': '2'
        }
    }
    
    # Remove old transition config if number changed
    if old_transition_number != new_transition_number and old_transition_number in existing_transitions:
        print(f"  Would remove old config for transition {old_transition_number}")
        del existing_transitions[old_transition_number]
    
    # Update with new configuration using the new transition number
    existing_transitions[new_transition_number] = {
        'target_map': transition_config['target_map'],
        'spawn_point': transition_config['spawn_point']
    }
    
    print(f"  Updated transitions: {existing_transitions}")
    
    # Check the result
    assert new_transition_number in existing_transitions
    assert existing_transitions[new_transition_number]['target_map'] == transition_config['target_map']
    assert existing_transitions[new_transition_number]['spawn_point'] == transition_config['spawn_point']
    
    if old_transition_number != new_transition_number:
        assert old_transition_number not in existing_transitions or old_transition_number == new_transition_number
    
    print("  ✓ Test passed!")

if __name__ == "__main__":
    test_transition_dialog_fix()
    print("All tests passed!")
