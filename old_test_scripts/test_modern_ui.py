#!/usr/bin/env python3
"""
Test script to showcase the modern UI improvements.

This script demonstrates the new UI design system with modern colors,
animations, and visual effects.
"""

import pygame
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.editor.ui_config import (
    get_ui_config, get_colors, get_ui_animator,
    draw_modern_button, draw_modern_panel, draw_progress_bar,
    render_text_with_shadow, draw_pulse_effect
)


class ModernUIDemo:
    """Demo application showcasing the modern UI system."""
    
    def __init__(self):
        """Initialize the demo."""
        pygame.init()
        
        # Set up display
        self.screen = pygame.display.set_mode((1200, 800))
        pygame.display.set_caption("Treebeard's Revenge - Modern UI Demo")
        
        # Get UI configuration
        self.ui_config = get_ui_config()
        self.colors = self.ui_config.get_standard_colors()
        self.animator = get_ui_animator()
        
        # Fonts
        from src.editor.ui_config import get_font, FONT_LARGE, FONT_NORMAL, FONT_SMALL
        self.font_large = get_font(FONT_LARGE)
        self.font_normal = get_font(FONT_NORMAL)
        self.font_small = get_font(FONT_SMALL)
        
        # Demo state
        self.running = True
        self.clock = pygame.time.Clock()
        self.mouse_pos = (0, 0)
        
        # Demo elements
        self.buttons = [
            {"rect": pygame.Rect(50, 100, 150, 40), "text": "Primary", "type": "primary"},
            {"rect": pygame.Rect(220, 100, 150, 40), "text": "Secondary", "type": "secondary"},
            {"rect": pygame.Rect(390, 100, 150, 40), "text": "Success", "type": "success"},
            {"rect": pygame.Rect(560, 100, 150, 40), "text": "Danger", "type": "danger"},
        ]
        
        self.progress_bars = [
            {"rect": pygame.Rect(50, 200, 200, 25), "progress": 0.75, "color": self.colors["health"], "label": "Health"},
            {"rect": pygame.Rect(50, 240, 200, 25), "progress": 0.45, "color": self.colors["mana"], "label": "Mana"},
            {"rect": pygame.Rect(50, 280, 200, 25), "progress": 0.90, "color": self.colors["experience"], "label": "Experience"},
        ]
        
        self.panels = [
            {"rect": pygame.Rect(400, 200, 300, 200), "title": "Character Stats"},
            {"rect": pygame.Rect(750, 200, 300, 200), "title": "Inventory"},
        ]
        
    def handle_events(self):
        """Handle pygame events."""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
            elif event.type == pygame.MOUSEMOTION:
                self.mouse_pos = event.pos
                
    def update(self, dt):
        """Update demo state."""
        pass
        
    def render(self):
        """Render the demo."""
        # Clear screen with modern background
        self.screen.fill(self.colors["background_primary"])
        
        # Title
        title_text = "Modern UI Design System Demo"
        render_text_with_shadow(
            self.screen, self.font_large, title_text, 
            (50, 30), self.colors["text_primary"],
            shadow_color=(0, 0, 0, 150), shadow_offset=(2, 2)
        )
        
        # Subtitle
        subtitle_text = "Showcasing the new visual design for Treebeard's Revenge"
        render_text_with_shadow(
            self.screen, self.font_normal, subtitle_text,
            (50, 70), self.colors["text_secondary"],
            shadow_color=(0, 0, 0, 100), shadow_offset=(1, 1)
        )
        
        # Demo buttons
        self.render_buttons()
        
        # Demo progress bars
        self.render_progress_bars()
        
        # Demo panels
        self.render_panels()
        
        # Color palette showcase
        self.render_color_palette()
        
        pygame.display.flip()
        
    def render_buttons(self):
        """Render demo buttons."""
        for button in self.buttons:
            is_hovered = button["rect"].collidepoint(self.mouse_pos)
            
            # Choose colors based on button type
            if button["type"] == "primary":
                color = self.colors["button_primary"]
                hover_color = self.colors["button_primary_hover"]
            elif button["type"] == "success":
                color = self.colors["button_success"]
                hover_color = self.colors["button_success_hover"]
            elif button["type"] == "danger":
                color = self.colors["button_danger"]
                hover_color = self.colors["button_danger_hover"]
            else:  # secondary
                color = self.colors["button_secondary"]
                hover_color = self.colors["button_secondary_hover"]
            
            # Draw modern button
            draw_modern_button(
                self.screen, button["rect"], color, hover_color, color,
                is_hovered=is_hovered, is_active=False
            )
            
            # Button text
            text_surface = self.font_normal.render(button["text"], True, self.colors["text_primary"])
            text_rect = text_surface.get_rect(center=button["rect"].center)
            self.screen.blit(text_surface, text_rect)
            
    def render_progress_bars(self):
        """Render demo progress bars."""
        for bar in self.progress_bars:
            # Label
            label_text = self.font_small.render(bar["label"], True, self.colors["text_secondary"])
            label_pos = (bar["rect"].x, bar["rect"].y - 20)
            self.screen.blit(label_text, label_pos)
            
            # Progress bar
            draw_progress_bar(
                self.screen, bar["rect"], bar["progress"], bar["color"],
                show_text=True, font=self.font_small
            )
            
    def render_panels(self):
        """Render demo panels."""
        for panel in self.panels:
            # Draw modern panel
            draw_modern_panel(
                self.screen, panel["rect"],
                background_color=self.colors["background_panel"],
                border_color=self.colors["border_primary"],
                shadow=True
            )
            
            # Panel title
            title_surface = self.font_normal.render(panel["title"], True, self.colors["text_accent"])
            title_pos = (panel["rect"].x + 15, panel["rect"].y + 15)
            self.screen.blit(title_surface, title_pos)
            
            # Sample content
            content_y = panel["rect"].y + 50
            sample_stats = ["Strength: 15", "Dexterity: 12", "Intelligence: 18", "Health: 85/100"]
            
            for i, stat in enumerate(sample_stats):
                stat_surface = self.font_small.render(stat, True, self.colors["text_primary"])
                stat_pos = (panel["rect"].x + 15, content_y + i * 25)
                self.screen.blit(stat_surface, stat_pos)
                
    def render_color_palette(self):
        """Render color palette showcase."""
        palette_title = self.font_normal.render("Color Palette", True, self.colors["text_primary"])
        self.screen.blit(palette_title, (50, 450))
        
        # Show key colors
        color_samples = [
            ("Primary BG", self.colors["background_primary"]),
            ("Secondary BG", self.colors["background_secondary"]),
            ("Panel BG", self.colors["background_panel"]),
            ("Primary Border", self.colors["border_primary"]),
            ("Accent Border", self.colors["border_accent"]),
            ("Primary Text", self.colors["text_primary"]),
            ("Accent Text", self.colors["text_accent"]),
            ("Success", self.colors["success"]),
            ("Warning", self.colors["warning"]),
            ("Error", self.colors["error"]),
        ]
        
        for i, (name, color) in enumerate(color_samples):
            x = 50 + (i % 5) * 120
            y = 480 + (i // 5) * 60
            
            # Color swatch
            swatch_rect = pygame.Rect(x, y, 40, 30)
            pygame.draw.rect(self.screen, color[:3], swatch_rect)
            pygame.draw.rect(self.screen, self.colors["border_primary"], swatch_rect, 1)
            
            # Color name
            name_surface = self.font_small.render(name, True, self.colors["text_secondary"])
            name_pos = (x + 45, y + 8)
            self.screen.blit(name_surface, name_pos)
        
    def run(self):
        """Run the demo."""
        while self.running:
            dt = self.clock.tick(60) / 1000.0
            
            self.handle_events()
            self.update(dt)
            self.render()


if __name__ == "__main__":
    demo = ModernUIDemo()
    demo.run()
    pygame.quit()
