#!/usr/bin/env python3
"""
Test script to verify that NPCs load correctly across all three scenarios:
1. New game loading
2. Map transition loading  
3. Save game loading

This test addresses the issue where NPCs weren't loading when loading saved games.
"""

import sys
import tempfile
import shutil
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_npc_loading_scenarios():
    """Test NPC loading across new game, map transition, and save game scenarios."""
    
    # Create temporary directory for test saves
    temp_dir = tempfile.mkdtemp()
    print(f"Using temporary directory: {temp_dir}")
    
    try:
        # Import required modules
        from src.infrastructure.repositories.json_save_game_repository import JsonSaveGameRepository
        from src.infrastructure.repositories.level_repository import LevelRepository
        from src.infrastructure.repositories.map_parser import MapParser
        from src.application.use_cases import BuildLevelUseCase
        from src.game_core.entities import Player, Position, Stats
        from src.application.interfaces import GameStateData
        
        # Create a mock event bus for testing
        class MockEventBus:
            def publish(self, event):
                pass

        # Set up repositories
        save_repository = JsonSaveGameRepository(temp_dir)
        level_repository = LevelRepository("src/levels", "src/game_data/base_legend.yaml")
        map_parser = MapParser("src/game_data/base_legend.yaml")
        build_level_use_case = BuildLevelUseCase(MockEventBus())
        
        print("✓ Repositories initialized")
        
        # Test 1: New Game Loading (baseline)
        print("\n=== Test 1: New Game Loading ===")
        
        # Load town_caledon level (should have NPCs)
        layout_data = level_repository.load_level("town_caledon")
        fresh_game_state = build_level_use_case.execute(layout_data, "town_caledon")
        
        print(f"✓ Fresh game state loaded")
        print(f"  - NPCs found: {len(fresh_game_state.npcs)}")
        for npc_id, npc in fresh_game_state.npcs.items():
            print(f"    - {npc_id}: {npc.name} ({npc.npc_type})")
        
        # Verify we have NPCs
        assert len(fresh_game_state.npcs) > 0, "No NPCs found in fresh game state"
        
        # Test 2: Save Game and Load Game
        print("\n=== Test 2: Save and Load Game ===")
        
        # Save the game
        save_repository.save_game(fresh_game_state, "npc_test")
        print("✓ Game saved")
        
        # Load the game using the old method (direct deserialization)
        loaded_state_old = save_repository.load_game("npc_test")
        print(f"✓ Game loaded using old method")
        print(f"  - NPCs found: {len(loaded_state_old.npcs)}")
        
        # Verify NPCs are preserved in save/load
        assert len(loaded_state_old.npcs) == len(fresh_game_state.npcs), \
            f"NPC count mismatch: saved {len(fresh_game_state.npcs)}, loaded {len(loaded_state_old.npcs)}"
        
        # Test 3: Simulate the unified loading approach
        print("\n=== Test 3: Unified Loading Approach ===")
        
        # This simulates what the new _load_level_data method does
        def simulate_unified_loading(level_id: str, existing_game_state=None):
            """Simulate the unified loading approach."""
            # Load fresh level data
            layout_data = level_repository.load_level(level_id)
            fresh_state = build_level_use_case.execute(layout_data, level_id)
            
            if existing_game_state:
                # Merge saved state with fresh level data
                merged_npcs = {}
                
                # Start with fresh NPCs from level
                for npc_id, fresh_npc in fresh_state.npcs.items():
                    merged_npcs[npc_id] = fresh_npc
                
                # Update with saved NPC states
                for npc_id, saved_npc in existing_game_state.npcs.items():
                    if npc_id in merged_npcs:
                        # Merge NPC states
                        fresh_npc = merged_npcs[npc_id]
                        from src.game_core.entities import NPC
                        merged_npcs[npc_id] = NPC(
                            id=fresh_npc.id,
                            name=fresh_npc.name,
                            position=saved_npc.position,
                            asset_id=fresh_npc.asset_id,
                            npc_type=fresh_npc.npc_type,
                            behavior=fresh_npc.behavior,
                            dialog=fresh_npc.dialog,
                            inventory=saved_npc.inventory,
                            properties=saved_npc.properties
                        )
                
                # Create merged game state
                return GameStateData(
                    player=existing_game_state.player,
                    monsters=existing_game_state.monsters,
                    items=existing_game_state.items,
                    npcs=merged_npcs,
                    current_level_id=level_id,
                    collision_map=fresh_state.collision_map,
                    level_tiles=fresh_state.level_tiles
                )
            else:
                return fresh_state
        
        # Test unified loading with saved state
        unified_loaded_state = simulate_unified_loading("town_caledon", loaded_state_old)
        print(f"✓ Unified loading completed")
        print(f"  - NPCs found: {len(unified_loaded_state.npcs)}")
        
        # Verify unified loading preserves NPCs
        assert len(unified_loaded_state.npcs) >= len(fresh_game_state.npcs), \
            f"Unified loading lost NPCs: expected >= {len(fresh_game_state.npcs)}, got {len(unified_loaded_state.npcs)}"
        
        # Test 4: Map Transition Scenario
        print("\n=== Test 4: Map Transition Scenario ===")
        
        # Simulate transitioning to a different level and back
        if Path("src/levels/RemiMap").exists():
            # Load RemiMap
            remi_layout = level_repository.load_level("RemiMap")
            remi_state = build_level_use_case.execute(remi_layout, "RemiMap")
            print(f"✓ RemiMap loaded with {len(remi_state.npcs)} NPCs")
            
            # Simulate transition back to town_caledon with preserved player
            transition_state = simulate_unified_loading("town_caledon", GameStateData(
                player=fresh_game_state.player,
                monsters={},
                items={},
                npcs={},
                current_level_id="town_caledon",
                collision_map=[],
                level_tiles=[]
            ))
            
            print(f"✓ Transition back to town_caledon completed")
            print(f"  - NPCs found: {len(transition_state.npcs)}")
            
            # Verify NPCs are reloaded after transition
            assert len(transition_state.npcs) >= len(fresh_game_state.npcs), \
                f"Map transition lost NPCs: expected >= {len(fresh_game_state.npcs)}, got {len(transition_state.npcs)}"
        
        print("\n=== All Tests Passed! ===")
        print("✓ New game loading works correctly")
        print("✓ Save/load preserves NPC data")
        print("✓ Unified loading approach maintains NPCs")
        print("✓ Map transitions reload NPCs properly")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Clean up temporary directory
        shutil.rmtree(temp_dir)
        print(f"\n✓ Cleaned up temporary directory: {temp_dir}")

if __name__ == "__main__":
    print("Testing NPC loading across all scenarios...")
    success = test_npc_loading_scenarios()
    sys.exit(0 if success else 1)
