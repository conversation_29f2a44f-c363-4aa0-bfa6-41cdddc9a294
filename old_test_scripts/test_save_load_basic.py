#!/usr/bin/env python3
"""
Basic test script for the save/load system implementation (no pygame display).

This script tests the core functionality without requiring a display.
"""

import sys
import os
from pathlib import Path

# Add the src directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.game_core.config import get_config
from src.infrastructure.repositories.json_save_game_repository import JsonSaveGameRepository


def test_config():
    """Test the configuration system."""
    print("Testing configuration...")
    
    config = get_config()
    print(f"✓ Config loaded: {config is not None}")
    print(f"✓ Settings UI config: {config.settings_ui is not None}")
    print(f"✓ Show on startup: {config.settings_ui.show_on_startup}")
    print(f"✓ Default save slot: {config.settings_ui.default_save_slot}")
    print(f"✓ UI scaling factor: {config.ui_scaling.scale_factor}")
    
    # Test all config sections
    assert config.rendering is not None, "Rendering config missing"
    assert config.movement is not None, "Movement config missing"
    assert config.combat is not None, "Combat config missing"
    assert config.audio is not None, "Audio config missing"
    assert config.inventory_ui is not None, "Inventory UI config missing"
    assert config.ui_scaling is not None, "UI scaling config missing"
    assert config.settings_ui is not None, "Settings UI config missing"
    
    print("✓ All config sections present")
    print("Configuration test completed.\n")


def test_save_repository():
    """Test the save repository functionality."""
    print("Testing save repository...")
    
    # Create a save repository
    save_repo = JsonSaveGameRepository()
    print(f"✓ Save repository created: {save_repo is not None}")
    
    # Test listing save slots
    slots = save_repo.list_save_slots()
    print(f"✓ Found {len(slots)} save slots")
    assert len(slots) == 10, f"Expected 10 slots, got {len(slots)}"
    
    # Test slot name generation
    for i in range(1, 11):
        slot_name = save_repo.get_numbered_slot_name(i)
        expected = f"slot_{i}"
        assert slot_name == expected, f"Expected {expected}, got {slot_name}"
    print("✓ Slot name generation works correctly")
    
    # Test invalid slot numbers
    try:
        save_repo.get_numbered_slot_name(0)
        assert False, "Should have raised ValueError for slot 0"
    except ValueError:
        print("✓ Correctly rejects invalid slot number 0")
    
    try:
        save_repo.get_numbered_slot_name(11)
        assert False, "Should have raised ValueError for slot 11"
    except ValueError:
        print("✓ Correctly rejects invalid slot number 11")
    
    # Test getting save info for non-existent slot
    info = save_repo.get_save_info("slot_1")
    print(f"✓ Save info for empty slot: {info is None}")
    
    print("Save repository test completed.\n")


def test_ui_imports():
    """Test that UI controllers can be imported without pygame display."""
    print("Testing UI imports...")
    
    try:
        from src.presentation.ui import (
            SettingsUIController, SaveSlotUIController, HelpUIController, 
            GameState, SettingsData, SaveSlotData, HelpData
        )
        print("✓ All UI controllers imported successfully")
        
        # Test enums
        assert GameState.MENU.value == "menu"
        assert GameState.PLAYING.value == "playing"
        assert GameState.PAUSED.value == "paused"
        print("✓ GameState enum works correctly")
        
        # Test data classes
        settings_data = SettingsData()
        assert settings_data.is_visible == False
        assert settings_data.current_save_slot is None
        assert settings_data.game_state == GameState.MENU
        print("✓ SettingsData dataclass works correctly")
        
        save_slot_data = SaveSlotData()
        assert save_slot_data.is_visible == False
        assert save_slot_data.mode == "load"
        assert save_slot_data.selected_slot is None
        print("✓ SaveSlotData dataclass works correctly")
        
        help_data = HelpData()
        assert help_data.is_visible == False
        print("✓ HelpData dataclass works correctly")
        
    except ImportError as e:
        print(f"✗ Import failed: {e}")
        raise
    
    print("UI imports test completed.\n")


def test_game_engine_imports():
    """Test that GameEngine can import the new UI controllers."""
    print("Testing GameEngine imports...")
    
    try:
        # This will test if the imports work
        from src.presentation.game_engine import GameEngine
        print("✓ GameEngine imports successfully with new UI controllers")
        
        # Check if the GameEngine class has the expected attributes
        # We can't instantiate it without all dependencies, but we can check the class
        import inspect
        members = inspect.getmembers(GameEngine)
        method_names = [name for name, _ in members if inspect.isfunction(getattr(GameEngine, name, None))]
        
        expected_methods = [
            '_handle_escape_key',
            '_handle_new_game', 
            '_handle_load_game_request',
            '_handle_save_slot_selected',
            '_handle_save_slot_cancel',
            '_handle_load_game',
            '_handle_save_game',
            '_handle_exit_game',
            '_handle_help_close'
        ]
        
        for method in expected_methods:
            if method in method_names:
                print(f"✓ GameEngine has method: {method}")
            else:
                print(f"✗ GameEngine missing method: {method}")
                raise AssertionError(f"GameEngine missing method: {method}")
        
    except ImportError as e:
        print(f"✗ GameEngine import failed: {e}")
        raise
    
    print("GameEngine imports test completed.\n")


def test_ui_config():
    """Test UI configuration system."""
    print("Testing UI configuration...")
    
    try:
        from src.editor.ui_config import get_colors, get_font, scale_value, get_spacing
        
        # Test colors
        colors = get_colors()
        print(f"✓ Got {len(colors)} color definitions")
        
        # Check for required colors
        required_colors = [
            'background_panel', 'border', 'text_primary', 'text_secondary',
            'button_primary', 'button_primary_hover', 'button_secondary',
            'button_disabled', 'text_disabled', 'border_accent', 'text_accent'
        ]
        
        for color_name in required_colors:
            if color_name in colors:
                print(f"✓ Color '{color_name}' available")
            else:
                print(f"✗ Color '{color_name}' missing")
                raise AssertionError(f"Required color '{color_name}' not found")
        
        # Test scaling
        scaled = scale_value(100)
        print(f"✓ scale_value(100) = {scaled}")
        
        spacing = get_spacing('normal')
        print(f"✓ get_spacing('normal') = {spacing}")
        
    except ImportError as e:
        print(f"✗ UI config import failed: {e}")
        raise
    
    print("UI configuration test completed.\n")


def main():
    """Run all tests."""
    print("=== Save/Load System Basic Test ===\n")
    
    try:
        test_config()
        test_save_repository()
        test_ui_imports()
        test_game_engine_imports()
        test_ui_config()
        
        print("=== All Basic Tests Completed Successfully ===")
        print("\nThe save/load system implementation appears to be working correctly!")
        print("Key features implemented:")
        print("- ✓ Settings UI with New Game, Load Game, Save Game, Help, Exit")
        print("- ✓ Save slot system with 10 numbered slots")
        print("- ✓ Help panel with game controls")
        print("- ✓ Context-aware escape key handling")
        print("- ✓ Game state tracking (MENU, PLAYING, PAUSED)")
        print("- ✓ Enhanced save repository with metadata")
        print("- ✓ Modern blue-grey UI theme")
        print("- ✓ Proper UI scaling and configuration")
        
        return 0
        
    except Exception as e:
        print(f"✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
