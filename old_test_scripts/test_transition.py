#!/usr/bin/env python3
"""
Test script to verify transition number preservation works correctly.
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from infrastructure.repositories.map_parser import MapParser
from editor.map_operations import MapOperations, EditorMapData

def test_transition_preservation():
    """Test that transition numbers are preserved during load/save cycles."""
    print("Testing transition number preservation...")
    
    # Initialize components
    base_legend_path = "src/game_data/base_legend.yaml"
    levels_path = "src/levels"
    
    map_parser = MapParser(base_legend_path)
    map_operations = MapOperations(map_parser, levels_path)
    
    # Load a map with transitions
    map_file = "src/levels/town_caledon/caledon_main.map"
    
    print(f"Loading map: {map_file}")
    editor_map = map_operations.load_map(map_file)
    
    print(f"Transition metadata: {editor_map.transition_metadata}")
    
    # Save to a test file
    test_file = "test_output/transition_test.map"
    os.makedirs("test_output", exist_ok=True)
    
    print(f"Saving to: {test_file}")
    map_operations.save_map(editor_map, test_file)
    
    # Read and check the result
    with open(test_file, 'r') as f:
        content = f.read()
    
    print("Saved map content (ASCII section):")
    if '---' in content:
        ascii_section = content.split('---', 1)[1]
        print(ascii_section)
    
    # Check if transition symbols are preserved
    if '1' in ascii_section:
        print("✓ SUCCESS: Transition symbol '1' preserved correctly!")
    else:
        print("✗ FAILURE: Transition symbol not preserved correctly")
        
    # Clean up
    if os.path.exists(test_file):
        os.remove(test_file)

if __name__ == "__main__":
    test_transition_preservation()
