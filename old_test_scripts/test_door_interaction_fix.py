#!/usr/bin/env python3
"""Test script to verify the door interaction fix works correctly."""

import unittest
from unittest.mock import Mock
import yaml
import os
import sys

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.application.use_cases import TileInteractionUseCase
from src.infrastructure.repositories.map_parser import MapParser


class TestDoorInteractionFix(unittest.TestCase):
    """Test that door interactions work for both opening and closing."""
    
    def setUp(self):
        """Set up test objects."""
        # Load base legend
        base_legend_path = os.path.join(os.path.dirname(__file__), 'src', 'game_data', 'base_legend.yaml')
        with open(base_legend_path, 'r') as f:
            self.base_legend = yaml.safe_load(f)
        
        # Create mock map parser
        self.mock_map_parser = Mock()
        self.mock_map_parser.base_legend = self.base_legend
        
        # Create mock event bus
        self.mock_event_bus = Mock()
        
        # Create use case
        self.use_case = TileInteractionUseCase(self.mock_event_bus, self.mock_map_parser)
    
    def test_closed_door_detection(self):
        """Test that a closed door is detected as interactive."""
        closed_door_asset = "tile.door.wooden"
        tile_def = self.use_case._get_base_tile_definition_for_interactive_tile(closed_door_asset)
        
        self.assertIsNotNone(tile_def, "Closed door should be detected as interactive")
        self.assertTrue(tile_def.get('properties', {}).get('can_interact', False))
        self.assertEqual(tile_def.get('asset_id'), "tile.door.wooden")
    
    def test_open_door_detection(self):
        """Test that an open door is detected as interactive."""
        open_door_asset = "tile.door.wooden.open"
        tile_def = self.use_case._get_base_tile_definition_for_interactive_tile(open_door_asset)
        
        self.assertIsNotNone(tile_def, "Open door should be detected as interactive")
        self.assertTrue(tile_def.get('properties', {}).get('can_interact', False))
        self.assertEqual(tile_def.get('asset_id'), "tile.door.wooden")  # Should return the base door definition
    
    def test_get_tile_definition_direct(self):
        """Test that _get_tile_definition works for closed doors."""
        closed_door_asset = "tile.door.wooden"
        tile_def = self.use_case._get_tile_definition(closed_door_asset)
        
        self.assertIsNotNone(tile_def, "Direct lookup should work for closed doors")
        self.assertTrue(tile_def.get('properties', {}).get('can_interact', False))
    
    def test_get_tile_definition_open_door_fails(self):
        """Test that _get_tile_definition returns None for open doors (expected behavior)."""
        open_door_asset = "tile.door.wooden.open"
        tile_def = self.use_case._get_tile_definition(open_door_asset)
        
        self.assertIsNone(tile_def, "Direct lookup should fail for open doors - that's why we need the smart method")
    
    def test_base_legend_contains_door(self):
        """Test that the base legend contains the door definition."""
        # Find the door entry in base legend
        door_entry = None
        for symbol, definition in self.base_legend.items():
            if definition.get('asset_id') == 'tile.door.wooden':
                door_entry = definition
                break
        
        self.assertIsNotNone(door_entry, "Base legend should contain door definition")
        self.assertTrue(door_entry.get('properties', {}).get('can_interact', False))
        self.assertEqual(door_entry.get('properties', {}).get('closed_asset_id'), 'tile.door.wooden')
        self.assertEqual(door_entry.get('properties', {}).get('open_asset_id'), 'tile.door.wooden.open')


if __name__ == '__main__':
    print("Testing door interaction fix...")
    unittest.main(verbosity=2)
