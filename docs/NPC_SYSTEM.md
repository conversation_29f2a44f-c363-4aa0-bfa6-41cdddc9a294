# NPC System Documentation

## Overview

The NPC (Non-Player Character) system in Tree<PERSON>ard's Revenge provides a comprehensive framework for creating interactive characters with different behaviors, dialog systems, and store interfaces. NPCs can serve as merchants, guards, innkeepers, and more, each with unique interactions and customizable properties.

## NPC Types

### Store NPCs (Interactive Merchants)

#### Merchant
- **Symbol**: `M` in map files
- **Behavior**: Opens store interface
- **Default Inventory**: Health potions, mana potions, bread, gold coins, gems
- **Dialog**: Trade-focused greetings

#### Armourer
- **Symbol**: `B` in map files  
- **Behavior**: Opens store interface
- **Default Inventory**: Helmets, armor, greaves, shields
- **Dialog**: Protection and defense focused

#### Weaponsmith
- **Symbol**: `W` in map files
- **Behavior**: Opens store interface
- **Default Inventory**: Swords, daggers, bows, axes
- **Dialog**: Combat and weapon quality focused

#### Innkeeper
- **Symbol**: `I` in map files
- **Behavior**: Opens store interface
- **Default Inventory**: Food, drinks, room keys
- **Dialog**: Hospitality focused

### Dialog NPCs (Information Providers)

#### Commoner
- **Symbol**: `c` in map files
- **Behavior**: Shows random dialog
- **Dialog**: Location-specific conversations, local events, gossip

#### Guard
- **Symbol**: `G` in map files
- **Behavior**: Shows random dialog
- **Dialog**: Security-focused, patrol mentions, law enforcement

## Player Interaction

### Controls
- **E Key**: Interact with nearby NPCs (within adjacent tiles, including diagonally)
- **Space/Enter/Click**: Continue through dialog
- **Tab**: Switch between Buy/Sell modes in stores
- **Escape**: Close any NPC interface

### Interaction Types
1. **Dialog NPCs**: Display random dialog text with continue prompt
2. **Store NPCs**: Open store interface with buy/sell functionality

## Map Integration

### Adding NPCs to Maps

NPCs are placed in map files using their designated symbols:

```
##########
#M.....c.#
#........#
#..G..B..#
#........#
#.W....I.#
##########
```

### Map Legend Symbols
- `M` = Merchant
- `B` = Armourer (B for "Blacksmith")
- `W` = Weaponsmith
- `I` = Innkeeper
- `c` = Commoner (lowercase to distinguish from chests)
- `G` = Guard

## Level Configuration System

### Basic Configuration

Create a `level_config.py` file in your level directory to customize NPCs:

```python
# levels/my_town/level_config.py

npc_overrides = {
    # Override inventory for NPC types
    "merchant_inventory": [
        "special_potion", 
        "local_bread", 
        "town_trinket"
    ],
    
    # Override dialog for NPC types
    "merchant_dialog": [
        "Welcome to our town!",
        "I have special local goods!",
        "Trade keeps our community strong!"
    ],
    
    # Specific NPC overrides by position
    "specific_npcs": {
        "merchant_at_5_10": {
            "name": "Special Merchant Bob",
            "dialog": ["I'm the special merchant!"],
            "inventory": {
                "rare_item": 1,
                "health_potion": 20
            }
        }
    }
}
```

### Override Types

#### Type-Based Overrides
Override all NPCs of a specific type in the level:

```python
npc_overrides = {
    # Dialog overrides
    "merchant_dialog": ["Custom merchant dialog"],
    "guard_dialog": ["Custom guard dialog"],
    "commoner_dialog": ["Custom commoner dialog"],
    
    # Inventory overrides (for store NPCs)
    "merchant_inventory": ["item1", "item2", "item3"],
    "armourer_inventory": ["armor1", "armor2"],
    "weaponsmith_inventory": ["weapon1", "weapon2"],
    "innkeeper_inventory": ["food1", "drink1", "room_key"]
}
```

#### Position-Based Overrides
Override specific NPC instances by their map position:

```python
npc_overrides = {
    "specific_npcs": {
        "merchant_at_12_8": {
            "name": "Gate Merchant",
            "dialog": ["Welcome to the city gates!"],
            "inventory": {
                "travel_supplies": 10,
                "city_pass": 5
            }
        },
        "guard_at_0_0": {
            "name": "Captain of the Guard",
            "dialog": [
                "I'm the captain here.",
                "Keep the peace, citizen.",
                "Report any trouble to me directly."
            ]
        }
    }
}
```

## Store System

### Transaction Flow
1. Player interacts with store NPC (E key)
2. Store interface opens showing NPC's inventory
3. Player can switch between Buy/Sell modes (Tab key)
4. Click items to select, confirm transactions
5. Gold and items are exchanged automatically

### Pricing
- **Buy Price**: Item's base value from item definitions
- **Sell Price**: Half of item's base value (minimum 1 gold)

### Default Inventories
Each store NPC type has a default inventory with 5 of each item:

- **Merchant**: `health_potion`, `mana_potion`, `bread`, `gold_coin`, `ruby_gem`
- **Armourer**: `iron_helmet`, `leather_armor`, `iron_greaves`, `wooden_shield`
- **Weaponsmith**: `iron_sword`, `steel_dagger`, `wooden_bow`, `battle_axe`
- **Innkeeper**: `bread`, `ale`, `cheese`, `room_key`, `hot_meal`

## Dialog System

### Random Dialog Selection
Dialog NPCs randomly select from their available dialog options each interaction.

### Default Dialog Examples

**Merchant**:
- "Welcome to my shop! I have the finest goods in the land."
- "Looking for something special? I might have just what you need."
- "Trade is the lifeblood of any town. What can I get for you?"

**Guard**:
- "Keep the peace, citizen."
- "Nothing suspicious to report today."
- "The town is secure under our watch."

**Commoner**:
- "Good day to you, traveler."
- "The weather's been quite pleasant lately."
- "I hope you're enjoying your stay in our town."

## Asset Generation

NPCs use procedurally generated sprites with distinct visual characteristics:

- **Merchants**: Brown clothing with trade bags
- **Armourers**: Gray armor with hammers
- **Weaponsmiths**: Leather aprons with displayed weapons
- **Innkeepers**: White aprons with friendly appearance
- **Commoners**: Simple civilian clothing
- **Guards**: Armor and weapons with official appearance

## Map Editor Integration

### Placing NPCs
1. Open map editor: `./run.sh editor`
2. Select "Entities" category in asset panel
3. Choose desired NPC type
4. Click on map to place
5. NPCs appear with appropriate icons and tooltips

### NPC Icons
Each NPC type has a distinct icon in the map editor:
- Merchant: Bag with coins
- Armourer: Shield with cross
- Weaponsmith: Sword
- Innkeeper: Mug with foam
- Commoner: Simple person silhouette
- Guard: Helmet with plume

## Technical Implementation

### Core Classes
- `NPC`: Core entity class with behavior and inventory management
- `NPCDefinition`: Data class for NPC type definitions
- `CreateNPCUseCase`: Creates NPCs from type definitions
- `InteractWithNPCUseCase`: Handles player-NPC interactions
- `BuyFromNPCUseCase`: Manages purchase transactions
- `SellToNPCUseCase`: Manages sale transactions

### UI Components
- `DialogUIController`: Manages dialog display with fade animations
- `StoreUIController`: Manages store interface with buy/sell modes
- Both integrate with existing UI framework and pause game when active

### Event System
NPCs integrate with the existing event bus system for consistent game state management and audio feedback.

## Best Practices

### Level Design
1. Place merchants near town centers for easy access
2. Position guards at strategic locations (gates, important buildings)
3. Scatter commoners throughout populated areas for atmosphere
4. Group related NPCs (weaponsmith near armourer) for logical shopping

### Dialog Writing
1. Keep dialog concise and character-appropriate
2. Reference local events or level-specific context
3. Provide 3-5 dialog options for variety
4. Match tone to NPC type (formal for guards, friendly for innkeepers)

### Inventory Balance
1. Stock appropriate items for NPC type
2. Consider level progression when setting quantities
3. Include both common and special items
4. Balance prices with player progression

## Troubleshooting

### Common Issues
1. **NPC not appearing**: Check map symbol matches base_legend.yaml
2. **No interaction**: Ensure player is adjacent to NPC, press E key
3. **Store not opening**: Verify NPC has store behavior, not dialog-only
4. **Custom dialog not showing**: Check level_config.py syntax and file location
5. **Inventory override not working**: Verify item IDs exist in item definitions

### Debug Tips
1. Check console for error messages during level loading
2. Verify level_config.py file is in correct level directory
3. Test with default NPCs before adding overrides
4. Use map editor to verify NPC placement
