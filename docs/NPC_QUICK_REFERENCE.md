# NPC System Quick Reference

## Map Symbols
```
M = Merchant      (store)
B = Armourer      (store) 
W = Weaponsmith   (store)
I = Innkeeper     (store)
c = Commoner      (dialog)
G = Guard         (dialog)
```

## Player Controls
```
E     = Interact with nearby NPC
TAB   = Switch Buy/Sell in stores
SPACE = Continue dialog
ESC   = Close NPC interface
```

## Level Config Template

```python
# levels/your_level/level_config.py

npc_overrides = {
    # Type-based dialog overrides
    "merchant_dialog": [
        "Welcome to our shop!",
        "Best prices in town!"
    ],
    
    "guard_dialog": [
        "Stay safe, citizen.",
        "All quiet on my watch."
    ],
    
    "commoner_dialog": [
        "Good day!",
        "Lovely weather today."
    ],
    
    # Type-based inventory overrides
    "merchant_inventory": [
        "health_potion",
        "bread", 
        "special_item"
    ],
    
    "armourer_inventory": [
        "iron_helmet",
        "leather_armor",
        "wooden_shield"
    ],
    
    # Position-specific overrides
    "specific_npcs": {
        "merchant_at_5_10": {
            "name": "Special Merchant",
            "dialog": ["I'm special!"],
            "inventory": {
                "rare_item": 1,
                "health_potion": 10
            }
        }
    }
}
```

## Default Inventories

**Merchant**: health_potion, mana_potion, bread, gold_coin, ruby_gem
**Armourer**: iron_helmet, iron_breastplate, iron_greaves, iron_boots, leather_armor, wooden_shield
**Weaponsmith**: iron_sword, steel_dagger, wooden_bow, battle_axe
**Innkeeper**: bread, ale, cheese, room_key, hot_meal

## Store Pricing
- Buy: Item base value
- Sell: Item base value ÷ 2 (min 1 gold)

## Position Format
Use `npctype_at_X_Y` where X,Y are tile coordinates:
- `merchant_at_5_10` = Merchant at tile (5, 10)
- `guard_at_0_15` = Guard at tile (0, 15)

## Quick Setup Steps
1. Add NPC symbol to your .map file
2. Create level_config.py (optional)
3. Test in game with E key
4. Customize dialog/inventory as needed

## Common Patterns

### Town Setup
```
# Town square with merchant and guard
M.....G
.......
.......
c.....c
```

### Shop District  
```
# Weapon/armor shops together
W..B
....
I..M
```

### Guard Posts
```
# Guards at strategic points
G.....G
.......
...P...  (P = Player spawn)
.......
G.....G
```
