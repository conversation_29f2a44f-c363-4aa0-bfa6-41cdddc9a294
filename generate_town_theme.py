#!/usr/bin/env python3
"""
Generate Town Theme Music

This script creates a classic RPG town theme and saves it as an OGG file.
"""

import numpy as np
import pygame
import sys
from pathlib import Path

def generate_note(frequency, duration, sample_rate=22050, attack=0.05, decay=0.1, sustain=0.7, release=0.3):
    """Generate a note with ADSR envelope."""
    samples = int(duration * sample_rate)
    t = np.linspace(0, duration, samples)
    
    # Basic sine wave
    wave = np.sin(2 * np.pi * frequency * t)
    
    # ADSR Envelope
    attack_samples = int(attack * samples)
    decay_samples = int(decay * samples)
    release_samples = int(release * samples)
    sustain_samples = samples - attack_samples - decay_samples - release_samples
    
    envelope = np.ones(samples)
    
    # Attack
    if attack_samples > 0:
        envelope[:attack_samples] = np.linspace(0, 1, attack_samples)
    
    # Decay
    if decay_samples > 0:
        start = attack_samples
        end = start + decay_samples
        envelope[start:end] = np.linspace(1, sustain, decay_samples)
    
    # Sustain
    if sustain_samples > 0:
        start = attack_samples + decay_samples
        end = start + sustain_samples
        envelope[start:end] = sustain
    
    # Release
    if release_samples > 0:
        start = samples - release_samples
        envelope[start:] = np.linspace(sustain, 0, release_samples)
    
    return wave * envelope

def note_to_frequency(note, octave=4):
    """Convert note name to frequency."""
    notes = {
        'C': 0, 'C#': 1, 'D': 2, 'D#': 3, 'E': 4, 'F': 5,
        'F#': 6, 'G': 7, 'G#': 8, 'A': 9, 'A#': 10, 'B': 11
    }
    
    if note not in notes:
        return 0
    
    # A4 = 440 Hz
    semitones_from_a4 = (octave - 4) * 12 + (notes[note] - notes['A'])
    return 440.0 * (2 ** (semitones_from_a4 / 12))

def generate_chord(notes, octave, duration, sample_rate=22050):
    """Generate a chord from multiple notes."""
    chord_wave = np.zeros(int(duration * sample_rate))
    
    for note in notes:
        freq = note_to_frequency(note, octave)
        if freq > 0:
            note_wave = generate_note(freq, duration, sample_rate)
            chord_wave += note_wave * 0.3  # Reduce volume when mixing
    
    return chord_wave

def generate_town_theme():
    """Generate a peaceful RPG town theme."""
    sample_rate = 22050
    bpm = 100
    beat_duration = 60.0 / bpm
    
    # Chord progression: I-vi-IV-V in C major (C-Am-F-G)
    chords = [
        (['C', 'E', 'G'], 4),    # C major
        (['A', 'C', 'E'], 4),    # A minor
        (['F', 'A', 'C'], 4),    # F major
        (['G', 'B', 'D'], 4),    # G major
    ]
    
    # Melody notes for each chord (simple pentatonic)
    melodies = [
        ['C', 'E', 'G', 'E'],    # Over C major
        ['A', 'C', 'E', 'C'],    # Over A minor
        ['F', 'A', 'C', 'A'],    # Over F major
        ['G', 'B', 'D', 'B'],    # Over G major
    ]
    
    # Generate the theme (32 beats total, 8 beats per chord)
    total_duration = 32 * beat_duration
    samples = int(total_duration * sample_rate)
    music = np.zeros(samples)
    
    current_time = 0
    
    for i, (chord_notes, chord_octave) in enumerate(chords):
        # Bass line (whole notes)
        bass_freq = note_to_frequency(chord_notes[0], chord_octave - 1)
        bass_duration = 8 * beat_duration
        bass_wave = generate_note(bass_freq, bass_duration, sample_rate, 
                                attack=0.02, decay=0.1, sustain=0.6, release=0.4)
        
        # Chord (half notes)
        for j in range(4):  # 4 half notes per chord
            chord_start = current_time + j * 2 * beat_duration
            chord_duration = 2 * beat_duration
            chord_wave = generate_chord(chord_notes, chord_octave, chord_duration, sample_rate)
            
            start_sample = int(chord_start * sample_rate)
            end_sample = start_sample + len(chord_wave)
            if end_sample <= len(music):
                music[start_sample:end_sample] += chord_wave * 0.3
        
        # Melody (quarter notes)
        melody_notes = melodies[i]
        for j, note in enumerate(melody_notes * 2):  # Repeat melody twice per chord
            melody_start = current_time + j * beat_duration
            melody_duration = beat_duration * 0.8  # Slight staccato
            melody_freq = note_to_frequency(note, chord_octave + 1)
            melody_wave = generate_note(melody_freq, melody_duration, sample_rate,
                                      attack=0.01, decay=0.05, sustain=0.8, release=0.2)
            
            start_sample = int(melody_start * sample_rate)
            end_sample = start_sample + len(melody_wave)
            if end_sample <= len(music):
                music[start_sample:end_sample] += melody_wave * 0.4
        
        # Add bass
        start_sample = int(current_time * sample_rate)
        end_sample = start_sample + len(bass_wave)
        if end_sample <= len(music):
            music[start_sample:end_sample] += bass_wave * 0.5
        
        current_time += 8 * beat_duration
    
    # Normalize and add some reverb-like effect
    music = music / np.max(np.abs(music)) * 0.8
    
    # Simple delay/echo effect
    delay_samples = int(0.3 * sample_rate)  # 300ms delay
    delayed = np.zeros_like(music)
    delayed[delay_samples:] = music[:-delay_samples] * 0.3
    music = music + delayed
    
    # Final normalization
    music = music / np.max(np.abs(music)) * 0.9
    
    return music, sample_rate

def save_as_wav(audio_data, sample_rate, filename):
    """Save audio data as WAV file using pygame."""
    # Convert to 16-bit integers
    audio_16bit = (audio_data * 32767).astype(np.int16)
    
    # Create stereo version - ensure C-contiguous
    stereo_audio = np.array([audio_16bit, audio_16bit]).T.copy()
    
    # Initialize pygame mixer
    pygame.mixer.init(frequency=sample_rate, size=-16, channels=2)
    
    # Create sound from array
    sound = pygame.sndarray.make_sound(stereo_audio)
    
    # Save as WAV (pygame doesn't directly support OGG export)
    # We'll save as WAV first
    import wave
    import struct
    
    with wave.open(filename, 'w') as wav_file:
        wav_file.setnchannels(2)  # Stereo
        wav_file.setsampwidth(2)  # 2 bytes per sample (16-bit)
        wav_file.setframerate(sample_rate)
        
        # Write stereo data
        for left, right in stereo_audio:
            wav_file.writeframes(struct.pack('<hh', left, right))

def main():
    """Main function to generate and save the town theme."""
    print("Generating RPG town theme...")
    
    # Generate the music
    music, sample_rate = generate_town_theme()
    
    # Create output directory
    output_dir = Path("assets/audio/music")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Save as WAV file
    output_file = output_dir / "town_theme.wav"
    save_as_wav(music, sample_rate, str(output_file))
    
    print(f"Town theme saved as: {output_file}")
    print("Duration: {:.1f} seconds".format(len(music) / sample_rate))
    
    # Also try to convert to OGG if possible
    try:
        import subprocess
        ogg_file = output_dir / "town_theme.ogg"
        result = subprocess.run(['ffmpeg', '-i', str(output_file), '-acodec', 'libvorbis', str(ogg_file)], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"Also saved as OGG: {ogg_file}")
        else:
            print("FFmpeg not available for OGG conversion, WAV file created.")
    except FileNotFoundError:
        print("FFmpeg not available for OGG conversion, WAV file created.")

if __name__ == "__main__":
    main()
